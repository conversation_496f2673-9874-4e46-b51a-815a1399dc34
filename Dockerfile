# 멀티 스테이지 빌드를 사용하여 최종 이미지 크기 감소
FROM php:8.3-fpm AS builder

# 환경 변수 설정
ENV APP_ENV=local
ENV APP_DEBUG=true
ENV COMPOSER_ALLOW_SUPERUSER=1

# 작업 디렉토리 설정
WORKDIR /var/www/html

# 필수 패키지 설치 및 PHP 확장 설치
RUN apt-get update && apt-get install -y \
    curl \
    libzip-dev \
    zip \
    unzip \
    openssl \
    libssl-dev \
    libcurl4-openssl-dev \
    libicu-dev \
    libpng-dev \
    libwebp-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libmagickwand-dev \
    libmemcached-dev \
    supervisor && \
    apt-get clean && rm -rf /var/lib/apt/lists/* && \
    docker-php-ext-install pdo_mysql bcmath exif intl soap && \
    docker-php-ext-configure zip && \
    docker-php-ext-install zip && \
    curl -L -o /usr/local/bin/pickle https://github.com/FriendsOfPHP/pickle/releases/latest/download/pickle.phar && \
    chmod +x /usr/local/bin/pickle && \
    docker-php-ext-enable opcache && \
    docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp && \
    docker-php-ext-install -j$(nproc) gd && \
    pecl channel-update pecl.php.net && \
    pickle install memcached && \
    docker-php-ext-enable memcached && \
    pickle install redis && \
    docker-php-ext-enable redis

# Composer 설치
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 프로덕션 이미지 생성
FROM php:8.3-fpm

# 환경 변수 설정
#ENV APP_ENV=production
ENV APP_ENV=local
#ENV APP_DEBUG=false
ENV APP_DEBUG=true
ENV COMPOSER_ALLOW_SUPERUSER=1

# 작업 디렉토리 설정
WORKDIR /var/www/html

# builder 스테이지에서 필요한 파일들만 복사
COPY --from=builder /usr/local/lib/php/extensions /usr/local/lib/php/extensions
COPY --from=builder /usr/local/etc/php/conf.d /usr/local/etc/php/conf.d
COPY --from=builder /usr/bin/composer /usr/bin/composer

# 필요한 런타임 패키지만 설치
RUN apt-get update && apt-get install -y \
    git \
    unzip \
    libzip-dev \
    libpng-dev \
    libwebp-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libmemcached-dev \
    supervisor \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# 사용자 및 그룹 설정
ARG user
ARG uid
RUN useradd -G www-data,root -u $uid -d /home/<USER>
RUN mkdir -p /home/<USER>/.composer && \
    chown -R $user:$user /home/<USER>

# 애플리케이션 파일 복사 (실제 프로젝트 파일로 대체 필요)
# COPY --chown=$user:$user ./app /var/www/html

# 권한 설정
# RUN chown -R $user:$user /var/www/html \
#     && chmod -R 755 /var/www/html/storage

# 포트 노출
EXPOSE 9000

# Supervisor 설정 복사
COPY ./conf/supervisor/supervisord.conf /etc/supervisor/supervisord.conf

# Supervisor로 서비스 시작
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/supervisord.conf", "-n"]

# 사용자 전환
USER $user