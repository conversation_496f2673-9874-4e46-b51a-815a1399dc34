name: cornerstone-api

services:
  # PHP API 웹서버
  webserver:
    container_name: cnsprowms-web
    image: nginx:stable-alpine
    expose:
      - 80
      - 443
    environment:
      - TZ=Asia/Seoul
    volumes:
      - ./app:/var/www/html
      - ./conf/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./logs/nginx:/var/log/nginx
    restart: unless-stopped
    depends_on:
      - laravel
    networks:
      - shared_network

  # PHP 이미지
  laravel:
    container_name: cnsprowms-api
    build:
      args:
        user: izen # 유저명 변경 필요( Local: izen, AWS: ec2-user, Oracle: ubuntu )
        uid: 1000 # UID 변경 필요( Local, AWS: 1000, Oracle: 1001 )
      context: .
      dockerfile: Dockerfile
    working_dir: /var/www/html
    volumes:
      - ./app:/var/www/html
      - ./conf/php/php.ini:/usr/local/etc/php/php.ini
      - ./conf/php/www.conf:/usr/local/etc/php-fpm.d/www.conf
      - ./conf/php/opcache.ini:/usr/local/etc/php/conf.d/opcache-recommended.ini
      - ./conf/php/memcached.ini:/usr/local/etc/php/conf.d/memcached-recommended.ini
      - ./logs/supervisor/:/var/log/supervisor
    restart: unless-stopped
    environment:
      - TZ=Asia/Seoul
      - MEILISEARCH_HOST=http://meilisearch:7700
      - MEILISEARCH_KEY=P@ssw0rd-1234
    networks:
      - shared_network

networks:
  shared_network:
    external: true
