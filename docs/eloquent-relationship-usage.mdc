# Eloquent Relationship Usage Guide

## 🚨 일반적인 오류와 해결방법

### 1. 관계 메서드 호출 오류

#### ❌ 잘못된 사용법
```php
$product = $palletProduct->product();  // 관계 객체 반환
$productId = $product->id;  // 오류: BelongsTo 객체의 id 속성에 접근
```

#### ✅ 올바른 사용법
```php
$product = $palletProduct->product;  // 실제 모델 인스턴스 반환
$productId = $product->id;  // 정상: Product 모델의 id 속성에 접근
```

### 2. 관계 로딩 최적화

#### ❌ N+1 문제 발생
```php
foreach ($palletProducts as $palletProduct) {
    $product = $palletProduct->product;  // 각 반복마다 쿼리 실행
}
```

#### ✅ Eager Loading 사용
```php
$palletProducts = PalletProduct::with('product')->get();
foreach ($palletProducts as $palletProduct) {
    $product = $palletProduct->product;  // 이미 로드된 관계 사용
}
```

### 3. 관계 존재 여부 확인

#### ❌ 잘못된 확인 방법
```php
if ($palletProduct->product()) {  // 항상 true 반환
    // 관계가 존재하지 않아도 실행됨
}
```

#### ✅ 올바른 확인 방법
```php
if ($palletProduct->product) {  // 관계가 로드되었는지 확인
    // 관계가 존재할 때만 실행
}

// 또는 더 명확하게
if ($palletProduct->product()->exists()) {  // 데이터베이스에서 확인
    // 관계가 실제로 존재할 때만 실행
}
```

## 🔧 모범 사례

### 1. 관계 정의
```php
// PalletProduct 모델
public function product()
{
    return $this->belongsTo(Product::class);
}
```

### 2. 관계 사용
```php
// 단일 관계 접근
$product = $palletProduct->product;

// 관계가 없는 경우 처리
if ($product) {
    $productId = $product->id;
} else {
    // 관계가 없는 경우 처리
    throw new Exception('상품 정보를 찾을 수 없습니다.');
}

// 안전한 접근 (PHP 8+)
$productId = $palletProduct->product?->id;
```

### 3. 성능 최적화
```php
// 필요한 관계만 로드
$palletProducts = PalletProduct::with(['product:id,name,req_id'])->get();

// 조건부 관계 로딩
$palletProducts = PalletProduct::with(['product' => function($query) {
    $query->where('status', 'active');
}])->get();
```

## 🚨 주의사항

1. **메서드 호출 vs 속성 접근**
   - `$model->relation()`: 관계 객체 반환 (BelongsTo, HasMany 등)
   - `$model->relation`: 실제 모델 인스턴스 반환

2. **Null 안전성**
   - 관계가 존재하지 않을 수 있음을 항상 고려
   - Null coalescing operator (`??`) 사용 권장

3. **성능 고려사항**
   - 반복문에서 관계 접근 시 N+1 문제 주의
   - Eager loading으로 미리 로드

## 📋 체크리스트

- [ ] 관계 접근 시 `()` 대신 속성으로 접근
- [ ] 관계 존재 여부 확인 로직 포함
- [ ] Eager loading으로 성능 최적화
- [ ] Null 안전성 고려
- [ ] 적절한 예외 처리 구현
description:
globs:
alwaysApply: false
---
