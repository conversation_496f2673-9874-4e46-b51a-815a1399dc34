# UTF-8 인코딩 문제 방지 규칙

## 문제 상황
- **GuzzleHttp\Exception\InvalidArgumentException**: `json_encode error: Malformed UTF-8 characters, possibly incorrectly encoded`
- Slack 알림 전송 시 UTF-8 인코딩 오류 발생
- 엑셀 파일에서 읽어온 데이터에 잘못된 UTF-8 문자가 포함된 경우

## 원인 분석
1. **엑셀 파일 인코딩 문제**: 엑셀 파일이 UTF-8이 아닌 인코딩으로 저장된 경우
2. **제어 문자 포함**: 엑셀에서 복사/붙여넣기 시 제어 문자가 포함되는 경우
3. **특수 문자 처리**: 한글, 이모지, 특수 기호 등이 올바르게 처리되지 않는 경우

## 해결 방법

### 1. 데이터 정리 함수 구현
```php
/**
 * UTF-8 문자열 정리
 */
private static function sanitizeUtf8(string $string): string
{
    // UTF-8이 아닌 문자 제거 또는 변환
    $string = mb_convert_encoding($string, 'UTF-8', 'UTF-8');
    
    // 잘못된 UTF-8 시퀀스 제거
    $string = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $string);
    
    // 제어 문자 제거
    $string = preg_replace('/[\x{200B}-\x{200D}\x{FEFF}]/u', '', $string);
    
    return $string;
}

/**
 * 배열 내 모든 문자열을 UTF-8로 정리
 */
private static function sanitizeArray(array $array): array
{
    $result = [];
    
    foreach ($array as $key => $value) {
        if (is_string($value)) {
            $result[$key] = self::sanitizeUtf8($value);
        } elseif (is_array($value)) {
            $result[$key] = self::sanitizeArray($value);
        } else {
            $result[$key] = $value;
        }
    }
    
    return $result;
}
```

### 2. 로깅 서비스에서 UTF-8 정리
- `SimpleLogService`에서 Slack 알림 전송 전에 모든 데이터를 UTF-8로 정리
- 에러 발생 시 Slack 전송 실패를 catch하여 파일 로그에만 기록

### 3. 엑셀 임포트에서 UTF-8 정리
- `ProductsImport`에서 엑셀 데이터를 읽을 때 UTF-8 정리
- `removeCarriageReturn` 메서드에서 UTF-8 인코딩 정리 추가
- **행 번호 표시 기능 추가**: `isEmpty` 메서드에서 에러 발생 시 몇 번째 행인지 표시

### 4. Slack 알림에서 UTF-8 정리
- `SlackNotification`에서 메시지 구성 시 UTF-8 정리
- 사용자 이름, 오류 메시지 등 모든 문자열 데이터 정리

## 적용된 파일들
1. `app/Services/SimpleLogService.php`
2. `app/Imports/ProductsImport.php`
3. `app/Notifications/SlackNotification.php`

## 주요 개선사항

### 행 번호 표시 기능
- `isEmpty` 메서드에 행 번호 매개변수 추가
- 에러 메시지에 "행 번호: X" 정보 포함
- 로그 컨텍스트에 `row_number` 필드 추가
- 엑셀 파일에서 문제가 있는 정확한 위치 파악 가능

### 예시 메시지
```
엑셀 파일의 [QAID] 값이 비어 있습니다 (행 번호: 15). 
필터를 이용해 비어 있는 부분을 찾아 값을 채우거나 행을 제거해 주세요.
```

## 예방 규칙
1. **엑셀 파일 업로드 전 검증**: UTF-8 인코딩으로 저장된 파일만 허용
2. **데이터 정리**: 모든 외부 데이터는 UTF-8로 정리 후 처리
3. **에러 처리**: Slack 알림 실패 시 파일 로그에만 기록하도록 처리
4. **정기 검사**: 로그에서 UTF-8 인코딩 오류 패턴 모니터링
5. **행 번호 추적**: 에러 발생 시 정확한 행 번호 기록

## 테스트 방법
1. 다양한 인코딩의 엑셀 파일로 테스트
2. 특수 문자, 이모지가 포함된 데이터로 테스트
3. 제어 문자가 포함된 데이터로 테스트
4. Slack 알림 전송 성공 여부 확인
5. 행 번호가 올바르게 표시되는지 확인

## 모니터링
- UTF-8 인코딩 오류 발생 시 즉시 알림
- 정기적인 로그 분석으로 패턴 파악
- 사용자 피드백을 통한 추가 문제점 발견
- 행 번호 정보를 통한 정확한 문제 위치 파악
description:
globs:
alwaysApply: false
---
