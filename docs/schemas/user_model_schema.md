# 인증 시스템 구현 - 사용자 모델 스키마

## 개요
본 문서는 인증 시스템 구현에 사용되는 사용자(User) 모델과 관련 스키마를 설명합니다. 사용자 모델은 시스템의 인증과 권한 관리를 담당하며, 회사(Company) 모델과 연관되어 있습니다.

## 데이터베이스 구조

### 1. 회사 테이블 (companies)

#### 마이그레이션 정보
- 파일: `database/migrations/2014_10_01_000000_create_companies_table.php`
- 테이블명: `companies`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 회사 고유 식별자 | 기본키, 자동 증가 |
| code | string | 거래코드 | Nullable |
| name | string | 소속사 이름(영어) | Unique |
| kr_name | string | 소속사 이름(한글) | - |
| logo_uri | string | 로고위치, 상대경로 | Nullable |
| type | unsignedTinyInteger | 타입 | 기본값: 0 |
| f_default | string | 기본값 여부 | 기본값: 'N' |
| description | text | 회사 설명 | Nullable |
| status | unsignedTinyInteger | 상태 | 기본값: 0 |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 고유 인덱스: name
- 일반 인덱스: status

### 2. 사용자 테이블 (users)

#### 마이그레이션 정보
- 파일: `database/migrations/2014_10_12_000000_create_users_table.php`
- 테이블명: `users`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 사용자 고유 식별자 | 기본키, 자동 증가 |
| mem_no | unsignedInteger | 이전 멤버 번호 | 기본값: 0 |
| company_id | foreignId | 소속 회사 인덱스 | 외래키(companies.id), Nullable |
| role | string | 사용자 권한 | 기본값: 'Guest' |
| username | string | 사용자 아이디 | Unique |
| caps_id | string | 캡스용 사용자 ID(바코드용) | Unique, Nullable |
| name | string | 사용자 이름 | - |
| part | enum | 소속 부서 | 값: IT, 경영관리, 물류, 생산관리, 일반가전 |
| position | enum | 직위 | 값: 대표, 이사, 공장장, 과장, 팀장, 반장, 팀원 |
| email | string | 메일 주소 | Nullable |
| email_verified_at | dateTime | 이메일 인증 시간 | Nullable |
| cellphone | string | 연락처(휴대폰) | Nullable |
| telephone | string | 연락처(기타) | Nullable |
| status | unsignedTinyInteger | 사용자 상태 | 기본값: 0 |
| menu | json | 사용자 접근 가능 메뉴 | Nullable |
| login_at | dateTime | 마지막 로그인 시간 | Nullable |
| login_ip | string | 로그인 IP | Nullable |
| login_os | string | 로그인 OS | Nullable |
| password | string | 암호화된 비밀번호 | - |
| remember_token | string | 자동 로그인 토큰 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |
| deleted_at | timestamp | 삭제 시간 | Nullable (소프트 삭제) |

#### 인덱스 정보
- 고유 인덱스: username, caps_id
- 일반 인덱스: mem_no, name, email, cellphone, status

### 3. 사용자 출퇴근 테이블 (user_attendances)

#### 마이그레이션 정보
- 파일: `database/migrations/2025_04_27_000000_create_user_attendances_table.php`
- 테이블명: `user_attendances`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 고유 식별자 | 기본키, 자동 증가 |
| user_id | foreignId | 사용자 인덱스 | 외래키(users.id) |
| work_date | date | 근무일자 | - |
| day_type | enum | 근무일 명칭 | 값: 평일, 휴일, 기본값: 평일 |
| clock_in | time | 출근 시간 | Nullable |
| clock_out | time | 퇴근 시간 | Nullable |
| is_late | boolean | 지각 여부 | 기본값: false |
| late_hours | time | 지각 시간 | 기본값: '00:00:00' |
| is_early_leave | boolean | 조퇴 여부 | 기본값: false |
| early_leave_hours | time | 조퇴 시간 | 기본값: '00:00:00' |
| regular_hours | time | 기본 근무 시간 | 기본값: '00:00:00' |
| overtime_hours | time | 연장 근무 시간 | 기본값: '00:00:00' |
| total_hours | time | 총 근무 시간 | 기본값: '00:00:00' |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 고유 인덱스: [user_id, work_date]
- 일반 인덱스: work_date, day_type

## 모델 구현

### 1. Company 모델
파일: `app/Models/Company.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- HasFactory 트레이트 사용

#### 필드 속성
- fillable: 대량 할당 가능 필드 (code, name, kr_name 등)

#### 관계 메소드
- users(): 사용자와의 관계 (hasMany)

### 2. User 모델
파일: `app/Models/User.php`

#### 주요 특징
- Laravel의 Authenticatable을 상속
- HasApiTokens, HasFactory, Notifiable, SoftDeletes 트레이트 사용
- 사용자 역할 및 상태 관련 상수 정의

#### 역할 상수
```php
const ROLE_SUPER_ADMIN = 'Super-Admin';
const ROLE_ADMIN = 'Admin';
const ROLE_RECEIVING_MANAGER = 'Receiving-Manager';
const ROLE_PALLET_MANAGER = 'Pallet-Manager';
const ROLE_CARRYOUT_MANAGER = 'Carryout-Manager';
const ROLE_EMPLOYEE = 'Employee';
const ROLE_GUEST = 'Guest';
```

#### 상태 상수
```php
const MEMBER_STATUS_ACTIVE = 1;
const MEMBER_STATUS_PAUSE = 2;
const MEMBER_STATUS_UNAVAILABLE = 8;
const MEMBER_STATUS_DELETED = 9;
```

#### 필드 속성
- fillable: 대량 할당 가능 필드
- hidden: API 응답 시 숨김 필드 (password, remember_token)
- casts: 타입 캐스팅 (email_verified_at, login_at, password)

#### 관계 메소드
- company(): 회사와의 관계 (belongsTo)
- attendances(): 출퇴근 기록과의 관계 (hasMany)
- 그 외 다양한 모델과의 관계 메소드 (registerReqs, checkedReqs, registeredProducts 등)

### 3. UserAttendance 모델
파일: `app/Models/UserAttendance.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- HasFactory 트레이트 사용
- 근무일 타입 관련 상수 정의

#### 근무일 타입 상수
```php
const DAY_TYPE_WORKDAY = '평일';
const DAY_TYPE_HOLIDAY = '휴일';
```

#### 필드 속성
- fillable: 대량 할당 가능 필드
- casts: 타입 캐스팅 (날짜, 시간, 논리값 필드)

#### 관계 메소드
- user(): 사용자와의 관계 (belongsTo)

## 인증 시스템 구현 시 고려사항

1. **권한 관리**
   - 다양한 역할(Super-Admin, Admin, Manager, Employee, Guest)에 따른 권한 설정
   - menu JSON 필드를 통한 접근 가능 메뉴 관리

2. **사용자 상태 관리**
   - 활성, 일시 정지, 이용불가, 삭제 등의 상태 관리
   - SoftDeletes를 이용한 논리적 삭제 구현

3. **회사 연동**
   - 사용자는 특정 회사에 소속됨
   - company_id 외래키를 통한 관계 설정

4. **출퇴근 기록 관리**
   - 사용자별 출퇴근 기록 관리
   - 지각, 조퇴, 근무 시간 계산 등 기능 구현

5. **보안 고려사항**
   - 패스워드 해싱
   - 로그인 정보(IP, OS, 시간) 기록
   - 사용자 식별을 위한 고유 인덱스(username, caps_id) 설정 