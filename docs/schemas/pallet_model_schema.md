# 팔레트 관리 기능 - 스키마 설계

## 개요
본 문서는 팔레트 관리 기능의 핵심 모델인 팔레트(Pallet) 모델 및 관련 테이블의 스키마 구조를 설명합니다. 팔레트 관리는 상품의 적재, 출고, 마감 등의 물류 과정을 관리하는 기능입니다.

## 데이터베이스 구조

### 1. 팔레트(Pallets) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_003101_create_pallets_table.php`
- 테이블명: `pallets`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 팔레트 고유 식별자 | 기본키, 자동 증가 |
| location_id | foreignId | 팔레트 적재 위치 인덱스 | 외래키(locations.id) |
| repair_grade_id | foreignId | 팔레트 등급 | 외래키(repair_grades.id), Nullable |
| status | unsignedTinyInteger | 상태 | 기본값: 10 |
| registered_at | dateTime | 팔레트 등록 시간 | Nullable |
| registered_user_id | unsignedBigInteger | 팔레트 등록 직원 인덱스 | 외래키(users.id), Nullable |
| checked_at | dateTime | 팔레트 출고전 점검 시간 | Nullable |
| checked_user_id | unsignedBigInteger | 팔레트 출고전 검수 직원 인덱스 | 외래키(users.id), Nullable |
| exported_at | dateTime | 팔레트 출고 시간 | Nullable |
| exported_user_id | unsignedBigInteger | 팔레트 출고 직원 인덱스 | 외래키(users.id), Nullable |
| memo | text | 메모 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 일반 인덱스: location_id, status, registered_at, checked_at

### 2. 팔레트 상품(Pallet Products) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_003102_create_pallet_products_table.php`
- 테이블명: `pallet_products`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 고유 식별자 | 기본키, 자동 증가 |
| pallet_id | foreignId | 팔레트 인덱스 | 외래키(pallets.id) |
| product_id | foreignId | 상품 인덱스 | 외래키(products.id) |
| repair_product_id | foreignId | 수리된 상품 인덱스 | 외래키(repair_products.id), Nullable |
| status | unsignedTinyInteger | 상태 | 기본값: 10 |
| registered_user_id | unsignedBigInteger | 검수한 회원 인덱스 | 외래키(users.id), Nullable |
| registered_at | dateTime | 등록 일시 | Nullable |
| quantity | unsignedMediumInteger | 검수 수량 | 기본값: 0 |
| amount | unsignedInteger | 판매 단가(상품 등록값과 동일) | 기본값: 0 |
| process_check_id | unsignedBigInteger | 진단 내용(상품 상태) 번호 | Nullable |
| process_repair_id | unsignedBigInteger | 처리 내용(수리 내역) 번호 | Nullable |
| process_grade_id | unsignedBigInteger | 분류 등급(상품 등급) 번호 | Nullable |
| repair_symptom_id | foreignId | 증상 내용(상품 상태) 인덱스 | 외래키(repair_symptoms.id), Nullable |
| repair_process_id | foreignId | 처리 내용(수리 내역) 인덱스 | 외래키(repair_processes.id), Nullable |
| repair_grade_id | foreignId | 수리 등급 인덱스 | 외래키(repair_grades.id), Nullable |
| invoice1 | unsignedInteger | 청구금액1(기본) | 기본값: 0 |
| invoice2 | unsignedInteger | 청구금액2(추가) | 기본값: 0 |
| invoice3 | unsignedInteger | 청구금액3(추가) | 기본값: 0 |
| checked_user_id | unsignedBigInteger | 최종 검수 회원 인덱스 | 외래키(users.id), Nullable |
| checked_status | unsignedTinyInteger | 최종 상태 | 기본값: 10 |
| checked_at | dateTime | 최종 검수 일시 | Nullable |
| memo | text | 메모 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |
| deleted_at | dateTime | 삭제 일시 | Nullable |

#### 인덱스 정보
- 일반 인덱스: pallet_id, product_id, status, registered_at, repair_symptom_id, repair_process_id, repair_grade_id

## 모델 구현

### 1. Pallet 모델
파일: `app/Models/Pallet.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- HasFactory 트레이트 사용
- 팔레트 상태 관련 상수 정의

#### 팔레트 상태 상수
```php
const STATUS_REGISTERED = 10;
const STATUS_LOADED = 20;
const STATUS_CLOSED = 30;
const STATUS_EXPORTED = 40;
const STATUS_DELETED = 90;
```

#### 필드 속성
- fillable: 대량 할당 가능 필드
- casts: 타입 캐스팅 (registered_at, checked_at, exported_at)
- with: 기본으로 로드되는 관계 모델

#### 관계 메소드
- location(): 위치와의 관계 (belongsTo)
- repairGrade(): 수리 등급과의 관계 (belongsTo)
- registeredUser(): 등록 사용자와의 관계 (belongsTo)
- checkedUser(): 검수 사용자와의 관계 (belongsTo)
- exportedUser(): 출고 사용자와의 관계 (belongsTo)
- palletProducts(): 팔레트 상품과의 관계 (hasMany)
- deleteLog(): 삭제 로그와의 관계 (morphOne)

### 2. PalletProduct 모델

#### 주요 특징
- Laravel의 Model 클래스 상속
- 팔레트 상품의 상태 및 속성 관리
- 수리 정보(증상, 처리, 등급) 연결

#### 관계 메소드
- pallet(): 팔레트와의 관계
- product(): 제품과의 관계
- repairProduct(): 수리된 제품과의 관계
- repairSymptom(): 수리 증상과의 관계
- repairProcess(): 수리 과정과의 관계
- repairGrade(): 수리 등급과의 관계
- registeredUser(): 등록 사용자와의 관계
- checkedUser(): 검수 사용자와의 관계

## 팔레트 관리 기능 구현 시 고려사항

1. **팔레트 상태 관리**
   - 등록, 적재중, 적재마감(출고대기), 출고완료, 삭제 상태 관리
   - 상태 변경에 따른 처리 로직 구현

2. **팔레트 위치 관리**
   - 팔레트의 물리적 위치 코드 관리
   - 위치 변경 추적

3. **팔레트 등급 관리**
   - 수리 등급에 따른 팔레트 분류
   - 등급별 처리 로직

4. **팔레트-제품 연결 관리**
   - 팔레트에 제품 적재 기능
   - 적재 상태 및 이력 추적

5. **출고 프로세스**
   - 출고 전 검수 처리
   - 출고 처리 및 기록
   - 출고 취소 기능

6. **마감 프로세스**
   - 팔레트 마감 처리
   - 마감 후 추가 적재 방지
   - 마감 취소 기능

7. **송장 및 비용 관리**
   - 청구 금액 관리
   - 송장 정보 확인 