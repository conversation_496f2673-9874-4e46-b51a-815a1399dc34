# 반출(Carryout) 관리 기능 - 스키마 설계

## 개요
본 문서는 반출(Carryout) 관리 기능의 핵심 모델인 반출(Carryout) 모델 및 관련 테이블의 스키마 구조를 설명합니다. 반출 관리는 외부 업체로 제품을 보내고 반입하는 과정을 관리하는 기능입니다.

## 데이터베이스 구조

### 1. 반출(Carryouts) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_004101_create_carryouts_table.php`
- 테이블명: `carryouts`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 반출 고유 식별자 | 기본키, 자동 증가 |
| status | unsignedTinyInteger | 외주 상태 | 기본값: 10 |
| carryout_user_id | foreignId | 반출 담당 직원 | 외래키(users.id), Nullable |
| carryout_at | date | 반출 날짜 | Nullable |
| carryin_user_id | foreignId | 반입 담당 직원 | 외래키(users.id), Nullable |
| carryin_at | dateTime | 반입 시간 | Nullable |
| token_id | foreignId | 외부업체 접속 토큰 인덱스 | 외래키(carryout_tokens.id) |
| memo | text | 메모 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 일반 인덱스: status, carryout_user_id, carryout_at, carryin_user_id, carryin_at, created_at

### 2. 반출 토큰(Carryout Tokens) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_004100_create_carryout_tokens_table.php`
- 테이블명: `carryout_tokens`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 토큰 고유 식별자 | 기본키, 자동 증가 |
| token | string | 외부업체 접속 토큰 | - |

#### 인덱스 정보
- 일반 인덱스: token

### 3. 반출 상품(Carryout Products) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_004102_create_carryout_products_table.php`
- 테이블명: `carryout_products`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 고유 식별자 | 기본키, 자동 증가 |
| carryout_id | foreignId | 반출 인덱스 | 외래키(carryouts.id) |
| req_id | foreignId | 점검 요청서 인덱스 | 외래키(reqs.id) |
| product_id | foreignId | 상품 인덱스 | 외래키(products.id), Nullable |
| status | unsignedTinyInteger | 상태 | 기본값: 10 |
| checked_user_id | foreignId | 반출 담당 직원 | 외래키(users.id), Nullable |
| checked_at | dateTime | 반출 시간 | Nullable |
| process_check_id | unsignedBigInteger | 진단 내용(상품 상태) 번호 | Nullable |
| process_repair_id | unsignedBigInteger | 처리 내용(수리 내역) 번호 | Nullable |
| process_grade_id | unsignedBigInteger | 분류 등급(상품 등급) 번호 | Nullable |
| repair_symptom_id | foreignId | 증상 내용(상품 상태) 인덱스 | 외래키(repair_symptoms.id), Nullable |
| repair_process_id | foreignId | 처리 내용(수리 내역) 인덱스 | 외래키(repair_processes.id), Nullable |
| repair_grade_id | foreignId | 수리 등급 인덱스 | 외래키(repair_grades.id), Nullable |
| invoice2 | unsignedInteger | 청구금액2(추가) | 기본값: 0 |
| token_id | foreignId | 외부업체 접속 토큰 인덱스 | 외래키(carryout_tokens.id), Nullable |
| renovator_id | foreignId | 외부업체 접속 토큰 인덱스 | Nullable |
| renovate_at | dateTime | 수리 완료 시간 | Nullable |
| carryin_user_id | foreignId | 반입 담당 직원 | 외래키(users.id), Nullable |
| carryin_at | dateTime | 반입 시간 | Nullable |
| memo | text | 메모 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 일반 인덱스: carryout_id, req_id, product_id, status, checked_user_id, checked_at, token_id, renovate_at, carryin_user_id, carryin_at

## 모델 구현

### 1. Carryout 모델
파일: `app/Models/Carryout.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- HasFactory 트레이트 사용
- 반출 상태 관련 상수 정의

#### 반출 상태 상수
```php
const STATUS_CARRIED_OUT = 10;
const STATUS_CARRIED_IN = 30;
const STATUS_CANCELED = 90;
```

#### 필드 속성
- fillable: 대량 할당 가능 필드
- casts: 타입 캐스팅 (carryout_at, carryin_at)
- with: 기본으로 로드되는 관계 모델(carryoutUser, carryinUser, token)

#### 관계 메소드
- carryoutUser(): 반출 담당자와의 관계 (belongsTo)
- carryinUser(): 반입 담당자와의 관계 (belongsTo)
- token(): 토큰과의 관계 (belongsTo)
- carryoutProducts(): 반출 상품과의 관계 (hasMany)
- deleteLog(): 삭제 로그와의 관계 (morphOne)

### 2. CarryoutToken 모델
파일: `app/Models/CarryoutToken.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- HasFactory 트레이트 사용
- 타임스탬프 비활성화

#### 필드 속성
- fillable: 대량 할당 가능 필드 (token)

#### 관계 메소드
- carryouts(): 반출과의 관계 (hasMany)
- carryoutProducts(): 반출 상품과의 관계 (hasMany)

### 3. CarryoutProduct 모델
파일: `app/Models/CarryoutProduct.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- HasFactory 트레이트 사용
- 반출 상품 상태 관련 상수 정의

#### 반출 상품 상태 상수
```php
const STATUS_ONBOARD = 10;
const STATUS_RENOVATED = 20;
const STATUS_CANCELED = 90;
```

#### 필드 속성
- fillable: 대량 할당 가능 필드
- casts: 타입 캐스팅 (checked_at, carryin_at, renovate_at)
- with: 기본으로 로드되는 관계 모델

#### 관계 메소드
- carryout(): 반출과의 관계 (belongsTo)
- req(): 요청과의 관계 (belongsTo)
- token(): 토큰과의 관계 (belongsTo)
- product(): 제품과의 관계 (belongsTo)
- checkedUser(): 검수자와의 관계 (belongsTo)
- carryinUser(): 반입 담당자와의 관계 (belongsTo)
- repairSymptom(): 수리 증상과의 관계 (belongsTo)
- repairProcess(): 수리 과정과의 관계 (belongsTo)
- repairGrade(): 수리 등급과의 관계 (belongsTo)
- deleteLog(): 삭제 로그와의 관계 (morphOne)

## 반출 관리 기능 구현 시 고려사항

1. **반출 상태 관리**
   - 반출, 반입, 취소 상태 관리
   - 상태 변경에 따른 처리 로직 구현

2. **외부 업체 접근 토큰 관리**
   - 외부 업체 접근을 위한 토큰 생성 및 관리
   - 토큰 기반 인증 및 권한 설정

3. **반출 상품 상태 관리**
   - 수리대기, 수리완료, 취소 상태 관리
   - 상태 변경 추적 및 이력 관리

4. **반출 및 반입 프로세스**
   - 반출 처리 및 담당자 지정
   - 반입 처리 및 담당자 지정
   - 반출/반입 일자 관리

5. **수리 정보 관리**
   - 증상, 처리 내용, 등급 정보 관리
   - 수리 완료 시간 추적

6. **비용 관리**
   - 추가 청구 금액(invoice2) 관리
   - 비용 정산 및 보고 기능 