# 입고 목록 관리 기능 - 스키마 설계

## 개요
본 문서는 입고 목록 관리 기능의 핵심 모델인 `입고요청(Req)` 모델 및 관련 테이블의 스키마 구조를 설명합니다. 입고 목록은 시스템에 들어오는 상품들의 요청서 정보를 관리하며, 상품의 개수와 상태를 추적합니다.

## 데이터베이스 구조

### 1. 요청(Reqs) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_001101_create_reqs_table.php`
- 테이블명: `reqs`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 요청 고유 식별자 | 기본키, 자동 증가 |
| req_at | date | 요청일 | - |
| req_type | unsignedTinyInteger | 요청타입 | 기본값: 9 |
| status | unsignedTinyInteger | 등록상태 | 기본값: 1 |
| user_id | foreignId | 등록한 직원 인덱스 | 외래키(users.id), Nullable |
| checked_user_id | foreignId | 검수한 직원 인덱스 | 외래키(users.id), Nullable |
| checked_at | dateTime | 검수일 | Nullable |
| memo | text | 메모 | Nullable |
| total_count | unsignedBigInteger | 총 상품 개수 | 기본값: 0 |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 일반 인덱스: req_at, req_type, user_id, checked_user_id, created_at

### 2. 요청 개수(ReqCounts) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_001102_create_req_counts_table.php`
- 테이블명: `req_counts`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 고유 식별자 | 기본키, 자동 증가 |
| req_id | foreignId | 요청서 인덱스 | 외래키(reqs.id) |
| undelivered | unsignedBigInteger | 미입고 개수 | 기본값: 0 |
| unchecked | unsignedBigInteger | 입고(검수)대기 개수 | 기본값: 0 |
| checked | unsignedBigInteger | 점검대기 개수 | 기본값: 0 |
| carryout | unsignedBigInteger | 외주반출 수리중 개수 | 기본값: 0 |
| waiting | unsignedBigInteger | 수리대기(구성품 신청) 개수 | 기본값: 0 |
| repaired | unsignedBigInteger | 수리/점검완료(창고) 개수 | 기본값: 0 |
| checkout | unsignedBigInteger | 적재중(점검완료) 개수 | 기본값: 0 |
| exporting | unsignedBigInteger | 출고대기(마감) 개수 | 기본값: 0 |
| duplicated | unsignedBigInteger | 중복등록 개수 | 기본값: 0 |
| unlinked | unsignedBigInteger | 미등록 개수 | 기본값: 0 |
| completed | unsignedBigInteger | 출고완료 개수 | 기본값: 0 |
| deleted | unsignedBigInteger | 보류(삭제) 개수 | 기본값: 0 |

#### 인덱스 정보
- 일반 인덱스: req_id

### 3. 제품(Products) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_002105_create_products_table.php`
- 테이블명: `products`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 고유 식별자 | 기본키, 자동 증가 |
| req_id | foreignId | 점검 요청서 인덱스 | 외래키(reqs.id) |
| qaid | string | 품질관리번호 | - |
| barcode | string | 바코드 | - |
| name | string | 상품명 | - |
| cate4_id | foreignId | 카테고리 인덱스 | 외래키(cate4.id), Nullable |
| cate5_id | foreignId | 카테고리 인덱스 | 외래키(cate5.id), Nullable |
| quantity | unsignedMediumInteger | 상품 개수 | 기본값: 1 |
| amount | unsignedInteger | 판매단가 | 기본값: 0 |
| user_id | foreignId | 등록 직원 인덱스 | 외래키(users.id), Nullable |
| status | unsignedTinyInteger | 상품 처리 상태 | 기본값: 10 |
| rg | enum | RG 상품 여부 | 기본값: 'N' |
| duplicated | enum | 중복 QAID 여부 | 기본값: 'N' |
| checked_at | dateTime | 검수일 | Nullable |
| checked_status | unsignedTinyInteger | 입고 검수 여부 | Nullable |
| checked_user_id | foreignId | 검수한 직원 인덱스 | 외래키(users.id), Nullable |
| product_lot_id | foreignId | Lots 인덱스 | 외래키(product_lots.id), Nullable |
| product_vendor_id | foreignId | 공급처 인덱스 | 외래키(product_vendors.id), Nullable |
| product_link_id | foreignId | 쿠팡 연결 인덱스 | 외래키(product_links.id), Nullable |
| memo | text | 메모 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 일반 인덱스: req_id, qaid, barcode, cate4_id, cate5_id, user_id, status, rg, duplicated, checked_user_id, checked_status, checked_at, created_at
- 전문 검색 인덱스: name

## 모델 구현

### 1. Req 모델
파일: `app/Models/Req.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- HasFactory, Searchable 트레이트 사용
- 요청 타입 및 상태 관련 상수 정의
- 삭제 이벤트에서 관련 제품 데이터 정리

#### 요청 타입 상수
```php
const TYPE_COUPANG = 1; # 쿠팡 요청
const TYPE_APPLE = 2; # 애플(쿠팡) 요청
const TYPE_GHOST = 8; # 보관용 상품 요청
const TYPE_UNLINKED = 9; # 미등록 요청
```

#### 상태 상수
```php
const STATUS_REGISTERED = 10; # 등록
const STATUS_CHECKED = 30; # 점검중
const STATUS_COMPLETED = 50; # 완료
const STATUS_DELETED = 90; # 취소, 삭제
```

#### 필드 속성
- fillable: 대량 할당 가능 필드
- with: 기본으로 로드되는 관계 모델(user, checkedUser, reqCount)

#### 관계 메소드
- reqCount(): 요청 개수와의 관계 (hasOne)
- user(): 등록 사용자와의 관계 (belongsTo)
- checkedUser(): 검수 사용자와의 관계 (belongsTo)
- products(): 제품과의 관계 (hasMany)
- carryoutProducts(): 반출 상품과의 관계 (hasMany)
- deleteLog(): 삭제 로그와의 관계 (morphOne)

### 2. ReqCount 모델
파일: `app/Models/ReqCount.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- HasFactory 트레이트 사용
- 타임스탬프 비활성화

#### 개수 컬럼 상수
```php
const COUNT_COLUMNS = [
    'undelivered', # 미입고
    'unchecked', # 입고(검수)대기
    'checked', # 검수완료, 점검대기
    'carryout',  # 외주반출 수리중
    'waiting', # 수리대기(구성품 신청)
    'repaired', # 수리/점검완료(창고)
    'checkout', # 적재중(점검완료)
    'exporting', # 출고대기(마감)
    'duplicated', # 중복등록
    'unlinked', # 미등록
    'completed', # 출고완료
    'deleted', # 보류(삭제)
];
```

#### 필드 속성
- fillable: 대량 할당 가능 필드

#### 관계 메소드
- req(): 요청과의 관계 (belongsTo)

### 3. Product 모델
파일: `app/Models/Product.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- Searchable 트레이트 사용
- 상품 상태 및 검수 상태 관련 상수 정의
- Meilisearch 검색 기능 구현

#### 입고 검수 상태 상수
```php
const CHECKED_STATUS_UNCHECKED = 10;
const CHECKED_STATUS_CHECKED = 20;
const CHECKED_STATUS_UNDELIVERED = 30;
```

#### 상품 상태 상수
```php
const STATUS_REGISTERED = 10;
const STATUS_WAITING = 19;
const STATUS_REPAIRED = 20;
const STATUS_CHECKED_ON_PALLET = 30;
const STATUS_CARRIED_OUT = 50;
const STATUS_EXPORTED = 70;
const STATUS_HELD = 80;
const STATUS_DELETED = 90;
```

#### 필드 속성
- fillable: 대량 할당 가능 필드
- with: 기본으로 로드되는 관계 모델

#### 관계 메소드
- req(): 요청과의 관계 (belongsTo)
- cate4(): 카테고리4와의 관계 (belongsTo)
- cate5(): 카테고리5와의 관계 (belongsTo)
- lot(): 로트와의 관계 (belongsTo)
- vendor(): 공급업체와의 관계 (belongsTo)
- link(): 링크와의 관계 (belongsTo)
- palletProducts(): 팔레트 상품과의 관계 (hasMany)
- carryoutProducts(): 반출 상품과의 관계 (hasMany)
- user(): 등록 사용자와의 관계 (belongsTo)
- checkedUser(): 검수 사용자와의 관계 (belongsTo)

## 서비스 레이어 구현

### 1. ReqService
파일: `app/Services/ReqService.php`

#### 주요 메소드
- `getReqList(ReqFilterDto $filter)`: 요청 목록 조회 (필터링, 페이징 지원)
- `getUncheckedList()`: 검수대기 요청서 목록 조회
- `create(User $user, array $data)`: 요청 생성 (트랜잭션 처리)
- `update(int $id, User $user, array $data)`: 요청 수정
- `destroy(int $id, User $user)`: 요청 삭제 (연관 데이터 정리)

#### 비즈니스 로직
- 트랜잭션 처리로 데이터 일관성 보장
- ProductLog를 통한 작업 이력 관리
- Slack 알림을 통한 에러 모니터링

### 2. CountService
파일: `app/Services/CountService.php`

#### 주요 메소드
- `updateReqCount(int $id)`: 요청별 상품 상태 카운트 업데이트
- `getUndeliveredCount()`, `getUncheckedCount()` 등: 상태별 개수 계산
- `multipleUpdate()`: 여러 요청서 카운트 일괄 업데이트

#### 카운트 로직
- Product 상태와 Pallet 상태를 종합하여 정확한 카운트 계산
- 요청서 상태 자동 업데이트 (점검중, 완료 상태 변경)

## Job 및 스케줄러 구현

### 1. ReqJob
파일: `app/Jobs/ReqJob.php`

#### 주요 기능
- 엑셀 파일 파싱 및 Product 데이터 생성
- 중복 제품 감지 및 처리
- 큐를 통한 비동기 처리
- 실패 시 재시도 로직

### 2. UpdateAllCount Job
파일: `app/Jobs/UpdateAllCount.php`

#### 주요 기능
- 6개월 전 입고목록부터 카운터 계산
- 대량 데이터 처리 시 에러 복구
- 로그를 통한 모니터링

## 컨트롤러 구현

### ReqController
파일: `app/Http/Controllers/WMS/ReqController.php`

#### 주요 엔드포인트
- `reqList()`: 요청 목록 조회 (필터링, 페이징)
- `uncheckedList()`: 검수대기 요청서 목록
- `show(int $id)`: 요청 상세 조회
- `store(Request $request)`: 요청 생성
- `update(Request $request, int $id)`: 요청 수정
- `destroy(int $id)`: 요청 삭제
- `updateReqCount(int $id)`: 카운트 업데이트

#### 파일 업로드 처리
- `processExcel()`: 엑셀 파일 검증 및 업로드
- 파일 크기 제한 (2MB)
- 확장자 검증 (.xlsx, .xls)
- ReqJob을 통한 비동기 처리

## 입고 목록 관리 기능 구현 시 고려사항

1. **요청 타입 관리**
   - 쿠팡, 애플, 보관용, 미등록 등 다양한 요청 타입 구분
   - 타입별 처리 로직 구현

2. **요청 상태 관리**
   - 등록, 점검중, 완료, 삭제 등의 상태 관리
   - 상태 변경에 따른 처리 로직 구현

3. **요청 개수 관리**
   - 요청에 포함된 상품 상태별 개수 관리
   - 상태 변경 시 개수 자동 업데이트 처리

4. **제품 검색 및 필터링**
   - Meilisearch를 이용한 제품 검색 기능 구현
   - 다양한 조건(요청일, 검수상태 등)을 이용한 필터링

5. **입고 검수 관리**
   - 입고 검수 상태(검수대기, 검수완료, 미입고) 관리
   - 검수 완료 후 상태 변경 및 관련 데이터 업데이트

6. **엑셀 파일 처리**
   - 엑셀 업로드를 통한 대량 데이터 입력
   - 중복 검사 및 유효성 검증 구현
   - 큐를 통한 비동기 처리

7. **타입 안전성 개선 필요사항**
   - Auth::user() null 체크 추가
   - Request Validation 클래스 생성
   - 타입 캐스팅 개선

8. **추가 구현 필요사항**
   - 엑셀 다운로드 API 구현
   - 이벤트 리스너 구현
   - Swagger API 문서화
   - 검색 성능 테스트 