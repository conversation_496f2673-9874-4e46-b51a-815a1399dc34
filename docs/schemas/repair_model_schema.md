# 수리/점검 관리 기능 - 스키마 설계

## 개요
본 문서는 수리/점검 관리 기능의 핵심 모델인 수리/점검(RepairProduct) 모델 및 관련 테이블의 스키마 구조를 설명합니다. 수리/점검 관리는 제품의 수리 및 점검 과정을 추적하고 관리하는 기능입니다.

## 데이터베이스 구조

### 1. 수리/점검 상품(Repair Products) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2025_05_26_105649_create_repair_products_table.php`
- 테이블명: `repair_products`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 수리/점검 고유 식별자 | 기본키, 자동 증가 |
| product_id | foreignId | 상품 인덱스 | 외래키(products.id) |
| status | unsignedTinyInteger | 상태 | 기본값: 30 |
| waiting_user_id | unsignedBigInteger | 구성품 신청 직원 인덱스 | 외래키(users.id), Nullable |
| waiting_at | dateTime | 구성품 신청 일시 | Nullable |
| completed_user_id | unsignedBigInteger | 마지막으로 수리/점검한 직원 인덱스 | 외래키(users.id), Nullable |
| completed_at | dateTime | 마지막으로 수리/점검 일시 | Nullable |
| amount | unsignedInteger | 판매 단가(상품 등록값과 동일) | 기본값: 0 |
| repair_symptom_id | foreignId | 진단내용(상품상태)번호 | 외래키(repair_symptoms.id), Nullable |
| repair_process_id | foreignId | 처리내용(수리내역)번호 | 외래키(repair_processes.id), Nullable |
| repair_grade_id | foreignId | 분류등급(상품등급)번호 | 외래키(repair_grades.id), Nullable |
| invoice1 | unsignedInteger | 청구금액1(기본) | 기본값: 0 |
| invoice2 | unsignedInteger | 청구금액2(추가) | 기본값: 0 |
| invoice3 | unsignedInteger | 청구금액3(추가) | 기본값: 0 |
| memo | text | 메모 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |
| deleted_at | timestamp | 삭제 시간 | Nullable (소프트 삭제) |

#### 인덱스 정보
- 일반 인덱스: status, updated_at

### 2. 수리/점검 상품 구성품(Repair Product Parts) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2025_05_26_105650_create_repair_product_parts_table.php`
- 테이블명: `repair_product_parts`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 고유 식별자 | 기본키, 자동 증가 |
| repair_product_id | foreignId | 수리/점검 상품 인덱스 | 외래키(repair_products.id) |
| repair_parts_id | foreignId | 구성품 인덱스 | 외래키(repair_parts.id) |
| quantity | unsignedSmallInteger | 사용 개수 | 기본값: 1 |
| price | unsignedInteger | 사용시 단가(단가가 변경될 경우 대비) | 기본값: 0 |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

### 3. 수리 등급(Repair Grades) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2025_06_11_100000_create_repair_grades_table.php`
- 테이블명: `repair_grades`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 고유 식별자 | 기본키, 자동 증가 |
| name | string | 증상 이름 | - |
| code | string | 증상 코드 | Unique |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

### 4. 수리 구성품 카테고리(Repair Categories) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2025_04_23_000000_create_repair_categories_table.php`
- 테이블명: `repair_categories`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 고유 식별자 | 기본키, 자동 증가 |
| cate4_id | foreignId | 4차 카테고리 | 외래키(cate4.id) |
| cate5_id | foreignId | 5차 카테고리 | 외래키(cate5.id), Nullable |

#### 인덱스 정보
- 일반 인덱스: [cate4_id, cate5_id], cate4_id, cate5_id

### 5. 수리 구성품(Repair Parts) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2025_04_23_000010_create_repair_parts_table.php`
- 테이블명: `repair_parts`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 고유 식별자 | 기본키, 자동 증가 |
| category_id | foreignId | 수리 구성품 카테고리 인덱스 | 외래키(repair_parts_categories.id), Nullable |
| barcode | string | 바코드 | Nullable, Unique |
| name | string | 구성품 이름 | - |
| model_number | string | 구성품 모델번호 | Nullable |
| price | unsignedBigInteger | 구성품 가격 | 기본값: 0 |
| stock | unsignedMediumInteger | 구성품 재고 | 기본값: 0 |
| reorder_stock | unsignedMediumInteger | 재고 부족시 알림을 위한 최소 수량 | 기본값: 5 |
| acc_count | unsignedBigInteger | 누적 사용 개수 | 기본값: 0 |
| is_purchasable | enum | 구매 가능 여부 | 기본값: 'Y' |
| location_area | string | 구성품 건물 위치 | Nullable |
| location_zone | string | 구성품 랙의 위치 | Nullable |
| location_floor | unsignedTinyInteger | 랙에서의 층 | 기본값: 1 |
| location_position | unsignedMediumInteger | 층에서의 위치 | 기본값: 1 |
| memo | text | 메모 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 고유 인덱스: barcode
- 일반 인덱스: name, acc_count

## 모델 구현

### 1. RepairProduct 모델
파일: `app/Models/RepairProduct.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- SoftDeletes 트레이트 사용
- 수리/점검 상태 관련 상수 정의

#### 수리/점검 상태 상수
```php
const STATUS_WAITING = 10;
const STATUS_REPAIRED = 30;
const STATUS_DELETED = 90;
```

#### 필드 속성
- fillable: 대량 할당 가능 필드
- with: 기본으로 로드되는 관계 모델

#### 관계 메소드
- product(): 제품과의 관계 (belongsTo)
- repairSymptom(): 수리 증상과의 관계 (belongsTo)
- repairProcess(): 수리 과정과의 관계 (belongsTo)
- repairGrade(): 수리 등급과의 관계 (belongsTo)
- palletProducts(): 팔레트 상품과의 관계 (hasMany)
- waitingUser(): 대기 사용자와의 관계 (belongsTo)
- completedUser(): 완료 사용자와의 관계 (belongsTo)
- repairProductParts(): 수리 제품 구성품과의 관계 (hasMany)
- repairParts(): 수리 구성품과의 관계 (belongsToMany)

### 2. RepairProductParts 모델

#### 주요 특징
- 수리/점검 상품과 구성품 간의 관계 모델
- 사용된 구성품의 수량 및 가격 관리

#### 관계 메소드
- repairProduct(): 수리/점검 상품과의 관계 (belongsTo)
- repairParts(): 수리 구성품과의 관계 (belongsTo)

### 3. RepairParts 모델

#### 주요 특징
- 수리에 사용되는 구성품 모델
- 재고 관리 기능
- 위치 관리 기능

#### 관계 메소드
- category(): 카테고리와의 관계 (belongsTo)
- repairProducts(): 수리/점검 상품과의 관계 (belongsToMany)

### 4. RepairGrade 모델

#### 주요 특징
- 수리 등급 관리 모델
- 이름과 코드 관리

#### 관계 메소드
- repairProducts(): 수리/점검 상품과의 관계 (hasMany)
- repairProcesses(): 수리 과정과의 관계 (belongsToMany)

## 수리/점검 관리 기능 구현 시 고려사항

1. **수리/점검 상태 관리**
   - 구성품 신청, 수리/점검 완료, 삭제 상태 관리
   - 상태 변경에 따른 처리 로직 구현

2. **구성품 신청 및 관리**
   - 구성품 신청 프로세스
   - 구성품 재고 관리
   - 재고 부족 시 알림 기능

3. **수리/점검 정보 관리**
   - 진단 내용(증상), 처리 내용(수리 내역), 등급 정보 관리
   - 수리/점검 완료 시간 추적

4. **비용 관리**
   - 기본 및 추가 청구 금액 관리
   - 구성품 비용 관리

5. **구성품 위치 관리**
   - 구성품 물리적 위치 관리
   - 위치 검색 및 조회 기능

6. **사용자 추적**
   - 구성품 신청 담당자 추적
   - 수리/점검 완료 담당자 추적 