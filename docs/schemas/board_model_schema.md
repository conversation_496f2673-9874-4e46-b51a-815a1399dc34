# 게시판 관리 기능 - 스키마 설계

## 개요
본 문서는 게시판 관리 기능의 핵심 모델인 게시판(Board) 모델 및 관련 테이블의 스키마 구조를 설명합니다. 게시판 기능은 공지사항, FAQ 등의 정보 공유 및 커뮤니케이션을 위한 기능입니다.

## 데이터베이스 구조

### 1. 게시판(Boards) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_006100_create_boards_table.php`
- 테이블명: `boards`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 게시물 고유 식별자 | 기본키, 자동 증가 |
| type | string | 게시판 종류 | 기본값: 'board' |
| user_id | foreignId | 작성자 인덱스 | 외래키(users.id) |
| name | string | 작성자 이름 | - |
| subject | string | 제목 | - |
| content | longText | 내용 | - |
| status | unsignedTinyInteger | 상태 | 기본값: 1 |
| open_at | dateTime | 예약 게시물 | Nullable |
| f_show | enum | 보이기 여부 | 기본값: 'Y' |
| f_notice | enum | 공지 여부 | 기본값: 'N' |
| hits | unsignedBigInteger | 조회수 | 기본값: 0 |
| ip | string | 작성한 IP | - |
| user_agent | string | 접속 디바이스 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |
| deleted_at | dateTime | 삭제 시간 | Nullable (소프트 삭제) |

#### 인덱스 정보
- 일반 인덱스: type, user_id, name, subject, content, status, f_show, f_notice, hits, created_at

### 2. 게시판 댓글(Board Comments) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_006101_create_board_comments_table.php`
- 테이블명: `board_comments`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 댓글 고유 식별자 | 기본키, 자동 증가 |
| board_id | foreignId | 게시물 인덱스 | 외래키(boards.id) |
| user_id | foreignId | 작성자 인덱스 | 외래키(users.id) |
| name | string | 작성자 이름 | - |
| comment | text | 내용 | - |
| status | unsignedTinyInteger | 상태 | 기본값: 1 |
| ip | string | 작성한 IP | - |
| user_agent | string | 접속 디바이스 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 일반 인덱스: board_id, user_id, name, comment, status, created_at

### 3. 게시판 통계(Board Stats) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_006102_create_board_stats_table.php`
- 테이블명: `board_stats`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | 통계 고유 식별자 | 기본키, 자동 증가 |
| board_id | foreignId | 게시물 인덱스 | 외래키(boards.id) |
| user_id | foreignId | 작성자 인덱스 | 외래키(users.id) |
| ip | string | 확인한 IP | - |
| user_agent | string | 접속 디바이스 | Nullable |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 일반 인덱스: board_id, user_id, created_at

### 4. FAQ(FAQs) 테이블

#### 마이그레이션 정보
- 파일: `database/migrations/2024_07_01_006200_create_faqs_table.php`
- 테이블명: `faqs`

#### 스키마 구조
| 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
|--------|------------|------|-----------|
| id | bigint | FAQ 고유 식별자 | 기본키, 자동 증가 |
| subject | string | 제목 | - |
| cate1 | string | 분류1 | - |
| cate2 | string | 분류2 | Nullable |
| solution_code | string | 질문 유형 | - |
| content | text | 질문 답변 | - |
| created_at | timestamp | 생성 시간 | - |
| updated_at | timestamp | 수정 시간 | - |

#### 인덱스 정보
- 일반 인덱스: subject, content

## 모델 구현

### 1. Board 모델
파일: `app/Models/Board.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- HasFactory, SoftDeletes 트레이트 사용
- 게시판 종류 및 상태 관련 상수 정의
- 게시판 삭제 시 관련 데이터(댓글, 통계) 삭제 처리

#### 게시판 종류 상수
```php
const TYPE_BOARD = 'board';
const TYPE_NOTICE = 'notice';
const TYPE_FAQ = 'faq';
```

#### 상태 상수
```php
const STATUS_REGISTERED = 1;
const STATUS_DELETED = 9;
```

#### 표시 옵션 상수
```php
const F_SHOW_Y = 'Y';
const F_SHOW_N = 'N';

const F_NOTICE_Y = 'Y';
const F_NOTICE_N = 'N';
```

#### 필드 속성
- fillable: 대량 할당 가능 필드
- casts: 타입 캐스팅 (open_at, deleted_at)
- with: 기본으로 로드되는 관계 모델(user, comments)

#### 관계 메소드
- user(): 사용자와의 관계 (belongsTo)
- comments(): 댓글과의 관계 (hasMany)
- readStats(): 조회 통계와의 관계 (hasMany)

### 2. BoardComment 모델

#### 주요 특징
- 게시판 댓글 모델
- 게시물과 사용자 정보 연결
- 댓글 상태 관리

#### 관계 메소드
- board(): 게시판과의 관계 (belongsTo)
- user(): 사용자와의 관계 (belongsTo)

### 3. BoardStat 모델

#### 주요 특징
- 게시판 조회 통계 모델
- 조회한 사용자 정보 추적
- 조회수 집계 기능

#### 관계 메소드
- board(): 게시판과의 관계 (belongsTo)
- user(): 사용자와의 관계 (belongsTo)

### 4. FAQ 모델
파일: `app/Models/FAQ.php`

#### 주요 특징
- Laravel의 Model 클래스 상속
- HasFactory 트레이트 사용
- FAQ 정보 관리 기능

#### 필드 속성
- fillable: 대량 할당 가능 필드 (subject, cate1, cate2, solution_code, content)

## 게시판 관리 기능 구현 시 고려사항

1. **게시판 종류 관리**
   - 공지사항, 일반 게시판, FAQ 등 다양한 유형 관리
   - 종류별 권한 설정 및 UI 구현

2. **게시물 상태 관리**
   - 등록, 삭제 상태 관리
   - SoftDeletes를 통한 삭제 이력 관리

3. **게시물 표시 옵션**
   - 노출 여부(f_show) 설정을 통한 숨김 기능
   - 공지 여부(f_notice) 설정을 통한 상단 고정 기능
   - 예약 게시(open_at) 기능

4. **댓글 관리**
   - 댓글 작성 및 표시 기능
   - 댓글 상태 관리

5. **조회수 추적**
   - 사용자별 조회 이력 관리
   - 중복 조회 방지 로직

6. **FAQ 관리**
   - 카테고리별 FAQ 관리
   - 솔루션 코드를 통한 유형 관리 