# 대용량 엑셀 파일 처리 성능 최적화 가이드

## 📊 현재 성능 설정

### 메모리 관리
- **청크 크기**: 500행 (기존 1000행에서 감소)
- **메모리 제한**: 512MB
- **메모리 임계값**: 200MB (자동 가비지 컬렉션)
- **청크별 메모리 정리**: 활성화

### 실행 시간 설정
- **Job 타임아웃**: 60분 (기존 30분에서 증가)
- **PHP 실행 시간**: 무제한
- **파일 업로드 제한**: 10MB

## 🚀 성능 예측

### 처리 속도 (예상)
- **소용량 (1,000행 이하)**: 초당 150-200행
- **중용량 (1,000-5,000행)**: 초당 100-150행  
- **대용량 (5,000행 이상)**: 초당 80-120행

### 메모리 사용량 (예상)
- **1,000행**: 50-100MB
- **5,000행**: 200-300MB
- **10,000행**: 400-600MB

## ⚠️ 주의사항

### 1. 메모리 부족 시나리오
- **증상**: "Allowed memory size exhausted" 오류
- **해결방안**: 
  - 청크 크기를 300으로 추가 감소
  - 메모리 제한을 1GB로 증가
  - 서버 메모리 증설 검토

### 2. 타임아웃 발생 시나리오
- **증상**: Job이 60분 내에 완료되지 않음
- **해결방안**:
  - 청크 크기를 300으로 감소
  - 타임아웃을 90분으로 증가
  - 서버 성능 개선 검토

### 3. Redis 메모리 부족
- **증상**: 중복 QAID 저장 실패
- **해결방안**:
  - Redis 메모리 증설
  - 중복 데이터를 파일로 저장하는 방식으로 변경

## 🔧 추가 최적화 방안

### 1. 청크 크기 동적 조정
```php
// 파일 크기에 따른 청크 크기 자동 조정
protected function getOptimalChunkSize(int $totalRows): int
{
    if ($totalRows > 10000) return 300;
    if ($totalRows > 5000) return 400;
    if ($totalRows > 1000) return 500;
    return 1000;
}
```

### 2. 배치 처리 최적화
```php
// 여러 행을 한 번에 삽입하여 DB 부하 감소
protected function batchInsert(array $items): void
{
    Product::insert($items);
}
```

### 3. 인덱스 최적화
```sql
-- QAID 조회 성능 향상을 위한 인덱스
CREATE INDEX idx_products_qaid ON products(qaid);
CREATE INDEX idx_products_req_id ON products(req_id);
```

## 📈 모니터링 지표

### 성능 메트릭
- **처리 속도**: 행/초
- **메모리 사용량**: MB
- **DB 쿼리 시간**: ms
- **Redis 사용량**: MB

### 알림 설정
- **메모리 사용량 80% 초과**: 경고
- **처리 속도 50행/초 미만**: 경고
- **Job 타임아웃 10분 전**: 경고

## 🛠️ 문제 해결 체크리스트

### 메모리 문제
- [ ] 청크 크기 감소 (500 → 300)
- [ ] 메모리 제한 증가 (512MB → 1GB)
- [ ] 가비지 컬렉션 주기 조정
- [ ] 서버 메모리 증설

### 성능 문제
- [ ] DB 인덱스 최적화
- [ ] 쿼리 최적화
- [ ] 배치 처리 적용
- [ ] 서버 리소스 증설

### 안정성 문제
- [ ] 재시도 로직 강화
- [ ] 에러 핸들링 개선
- [ ] 로깅 강화
- [ ] 모니터링 시스템 구축 