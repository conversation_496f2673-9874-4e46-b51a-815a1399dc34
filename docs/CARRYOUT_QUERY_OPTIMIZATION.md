# CarryoutQueryBuilderService 쿼리 최적화 가이드

## 개요
외주 반출(팔레트) 목록 조회 시 성능을 최적화하기 위한 다양한 방법을 제안합니다.

## 캐시 관련 오류 해결

### ❌ 문제가 있는 캐시 구현
```php
// 문제: EloquentBuilder를 캐시하려고 시도
public function getCarryoutListWithCache(array $data): EloquentBuilder
{
    $cacheKey = 'carryout_list_' . md5(serialize($data));

    return Cache::remember($cacheKey, 300, function () use ($data) {
        return $this->getCarryoutList($data); // ❌ EloquentBuilder는 직렬화 불가
    });
}
```

**발생하는 오류:**
```
"Serialization of 'PDO' is not allowed"
```

**문제점:**
- EloquentBuilder는 PDO 연결을 포함하여 직렬화 불가
- 캐시는 직렬화된 데이터를 저장해야 함
- 쿼리 결과가 아닌 쿼리 빌더를 캐시하려고 시도

### ✅ 해결 방법

#### 1. 캐시 제거 (권장)
```php
// 단순하고 안전한 방법
public function getCarryoutList(array $data): EloquentBuilder
{
    $builder = Carryout::withCount([
        'carryoutProducts as count_total',
        'carryoutProducts as count_renovated' => function (EloquentBuilder $query) {
            $query->where('status', CarryoutProduct::STATUS_RENOVATED);
        },
        'carryoutProducts as count_remained' => function (EloquentBuilder $query) {
            $query->where('status', CarryoutProduct::STATUS_ONBOARD);
        },
    ])
    ->where('carryouts.status', '!=', Carryout::STATUS_CANCELED);

    // 날짜 범위 필터링 (인덱스 활용)
    if (!empty($data['beginAt']) && !empty($data['endAt'])) {
        $builder->whereBetween('carryouts.carryout_at', [$data['beginAt'], $data['endAt']]);
    }

    // 정렬 (인덱스 활용)
    $builder->orderBy('carryouts.created_at', 'desc');

    return $builder;
}
```

**장점:**
- ✅ 안전하고 예측 가능
- ✅ 페이지네이션 지원
- ✅ 실시간 데이터 반영
- ✅ 복잡한 필터링 지원

#### 2. 쿼리 결과 캐시 (대안)
```php
// 실제 쿼리 결과를 캐시
public function getCarryoutListWithCache(array $data): array
{
    $cacheKey = 'carryout_list_' . md5(json_encode($data, JSON_UNESCAPED_UNICODE));

    return Cache::remember($cacheKey, 300, function () use ($data) {
        return $this->getCarryoutList($data)->get()->toArray();
    });
}
```

**단점:**
- ❌ 페이지네이션 처리 복잡
- ❌ 실시간 데이터 반영 안됨
- ❌ 메모리 사용량 증가
- ❌ 캐시 무효화 관리 필요

#### 3. 조건 캐시 (고급)
```php
// 쿼리 조건만 캐시
public function getCarryoutListWithConditionCache(array $data): EloquentBuilder
{
    $cacheKey = 'carryout_conditions_' . md5(json_encode($data, JSON_UNESCAPED_UNICODE));

    $conditions = Cache::remember($cacheKey, 300, function () use ($data) {
        return $this->buildQueryConditions($data);
    });

    return $this->buildQueryFromConditions($conditions);
}
```

**장점:**
- ✅ 쿼리 빌더 반환 가능
- ✅ 페이지네이션 지원
- ✅ 조건 재사용 가능

**단점:**
- ❌ 구현 복잡
- ❌ 성능 향상 미미

## 현재 쿼리의 문제점

### 1. N+1 문제
```php
// 기존 코드: 각 Carryout마다 별도 쿼리 실행
Carryout::withCount([
    'carryoutProducts as count_total',
    'carryoutProducts as count_renovated' => function ($query) {
        $query->where('status', CarryoutProduct::STATUS_RENOVATED);
    },
    'carryoutProducts as count_remained' => function ($query) {
        $query->where('status', CarryoutProduct::STATUS_ONBOARD);
    },
])
```

### 2. 인덱스 미활용
- `carryout_at` 필터링 시 인덱스 활용 부족
- `created_at` 정렬 시 인덱스 활용 부족

## 최적화 방안

### 1. withCount 방식 (권장) ⭐

```php
public function getCarryoutList(array $data): EloquentBuilder
{
    return Carryout::withCount([
        'carryoutProducts as count_total',
        'carryoutProducts as count_renovated' => function (EloquentBuilder $query) {
            $query->where('status', CarryoutProduct::STATUS_RENOVATED);
        },
        'carryoutProducts as count_remained' => function (EloquentBuilder $query) {
            $query->where('status', CarryoutProduct::STATUS_ONBOARD);
        },
    ])
    ->where('carryouts.status', '!=', Carryout::STATUS_CANCELED)
    ->when(!empty($data['beginAt']) && !empty($data['endAt']), function ($query) use ($data) {
        return $query->whereBetween('carryouts.carryout_at', [$data['beginAt'], $data['endAt']]);
    })
    ->orderBy('carryouts.created_at', 'desc');
}
```

**장점:**
- ✅ Laravel의 최적화된 방식
- ✅ 자동으로 N+1 문제 해결
- ✅ 쿼리 캐싱 활용 가능
- ✅ 가독성과 유지보수성 우수

### 2. 서브쿼리 방식 (대안)

```php
public function getCarryoutListWithSubquery(array $data): EloquentBuilder
{
    return Carryout::select([
        'carryouts.*',
        DB::raw('(SELECT COUNT(*) FROM carryout_products WHERE carryout_products.carryout_id = carryouts.id) as count_total'),
        DB::raw('(SELECT COUNT(*) FROM carryout_products WHERE carryout_products.carryout_id = carryouts.id AND carryout_products.status = ' . CarryoutProduct::STATUS_RENOVATED . ') as count_renovated'),
        DB::raw('(SELECT COUNT(*) FROM carryout_products WHERE carryout_products.carryout_id = carryouts.id AND carryout_products.status = ' . CarryoutProduct::STATUS_ONBOARD . ') as count_remained'),
    ])
    ->where('carryouts.status', '!=', Carryout::STATUS_CANCELED)
    ->when(!empty($data['beginAt']) && !empty($data['endAt']), function ($query) use ($data) {
        return $query->whereBetween('carryouts.carryout_at', [$data['beginAt'], $data['endAt']]);
    })
    ->orderBy('carryouts.created_at', 'desc');
}
```

**장점:**
- ✅ 단일 쿼리로 모든 카운트 계산
- ✅ 복잡한 조건 지원
- ✅ 인덱스 활용 최적화

**단점:**
- ❌ 코드 복잡성 증가
- ❌ Laravel의 장점 활용 부족

## 성능 비교

| 구분 | withCount | 서브쿼리 | 캐시 |
|------|-----------|----------|------|
| **쿼리 최적화** | Laravel 자동 최적화 | 수동 최적화 | 조건부 최적화 |
| **가독성** | 우수 | 보통 | 우수 |
| **유지보수성** | 우수 | 보통 | 보통 |
| **페이지네이션** | 완벽 지원 | 완벽 지원 | 제한적 |
| **실시간성** | 완벽 | 완벽 | 제한적 |
| **메모리 사용** | 낮음 | 낮음 | 높음 |

## 권장사항

### 1. 기본 사용 (권장)
```php
// withCount 방식 사용
$carryouts = $this->carryoutQueryBuilderService->getCarryoutList($data);
```

### 2. 성능이 중요한 경우
```php
// 서브쿼리 방식 사용
$carryouts = $this->carryoutQueryBuilderService->getCarryoutListWithSubquery($data);
```

### 3. 캐시가 필요한 경우
```php
// 데이터베이스 레벨에서 캐시 설정
// 또는 Redis Query Cache 사용
```

## 결론

**withCount 방식을 사용하는 것이 가장 좋은 선택**입니다. Laravel의 최적화된 기능을 활용하면서도 안정적이고 유지보수하기 쉬운 코드를 작성할 수 있습니다.

캐시는 복잡성과 메모리 사용량을 증가시키므로, 특별한 성능 요구사항이 없는 한 사용하지 않는 것을 권장합니다. 