# WMS 범용 예외 처리 시스템

## 개요

WMS 시스템 전체에서 사용할 수 있는 일관된 예외 처리 시스템입니다. 이 시스템은 다음과 같은 목표를 가지고 설계되었습니다:

- 일관된 예외 처리 패턴 제공
- 모듈별 예외 분류 및 관리
- 컨텍스트 정보를 포함한 상세한 오류 정보 제공
- 표준화된 오류 메시지 생성

## 예외 클래스 계층 구조

```
WmsException (추상 기본 클래스)
├── BusinessException (비즈니스 로직 예외)
│   ├── CarryoutException (외주 관련 예외)
│   ├── WarehouseException (창고 관련 예외)
│   └── BusinessLogicException (레거시 호환성)
├── ValidationException (입력 검증 예외)
├── ResourceNotFoundException (리소스 없음 예외)
└── DataException (데이터베이스 관련 예외)
```

## 주요 클래스

### 1. WmsException (기본 클래스)

모든 WMS 예외의 기본 클래스입니다.

```php
abstract class WmsException extends Exception
{
    protected string $module;
    protected array $context = [];
    
    public function getModule(): string;
    public function getContext(): array;
    public function setContext(array $context): self;
    public function addContext(string $key, mixed $value): self;
}
```

### 2. BusinessException (비즈니스 로직 예외)

비즈니스 규칙 위반이나 로직 오류 시 사용합니다.

```php
// 사용 예시
throw BusinessException::forDuplicate('외주 반출 상품', 'TEST123');
throw BusinessException::forInvalidStatus('상품', 'TEST123', '반출됨', '등록됨');
throw BusinessException::forPermission('외주 반출');
```

### 3. ValidationException (입력 검증 예외)

입력 데이터 검증 실패 시 사용합니다.

```php
// 사용 예시
throw ValidationException::forField('qaid', 'QAID는 필수 입력 항목입니다.');
throw ValidationException::forFields(['qaid' => '필수 항목', 'carryoutId' => '잘못된 형식']);
```

### 4. ResourceNotFoundException (리소스 없음 예외)

요청한 리소스를 찾을 수 없을 때 사용합니다.

```php
// 사용 예시
throw ResourceNotFoundException::forResource('상품', 'TEST123');
throw ResourceNotFoundException::forId('외주 정보', 123);
```

### 5. CarryoutException (외주 관련 예외)

외주 관련 비즈니스 로직 예외입니다.

```php
// 사용 예시
throw CarryoutException::alreadyCarriedOut('TEST123');
throw CarryoutException::notCarriedOut('TEST123');
throw CarryoutException::alreadyImported('TEST123');
throw CarryoutException::carryoutNotFound(123);
throw CarryoutException::invalidProductStatus('TEST123', 'CARRIED_OUT');
```

## 헬퍼 클래스

### 1. ExceptionHelper

예외 생성을 위한 헬퍼 메서드들을 제공합니다.

```php
use App\Helpers\ExceptionHelper;

// 리소스 없음
ExceptionHelper::throwNotFound('상품', 'TEST123');
ExceptionHelper::throwNotFoundById('외주 정보', 123);

// 입력 검증
ExceptionHelper::throwValidation('qaid', 'QAID는 필수 항목입니다.');
ExceptionHelper::throwRequiredField('qaid');
ExceptionHelper::throwEmptyValue('qaid');

// 비즈니스 로직
ExceptionHelper::throwBusiness('사용자 정의 메시지');
ExceptionHelper::throwDuplicate('외주 반출 상품', 'TEST123');
ExceptionHelper::throwInvalidStatus('상품', 'TEST123', '현재상태', '필요상태');
ExceptionHelper::throwPermissionDenied('외주 반출');

// 데이터베이스
ExceptionHelper::throwTransactionFailed('외주 반출 처리');
ExceptionHelper::throwIntegrityViolation('외래키 제약조건');

// 예외 타입 확인
ExceptionHelper::isWmsException($exception);
ExceptionHelper::isBusinessException($exception);
ExceptionHelper::isValidationException($exception);
ExceptionHelper::isResourceNotFoundException($exception);
```

### 2. MessageHelper

일관된 오류 메시지 생성을 위한 헬퍼입니다.

```php
use App\Helpers\MessageHelper;

// 기본 메시지
MessageHelper::resourceNotFound('상품', 'TEST123');
MessageHelper::alreadyProcessed('외주 반출 상품', 'TEST123');
MessageHelper::requiredFieldMissing('qaid');
MessageHelper::permissionDenied();

// 상품 관련
MessageHelper::productNotFound('TEST123');
MessageHelper::productInvalidStatus('TEST123');

// 외주 관련
MessageHelper::carryoutNotFound('123');
MessageHelper::productAlreadyCarriedOut('TEST123');
MessageHelper::productNotCarriedOut('TEST123');
MessageHelper::productAlreadyImported('TEST123');

// 성공 메시지
MessageHelper::operationSuccess('외주 반출');
MessageHelper::carryoutExportSuccess();
MessageHelper::carryoutImportSuccess();

// 사용자 정의 메시지
MessageHelper::custom('사용자 {user}가 {action}을 수행했습니다.', [
    'user' => '홍길동',
    'action' => '외주 반출'
]);

// 포맷팅
MessageHelper::format('해당 %s [%s]이 존재하지 않습니다.', '상품', 'TEST123');
```

## 사용 가이드

### 1. 기본 사용법

```php
use App\Helpers\ExceptionHelper;
use App\Helpers\MessageHelper;

class CarryoutService
{
    public function exportProduct(array $data, User $user): void
    {
        // 상품 존재 확인
        $product = Product::where('qaid', $data['qaid'])->first();
        if (!$product) {
            ExceptionHelper::throwNotFound('상품', $data['qaid']);
        }
        
        // 중복 처리 확인
        $exists = CarryoutProduct::where('product_id', $product->id)->exists();
        if ($exists) {
            ExceptionHelper::throwDuplicate('외주 반출 상품', $data['qaid']);
        }
        
        // 상태 확인
        if ($product->status !== Product::STATUS_REGISTERED) {
            ExceptionHelper::throwInvalidStatus(
                '상품', 
                $data['qaid'], 
                $product->status, 
                Product::STATUS_REGISTERED
            );
        }
        
        // 비즈니스 로직 실행...
    }
}
```

### 2. 컨트롤러에서의 예외 처리

```php
use App\Helpers\ExceptionHelper;
use App\Helpers\MessageHelper;

class CarryoutController extends Controller
{
    public function export(ExportProductRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $user = Auth::user();
            $this->carryoutService->exportProduct($data, $user);
            
            return $this->successResponse([
                'message' => MessageHelper::carryoutExportSuccess()
            ]);
        } catch (WmsException $e) {
            // WMS 예외는 자동으로 Handler에서 처리됨
            throw $e;
        } catch (Exception $e) {
            // 기타 예외는 일반적인 오류로 처리
            return $this->errorResponse([
                'message' => MessageHelper::operationFailed('외주 반출'),
            ], $e, 'carryout', 500);
        }
    }
}
```

### 3. 트랜잭션과 함께 사용

```php
use App\Helpers\ExceptionHelper;
use Illuminate\Support\Facades\DB;

class CarryoutService
{
    public function exportProduct(array $data, User $user): void
    {
        try {
            DB::beginTransaction();
            
            // 비즈니스 로직 실행
            $this->validateProduct($data['qaid']);
            $this->processExport($data, $user);
            
            DB::commit();
        } catch (WmsException $e) {
            DB::rollBack();
            // WMS 예외는 그대로 전파
            throw $e;
        } catch (Exception $e) {
            DB::rollBack();
            // 기타 예외는 데이터 예외로 래핑
            ExceptionHelper::throwTransactionFailed('외주 반출 처리', $e);
        }
    }
}
```

## 로깅 및 모니터링

예외 핸들러는 자동으로 WMS 예외를 적절한 로그 레벨로 기록합니다:

- **ValidationException, BusinessException**: WARNING 레벨
- **ResourceNotFoundException**: INFO 레벨  
- **DataException**: ERROR 레벨

```php
// Handler.php에서 자동 처리
protected function logWmsException(WmsException $exception): void
{
    $module = $exception->getModule();
    $context = $exception->getContext();
    
    if ($exception instanceof ValidationException || $exception instanceof BusinessException) {
        SimpleLogService::warning($module, $exception->getMessage(), $context, $exception);
    } elseif ($exception instanceof ResourceNotFoundException) {
        SimpleLogService::info($module, $exception->getMessage(), $context);
    } else {
        SimpleLogService::error($module, $exception->getMessage(), $context, $exception);
    }
}
```

## 확장 가이드

새로운 모듈의 예외를 추가하려면:

1. **모듈별 예외 클래스 생성**:
```php
class ShippingException extends BusinessException
{
    protected string $module = 'shipping';
    
    public static function alreadyShipped(string $orderId): self
    {
        $message = "해당 주문 [{$orderId}]은 이미 출고된 상태입니다.";
        return new self($message, [
            'order_id' => $orderId,
            'type' => 'already_shipped'
        ]);
    }
}
```

2. **MessageHelper에 메시지 추가**:
```php
// MessageHelper.php에 추가
const SHIPPING_ALREADY_COMPLETED = "해당 출고 [%s]는 이미 완료된 상태입니다.";

public static function shippingAlreadyCompleted(string $shippingId): string
{
    return self::format(self::SHIPPING_ALREADY_COMPLETED, $shippingId);
}
```

3. **ExceptionHelper에 헬퍼 메서드 추가** (필요시):
```php
// ExceptionHelper.php에 추가
public static function throwShippingError(string $message, array $context = []): void
{
    throw new ShippingException($message, $context);
}
```

## 테스트

예외 시스템의 테스트는 다음과 같이 작성할 수 있습니다:

```php
public function test_throws_not_found_exception_when_product_not_exists()
{
    $this->expectException(ResourceNotFoundException::class);
    $this->expectExceptionMessage('해당 상품 [INVALID]이 존재하지 않습니다.');
    
    $this->carryoutService->exportProduct(['qaid' => 'INVALID'], $this->user);
}

public function test_exception_context_contains_proper_information()
{
    try {
        ExceptionHelper::throwNotFound('상품', 'TEST123');
    } catch (ResourceNotFoundException $e) {
        $context = $e->getContext();
        $this->assertEquals('상품', $context['resource']);
        $this->assertEquals('TEST123', $context['identifier']);
    }
}
```

## 마이그레이션 가이드

기존 코드를 새로운 예외 시스템으로 마이그레이션하려면:

1. **기존 Exception 사용을 WMS 예외로 교체**:
```php
// 기존
throw new Exception("해당 상품이 존재하지 않습니다.", 400);

// 새로운 방식
ExceptionHelper::throwNotFound('상품', $qaid);
```

2. **메시지를 MessageHelper 사용으로 교체**:
```php
// 기존
$message = "해당 상품 [{$qaid}]이 존재하지 않습니다.";

// 새로운 방식
$message = MessageHelper::productNotFound($qaid);
```

3. **예외 처리 로직 업데이트**:
```php
// 기존
catch (Exception $e) {
    if ($e->getCode() === 400) {
        // 비즈니스 로직 오류 처리
    }
}

// 새로운 방식
catch (BusinessException $e) {
    // 비즈니스 로직 오류 처리
} catch (ValidationException $e) {
    // 검증 오류 처리
} catch (ResourceNotFoundException $e) {
    // 리소스 없음 오류 처리
}
```

이 예외 시스템을 통해 WMS 전체에서 일관되고 체계적인 오류 처리가 가능합니다.