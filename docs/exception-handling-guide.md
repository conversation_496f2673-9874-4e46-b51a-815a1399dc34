# 예외 처리 개선 가이드라인

## 개요

Laravel 애플리케이션에서 예외 처리를 개선하여 적절한 로그 레벨을 사용하고, 사용자에게 더 나은 피드백을 제공하는 방법을 설명합니다.

## 문제점

기존 코드에서는 모든 예외가 `error` 레벨로 로깅되어 다음과 같은 문제가 있었습니다:

1. **사용자 실수와 시스템 오류의 구분 불가**: 비즈니스 로직 예외(사용자 실수)와 시스템 오류가 동일하게 처리됨
2. **알림 노이즈**: 실제 시스템 오류가 아닌 사용자 실수로 인한 알림이 과도하게 발생
3. **디버깅 어려움**: 실제 문제와 사용자 실수를 구분하기 어려움

## 해결 방법

### 1. 예외 분류

예외를 두 가지 카테고리로 분류합니다:

#### 비즈니스 로직 예외 (Business Logic Exception)
- **정의**: 사용자의 잘못된 입력이나 비즈니스 규칙 위반으로 인한 예외
- **로그 레벨**: `warning`
- **예시**:
  - 팔레트 상태가 맞지 않아 작업할 수 없는 경우
  - 등록상품이 없는 경우
  - 팔레트에 해당 상품이 없는 경우
  - 팔레트 등급 불일치

#### 시스템 오류 (System Error)
- **정의**: 데이터베이스 연결 오류, 트랜잭션 실패, 시스템 내부 오류
- **로그 레벨**: `error`
- **예시**:
  - 데이터베이스 연결 실패
  - 트랜잭션 롤백
  - 외부 API 호출 실패
  - 메모리 부족

### 2. 커스텀 예외 클래스 생성

```php
<?php

namespace App\Exceptions;

use Exception;

/**
 * 비즈니스 로직 예외 클래스
 */
class BusinessLogicException extends Exception
{
    protected array $context;

    public function __construct(string $message = "", array $context = [], int $code = 0, ?Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public static function isBusinessLogicException(Exception $exception): bool
    {
        return $exception instanceof self;
    }
}
```

### 3. 예외 처리 패턴

#### 비즈니스 로직 예외 처리

```php
// 비즈니스 로직 예외 발생
if ($pallet->status !== Pallet::STATUS_CLOSED) {
    $errorMessage = "쿠팡RP팔레트는 출고할 수 없는 상태(" . Pallet::$STATUS_NAME[$pallet->status] . ")입니다.";
    $context = [
        'pallet_id' => $pallet->id,
        'current_status' => $pallet->status,
        'status_name' => Pallet::$STATUS_NAME[$pallet->status] ?? '알 수 없음',
        'user_id' => $user->id,
    ];
    
    // warning 레벨로 로깅
    SimpleLogService::warning('pallet', '팔레트 출고 상태 오류', $context);
    
    // 커스텀 예외 발생
    throw new BusinessLogicException($errorMessage, $context);
}
```

#### catch 블록에서 예외 분류

```php
try {
    // 비즈니스 로직 실행
} catch (Exception $e) {
    DB::rollBack();

    // 비즈니스 로직 예외인지 확인
    if (BusinessLogicException::isBusinessLogicException($e)) {
        // 비즈니스 로직 예외는 이미 warning으로 로깅했으므로 여기서는 로깅하지 않음
    } else {
        // 시스템 오류인 경우에만 error 레벨로 로깅
        SimpleLogService::error('pallet', '팔레트 출고 실패', [], $e);
    }

    throw $e;
}
```

## 구현 예시

### PalletService 개선 사례

#### 개선 전
```php
if ($pallet->status === Pallet::STATUS_CLOSED) {
    // 정상 처리
} else {
    throw new Exception("쿠팡RP팔레트는 출고할 수 없는 상태(" . Pallet::$STATUS_NAME[$pallet->status] . ")입니다.");
}

// catch 블록에서 모든 예외를 error로 로깅
} catch (Exception $e) {
    SimpleLogService::error('pallet', '팔레트 출고 실패', [], $e);
    throw $e;
}
```

#### 개선 후
```php
if ($pallet->status === Pallet::STATUS_CLOSED) {
    // 정상 처리
} else {
    // 비즈니스 로직 예외: 팔레트 상태가 맞지 않음 (warning 레벨)
    $errorMessage = "쿠팡RP팔레트는 출고할 수 없는 상태(" . Pallet::$STATUS_NAME[$pallet->status] . ")입니다.";
    $context = [
        'pallet_id' => $pallet->id,
        'current_status' => $pallet->status,
        'status_name' => Pallet::$STATUS_NAME[$pallet->status] ?? '알 수 없음',
        'user_id' => $user->id,
    ];
    SimpleLogService::warning('pallet', '팔레트 출고 상태 오류', $context);
    throw new BusinessLogicException($errorMessage, $context);
}

// catch 블록에서 예외 타입에 따라 다르게 처리
} catch (Exception $e) {
    DB::rollBack();

    // 시스템 오류인 경우에만 error 레벨로 로깅
    if (BusinessLogicException::isBusinessLogicException($e)) {
        // 비즈니스 로직 예외는 이미 warning으로 로깅했으므로 여기서는 로깅하지 않음
    } else {
        SimpleLogService::error('pallet', '팔레트 출고 실패', [], $e);
    }

    throw $e;
}
```

## 장점

1. **명확한 예외 분류**: 비즈니스 로직 예외와 시스템 오류를 명확히 구분
2. **적절한 로그 레벨**: 예외의 성격에 맞는 로그 레벨 사용
3. **알림 노이즈 감소**: 실제 시스템 오류에만 error 알림 발생
4. **디버깅 효율성**: 문제 유형에 따른 빠른 원인 파악
5. **사용자 경험 개선**: 적절한 에러 메시지와 컨텍스트 정보 제공

## 적용 가이드라인

### 1. 새로운 서비스 작성 시

1. `BusinessLogicException` 클래스 import
2. 비즈니스 로직 예외 발생 시 warning 레벨로 로깅
3. `BusinessLogicException` 사용하여 예외 발생
4. catch 블록에서 예외 타입 확인 후 적절한 로그 레벨 사용

### 2. 기존 코드 개선 시

1. 예외 메시지 분석하여 비즈니스 로직 예외 식별
2. 해당 예외를 `BusinessLogicException`으로 변경
3. warning 레벨 로깅 추가
4. catch 블록에서 예외 타입 확인 로직 추가

### 3. 로그 모니터링

1. **warning 로그**: 사용자 실수 패턴 분석, UI/UX 개선 포인트 파악
2. **error 로그**: 실제 시스템 문제, 즉시 대응 필요

## 주의사항

1. **예외 메시지**: 사용자에게 보여질 메시지는 명확하고 친화적으로 작성
2. **컨텍스트 정보**: 디버깅에 필요한 충분한 정보 포함
3. **보안**: 민감한 정보는 로그에 포함하지 않음
4. **일관성**: 프로젝트 전체에서 동일한 패턴 적용

## 관련 파일

- `app/Exceptions/BusinessLogicException.php`: 커스텀 예외 클래스
- `app/Services/PalletService.php`: 개선된 예외 처리 예시
- `app/Services/SimpleLogService.php`: 로깅 서비스 