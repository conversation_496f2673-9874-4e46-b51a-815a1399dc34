# 코너스톤 프로젝트 WMS PRD (Product Requirements Document)

## 문서 목록

이 프로젝트의 전체 문서는 다음과 같이 구성되어 있습니다:

### 핵심 문서
- **[PRD (Product Requirements Document)](./prd.md)**: 제품 요구사항 정의 (현재 문서)
- **[사용자 시나리오](./user-scenarios.md)**: 사용자 역할별 상세 업무 시나리오
- **[API 명세서](./api-specification.md)**: RESTful API 엔드포인트 및 명세

### 데이터베이스 스키마
- **[사용자 모델 스키마](./schemas/user_model_schema.md)**: 사용자, 직원, 권한 관리
- **[점검 요청 모델 스키마](./schemas/req_model_schema.md)**: 점검 요청 및 관리
- **[수리 모델 스키마](./schemas/repair_model_schema.md)**: 수리/점검, 증상, 처리 과정, 등급
- **[팔레트 모델 스키마](./schemas/pallet_model_schema.md)**: 팔레트, 창고, 위치 관리
- **[반출 모델 스키마](./schemas/carryout_model_schema.md)**: 반출/반입, 협력사 관리
- **[게시판 모델 스키마](./schemas/board_model_schema.md)**: 공지사항, FAQ 관리

### 개발 문서
- **[백엔드 가이드](./backend.md)**: 백엔드 개발 가이드라인
- **[프론트엔드 가이드](./frontend.md)**: 프론트엔드 개발 가이드라인
- **[체크리스트](./checklist.md)**: 개발 진행 상황 및 체크리스트

---

## 1. 제품 개요

### 1.1 제품명
코너스톤 프로젝트 WMS (Warehouse Management System)

### 1.2 목적
반품된 제품의 점검 및 수리 프로세스 전반을 효율적으로 관리하고 지원하는 풀스택 애플리케이션을 개발합니다.

### 1.3 개발 방식
- **MVP (Minimum Viable Product)**: 핵심 기능부터 빠르게 개발하여 시장의 피드백을 반영합니다.
- **Agile**: 애자일 방법론을 병행하여 유연하고 반복적인 개발을 지향합니다.

### 1.4 아키텍처
- **백엔드**: Laravel, Laravel Scout (Meilisearch), MariaDB
- **프론트엔드**: Tauri v2, Svelte v5 (SvelteKit), Tailwind CSS v3.x + daisyUI v4.x

### 1.5 배포
- Docker를 이용한 컨테이너 기반 배포

---

## 2. 대상 사용자

### 2.1 주요 사용자
- **시스템 관리자**: WMS 시스템 전체 관리 및 유지보수
- **관리자**: 직원 관리, 업무 총괄, 보고서 작성
- **입고 담당자**: 반품 제품 입고, 입고 검수, 팔레트 적재
- **수리 담당자**: 제품 점검 및 수리, 수리비 산정
- **출고 담당자**: 수리 완료 제품 출고, 협력사 반출 관리
- **협력사 직원**: 외주 수리, 반입 처리

### 2.2 사용 환경
- **데스크톱 앱**: Tauri로 빌드한 Webview 기반 앱 (주 업무용)
- **웹 브라우저**: 창고 담당자의 팔레트 입출고 처리용
- **모바일**: 향후 태블릿 앱 개발 예정

### 2.3 상세 사용자 시나리오
자세한 사용자 시나리오는 별도 문서를 참조하세요:
- **[사용자 시나리오](./user-scenarios.md)**: 각 사용자 역할별 상세 업무 시나리오, 예외 상황 처리, 성능 요구사항 등

---

## 3. 핵심 기능 요구사항 (Core Features)

### 3.0 핵심 비즈니스 규칙 (Critical Business Rules)

> **⚠️ 중요**: 다음 5가지 규칙은 시스템의 핵심 비즈니스 로직으로, 모든 기능 구현 시 반드시 준수해야 합니다.

#### 📋 규칙 1: QAID 중복 관리
- **내용**: 모든 제품에는 발주처에서 발행한 고유한 **QAID**가 부여됨
- **요구사항**: 중복된 QAID 입고 시 시스템이 자동 감지하고 관리자에게 즉시 통보
- **처리**: 중복 제품은 별도 상태로 관리하여 오류 방지

#### 📋 규칙 2: 제품 이력 추적
- **내용**: 모든 제품은 입고 목록 등록부터 출고까지 전체 이력 기록 필수
- **목적**: 통계 자료 및 직원 생산성 분석을 위한 데이터 축적
- **범위**: 입고 → 검수 → 수리 → 출고의 모든 단계별 상태 및 처리 이력

#### 📋 규칙 3: 직원 작업 로그
- **내용**: 모든 직원의 로그인부터 퇴근까지 모든 작업 로그 저장
- **목적**: 직원별 생산성 측정 및 업무 효율성 분석
- **범위**: 시스템 접근, 데이터 조회/수정, 작업 완료 등 모든 활동

#### 📋 규칙 4: 수리비 차등 관리
- **내용**: 수리비는 카테고리별, 제품별로 차등 적용
- **예시**: 모니터류의 경우 인치(또는 CM)에 따라 가격 책정 차등
- **요구사항**: 제품 특성에 따른 유연한 수리비 정책 적용 가능

#### 📋 규칙 5: 구성품 자동 관리
- **내용**: 구성품 자동 발주 기능 및 사용량 누적 관리
- **기능**: 
  - 사용된 수량 누적으로 인기 구성품 분석
  - 시세 변동에 따른 가격 관리
  - 재고 부족 시 자동 발주 알림
- **목적**: 효율적인 부품 관리 및 비용 최적화

### 3.1 인증 및 권한 관리
- **인증**: Laravel Sanctum을 이용한 토큰 기반 인증
- **계정 관리**: 관리자가 직원의 계정을 생성하고 관리 (자체 회원가입 기능 불필요)

### 3.2 입고 목록 관리
- **생성**: 엑셀 파일 일괄 업로드 및 DB 저장 (중복 제품 검사 포함), 일반/애플 제품 분류
- **조회**: 입고 날짜(기간), 제품 분류 등 다양한 조건으로 검색
- **수정/삭제**: 입고 목록 항목 수정 및 삭제
- **상태 관리**: 입고 목록의 '완료'/'미완료' 상태 변경
- **출력**: 목록을 엑셀 파일로 출력

### 3.3 입고 검수 관리
- **검수**: 입고된 제품의 실물 확인 및 중복 검사 (제품 검색 기능 포함)
- **상태 변경**: 제품 상태를 '입고'/'미입고'/'중복' 등으로 변경
- **팔레트 입고 (웹)**: 바코드 스캔을 통한 간편한 팔레트 입고 처리 및 엑셀 출력

### 3.4 점검/수리 관리
- **팔레트 출고 (웹)**: 바코드 스캔을 통한 간편한 팔레트 출고 처리
- **점검/수리**: 입고 검수가 완료된 제품의 점검 및 수리 진행
- **라벨 프린트**: Tauri 플러그인을 이용한 라벨 프린터 연동
- **수리비 자동 산정**:
    - **수리비 관리**: 수리비 책정 기준 관리
    - **구성품 관리**: 수리에 사용되는 부품 및 자재 관리
    - **등급별 자동화**: 수리 증상, 처리 내용, 수리 등급에 따라 수리비 자동 산정
- **제품 검색**: Meilisearch를 이용한 빠른 제품 검색 페이지

### 3.5 출고 관리
- **출고 검수**: 점검/수리가 완료된 제품의 최종 출고 검수 및 처리
- **협력사 반입**: 외주 수리 후 반입된 제품의 수리비 확인 및 처리
- **수리비 조정**: 출고 과정에서 필요시 수리비를 조정할 수 있는 기능

### 3.6 설정
- **직원 관리**: 직원 정보 및 권한 관리
- **수리코드 관리**: 점검/수리 관련 코드(증상, 처리 내용, 수리 등급) 설정
- **수리비 설정**: 수리비 책정 기준 항목 설정
- **구성품 설정**: 수리에 사용되는 구성품 정보 설정

### 3.7 관리 및 통계
- **로그 관리**: 제품별 수리 로그 
- **생산성 관리**: 직원별 생산성 관리
- **근태 관리**: 직원 근태 기록 및 관리

### 3.8 사용자 인터페이스 (UI)
- **정보 밀도**: 작은 화면에서도 많은 정보를 효율적으로 확인할 수 있는 레이아웃
- **직관성**: 사용자가 별도의 학습 없이 사용할 수 있는 직관적인 UI
- **실시간 업데이트**: 데이터 변경 시 화면에 실시간으로 반영

---

## 4. 기술 스택

### 4.1 백엔드
- **언어**: PHP
- **API 프레임워크**: Laravel
- **주요 패키지**: Laravel Sanctum, Laravel Excel, Meilisearch 등
- **데이터베이스**: MariaDB
- **ORM**: Eloquent ORM
- **API 구조**: RESTful API (JSON 응답)

### 4.2 프론트엔드
- **프레임워크**: Svelte v5 (SvelteKit)
- **스타일링**: Tailwind CSS v3.x + daisyUI v4.x
- **빌드 도구**: Vite
- **데스크톱 앱**: Tauri v2 (Rust 기반)
- **패키지 관리자**: pnpm
- **언어**: TypeScript
- **상태 관리**: Svelte Stores
- **API 통신**: Fetch API

### 4.3 인프라
- **컨테이너화**: Docker & Docker Compose
- **배포 환경**: AWS (예정)
- **데이터 저장소**: MariaDB (RDBMS)

---

## 5. API 설계

API 상세 설계는 별도 문서를 참조하세요:
- **[API 명세서](./api-specification.md)**: RESTful API 엔드포인트, 요청/응답 형식, 에러 코드, 권한 관리 등

### 5.1 주요 API 그룹
1.  **인증 API**: 로그인/로그아웃, 사용자 정보
2.  **점검 요청 API**: 점검 요청 CRUD
3.  **상품 관리 API**: 제품 검색, QAID 관리, 중복 검사
4.  **입고 검수 API**: 입고 검수 프로세스 관리
5.  **팔레트 관리 API**: 창고 팔레트 입출고
6.  **수리/점검 API**: 수리 작업 기록, 구성품 사용 관리
7.  **출고 관리 API**: 출고 검수, 반출/반입 처리
8.  **설정 API**: 직원, 수리 코드, 수리비 등 기준 정보 관리
9.  **게시판 API**: 공지사항, FAQ 관리

---

## 6. 데이터베이스 스키마

DB 스키마 상세 설계는 별도 문서를 참조하세요:
- **[사용자 모델 스키마](./schemas/user_model_schema.md)**
- **[점검 요청 모델 스키마](./schemas/req_model_schema.md)**
- **[수리 모델 스키마](./schemas/repair_model_schema.md)**
- **[팔레트 모델 스키마](./schemas/pallet_model_schema.md)**
- **[반출 모델 스키마](./schemas/carryout_model_schema.md)**
- **[게시판 모델 스키마](./schemas/board_model_schema.md)**

### 6.1 주요 테이블 구조
1.  **users**: 사용자 계정 정보
2.  **companies**: 회사 정보
3.  **reqs**: 점검 요청 관리
4.  **products**: 제품 정보 및 QAID 관리
5.  **repairs**: 수리/점검 기록
6.  **pallets**: 팔레트 정보
7.  **carryouts**: 반출/반입 관리
8.  **boards**: 게시판 관리

---

## 7. 비기능적 요구사항 (Non-functional Requirements)

### 7.1 성능 요구사항

| 구분 | 항목 | 목표 |
| --- | --- | --- |
| **응답 시간** | API 응답 시간 | 평균 500ms 이내 (95% 요청 1초 이내) |
| | 페이지 로딩 시간 | 초기 로딩 3초 이내, SPA 전환 1초 이내 |
| | 검색 응답 시간 | 2초 이내 |
| | 바코드 스캔 응답 | 1초 이내 |
| | 엑셀 다운로드 | 30초 이내 (10,000건 기준) |
| **동시성** | 최대 동시 사용자 | 50명 |
| | 동시 트랜잭션 | 100 TPS |
| | 데이터베이스 연결 | 최대 20개 |
| **처리량** | 일일 제품 처리량 | 1,000건 |
| | 일일 수리 작업 | 500건 |
| | 일일 팔레트 입출고 | 100건 |

### 7.2 보안 요구사항

| 구분 | 항목 | 요구사항 |
| --- | --- | --- |
| **인증/권한** | 비밀번호 정책 | 최소 8자, 영문/숫자/특수문자 조합 |
| | 비밀번호 해싱 | bcrypt (cost factor 12) |
| | 세션/토큰 관리 | Laravel Sanctum 기반, 8시간 유효 후 자동 만료 |
| **데이터 보호**| 개인정보 암호화 | 민감 정보 AES-256 암호화 |
| | 전송 보안 | HTTPS/TLS 1.3 필수 |
| | 접근 로그 | 모든 데이터 접근 및 변경 로그 기록 |
| | 백업 암호화 | 백업 데이터 암호화 저장 |
| **네트워크** | CORS 정책 | 허용된 도메인만 API 접근 허용 |
| | Rate Limiting | API별 분당 요청 횟수 제한 |
| | Injection 방지 | SQL Injection(Prepared Statements), XSS(입력값 검증) 방지 |

### 7.3 사용성 요구사항

| 구분 | 항목 | 요구사항 |
| --- | --- | --- |
| **UI** | 직관성 | 별도 학습 없이 사용 가능한 UI |
| | 반응형 디자인 | 모바일(320px)부터 데스크톱(1024px+)까지 지원 |
| | 접근성 | WCAG 2.1 AA 수준 준수 |
| | 다국어 지원 | 한국어 기본 (향후 영어 확장 구조 고려) |
| **UX** | 로딩 표시 | 모든 비동기 작업에 로딩 인디케이터 표시 |
| | 에러 처리 | 명확한 에러 메시지와 해결 가이드 제시 |
| | 오프라인 지원 | (선택) 네트워크 단절 시 데이터 임시 저장 |
| | 바코드 지원 | Code128, QR 등 다양한 형식 지원 |

### 7.4 확장성 요구사항

| 구분 | 항목 | 요구사항 |
| --- | --- | --- |
| **시스템** | 수평 확장 | 로드 밸런서를 통한 다중 서버 지원 구조 |
| | 데이터베이스 확장 | 읽기/쓰기 분리(Read Replica) 지원 구조 |
| | 캐싱 | Redis 등을 통한 세션 및 데이터 캐싱 |
| | 비동기 처리 | Laravel Queues를 통한 대용량 작업 처리 |
| **기능** | 모듈화 | 기능별 모듈 분리로 독립적 확장 가능 |
| | API 버전 관리 | 하위 호환성을 고려한 API 버전 관리 |
| | 플러그인 구조 | 새로운 기능을 플러그인 형태로 추가 가능한 구조 |
| | 설정 관리 | 환경별(개발/스테이징/운영) 설정 분리 |

### 7.5 가용성 요구사항

| 구분 | 항목 | 요구사항 |
| --- | --- | --- |
| **시스템** | 업타임 | 99.5% 이상 (월 3.6시간 이하 다운타임) |
| | 백업 | 일일 자동 백업 (30일 보관) |
| | 재해 복구 | 4시간 이내 복구 (RTO 4시간) |
| | 모니터링 | 24/7 시스템 상태 모니터링 |
| **데이터** | 무결성 | ACID 트랜잭션 보장 |
| | 유효성 검사 | 서버 및 클라이언트 양단에서 데이터 유효성 검사 |
| | 중복 방지 | QAID 등 핵심 데이터 중복 방지 |
| | 감사 로그 | 모든 데이터 변경 이력 기록 |

---

## 8. 제약사항

### 8.1 기술적 제약
- TDD(Test-Driven Development) 방법론을 이용한 구현
- 복잡한 기능은 작은 단위의 함수로 분리하여 구현 (Clean Code)
- 이메일 인증 등 불필요한 인증 절차 제외
- 오류 추적을 위한 상세한 로깅 시스템 필수 구현

### 8.2 비즈니스 제약
- MVP 방식으로 핵심 기능부터 우선 개발하여 빠른 가치 제공에 집중

---

## 9. 개발 우선순위

### 9.1 Phase 1: 백엔드 API 설계 및 구현
- Docker 환경 구축 (Laravel, MariaDB, Meilisearch)
- 사용자 인증 및 핵심 기능 API 구현

### 9.2 Phase 2: 프론트엔드 기본 UI 구현
- Tauri, SvelteKit 프로젝트 설정
- Tailwind CSS + daisyUI 설정 및 기본 레이아웃 구현
- 로그인 페이지 및 핵심 기능 페이지 구현

### 9.3 Phase 3: 통합 및 배포
- 백엔드-프론트엔드 API 연동
- Docker Compose를 이용한 전체 시스템 통합
- 로컬 및 테스트 서버 환경에서 통합 테스트
- 운영 서버 배포

---

## 10. 성공 지표

### 10.1 기능적 성공 지표
- [ ] 핵심 기능(입고/검수, 점검/수리, 출고) 정상 작동
- [ ] 역할 기반(관리자, 담당자 등) 권한 제어 정상 작동
- [ ] 프론트엔드-백엔드 API 연동 완료
- [ ] 주요 기기(데스크톱, 웹)에서 반응형 UI 정상 작동

### 10.2 기술적 성공 지표
- [ ] Docker 컨테이너 정상 실행 및 서비스 안정성 확보
- [ ] API 평균 응답시간 < 500ms 달성
- [ ] CI/CD 파이프라인을 통한 자동 빌드 및 배포 오류 없음

---

## 11. 향후 확장 가능성

### 11.1 기능 확장
- **제품 잠금(Lock) 기능**: 관리자가 특정 제품의 출고를 보류시키는 기능
- **고도화된 통계/리포트**: 기간별, 담당자별, 제품별 성과 분석 대시보드
- **실시간 알림**: WebSocket을 활용한 작업 할당 및 상태 변경 실시간 알림

### 11.2 기술적 확장
- **캐싱 시스템 고도화**: Redis, Memcached를 활용한 성능 최적화
- **모바일 앱 개발**: 태블릿 등 모바일 기기 네이티브 앱 개발

---

## 12. 마일스톤

- **M1**: 백엔드 API 완성 및 단위 테스트 완료
- **M2**: 프론트엔드 핵심 기능 UI/UX 구현 완료
- **M3**: 전체 시스템 통합 및 E2E 테스트 완료
- **M4**: 라이브 데모 및 사용자 피드백 세션 준비 완료
