# 엑셀 헤더 매핑 시스템

## 📋 개요

원청에서 제공하는 엑셀 파일의 헤더가 자주 변경되는 문제를 해결하기 위해, 헤더 이름 기반의 동적 매핑 시스템을 구축했습니다.

## 🔧 시스템 구조

### 1. 헤더 매핑 설정 파일
- **위치**: `config/excel-headers.php`
- **기능**: 다양한 헤더명을 내부 필드명으로 매핑

### 2. 지원하는 헤더 형식
- **영어**: QAID, DESCRIPTION, QUANTITY 등
- **한글**: 구분, 센터, 상품명 등
- **혼합**: VENDOR_NAME, Product Name 등
- **오타 허용**: wmsSkuld (wmsSkuId의 오타)

## 📊 헤더 매핑 테이블

### 필수 필드 (반드시 있어야 하는 필드)
| 내부 필드명 | 지원 헤더명 |
|------------|------------|
| qaid | QAID, qaid, Qaid, qa_id, QA_ID |
| cate4 | CATE4, Cate4, cate4, 카테고리4, 카테4, CATEGORY4, Category4 |
| cate5 | CATE5, Cate5, cate5, 카테고리5, 카테5, CATEGORY5, Category5 |
| description | DESCRIPTION, description, 상품명, 상품설명, PRODUCT_NAME, Product Name |
| vendor_name | VENDOR_NAME, vendor_name, VENDOR NAME, 공급업체명, 벤더명, Vendor Name |

### 선택적 필드 (있으면 좋고 없어도 되는 필드)
| 내부 필드명 | 지원 헤더명 |
|------------|------------|
| lot_full_name | LOT_FULL_NAME, LOT FULL NAME, lot_full_name, 로트명, LOT명, Lot Name |
| wms_sku_id | wmsSkuId, wms_sku_id, WMS_SKU_ID, WMS SKU ID, wmsSkuld, WMS_SKULD |
| external_sku_id | externalSkuId, external_sku_id, EXTERNAL_SKU_ID, externalSkuld, EXTERNAL_SKULD |
| barcode | BARCODE, barcode, 바코드, BARCODEDESCRIPTIONDOR_NAQ, Barcode |
| quantity | QUANTITY, quantity, 수량, QTY, Quantity |
| amount | AMOUNT, amount, 금액, 가격, Amount, PRICE |
| vendor_item_id | vendoritemid, vendor_item_id, VENDOR_ITEM_ID, 공급업체상품ID, Vendor Item ID |
| product_id | productid, product_id, PRODUCT_ID, 상품ID, Product ID |
| item_id | itemid, item_id, ITEM_ID, 아이템ID, Item ID |
| rg | 구분, RG, rg, RG구분, RG_구분, RG_DIVISION, Division |
| center | 센터, CENTER, center, 센터명, CENTER_NAME, Center Name |

## 🚀 사용 방법

### 1. 기본 사용법
```php
// ReqJob.php에서 헤더 정보를 ProductsImport에 전달
$import = new ProductsImport($req, $user, $excelPath, $redirect, $startRow, $lastRowNumber);
$import->setHeaders($headers); // 헤더 정보 설정
```

### 2. 새로운 헤더 추가
```php
// config/excel-headers.php 파일에서 새로운 헤더 추가
'new_field' => [
    'NEW_HEADER', 'new_header', '새로운헤더', 'New Header'
],
```

### 3. 헤더 검증 규칙 수정
```php
// config/excel-headers.php 파일에서 검증 규칙 수정
'validation' => [
    'min_required_headers' => 5,    // 최소 필수 헤더 수
    'max_total_headers' => 20,      // 최대 헤더 수
    'max_header_length' => 50,      // 헤더명 최대 길이
],
```

## ⚠️ 에러 처리

### 1. 필수 헤더 누락
```
필수 헤더가 누락되었습니다: qaid, description
실제 헤더: QAID, CATE4, CATE5, 상품명, VENDOR_NAME
```

### 2. 헤더 수 오류
```
헤더 수가 너무 적습니다. 최소 5 개가 필요합니다.
헤더 수가 너무 많습니다. 최대 20 개까지 허용됩니다.
```

### 3. 헤더 형식 오류
```
헤더 형식이 올바르지 않습니다: 매우_긴_헤더_이름_50자를_초과하는_헤더
```

## 🔍 로그 정보

### 헤더 매핑 완료 로그
```json
{
    "column_indexes": {
        "qaid": 0,
        "cate4": 1,
        "cate5": 2,
        "description": 7,
        "vendor_name": 8
    },
    "headers": ["QAID", "CATE4", "CATE5", "LOT_FULL_NAME", "wmsSkuId", "externalSkuId", "BARCODE", "DESCRIPTION", "VENDOR_NAME"],
    "mapping_summary": {
        "total_headers": 9,
        "mapped_fields": 5,
        "required_fields_found": 5,
        "optional_fields_found": 0
    }
}
```

### 헤더를 찾을 수 없는 경우 경고 로그
```json
{
    "field": "wms_sku_id",
    "possible_headers": ["wmsSkuId", "wms_sku_id", "WMS_SKU_ID", "WMS SKU ID", "wmsSkuld"],
    "actual_headers": ["QAID", "CATE4", "CATE5", "LOT_FULL_NAME", "BARCODE", "DESCRIPTION", "VENDOR_NAME"]
}
```

## 🛠️ 문제 해결

### 1. 새로운 헤더 추가
1. `config/excel-headers.php` 파일 열기
2. 해당 필드의 배열에 새로운 헤더명 추가
3. 설정 파일 캐시 클리어: `php artisan config:clear`

### 2. 헤더 매핑 실패 시
1. 로그에서 실제 헤더 확인
2. 설정 파일에 해당 헤더 추가
3. 대소문자, 공백, 특수문자 확인

### 3. 성능 최적화
- 헤더 매핑은 한 번만 실행되므로 성능 영향 최소
- 매핑 결과는 메모리에 캐시됨

## 📈 장점

1. **유연성**: 헤더 변경 시 코드 수정 없이 설정 파일만 수정
2. **확장성**: 새로운 헤더 쉽게 추가 가능
3. **안정성**: 필수 필드 검증으로 데이터 무결성 보장
4. **다국어 지원**: 영어/한글 헤더 모두 지원
5. **오타 허용**: 일반적인 오타도 자동으로 처리

## 🔮 향후 개선 방안

1. **퍼지 매칭**: 유사한 헤더명 자동 매칭
2. **머신러닝**: 자주 사용되는 헤더 패턴 학습
3. **UI 관리**: 웹 인터페이스에서 헤더 매핑 관리
4. **버전 관리**: 헤더 매핑 설정 버전 관리 