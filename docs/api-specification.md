# API 명세서 (API Specification)

## 1. 개요

이 문서는 코너스톤 프로젝트 WMS의 RESTful API 명세를 정의합니다.

## 2. 기본 정보

- **Test URL**: `https://csp-api.freshlove.net`
- **Base URL**: `https://api.cnsprowms.com`
- **Content-Type**: `application/json`
- **인증 방식**: Laravel Sanctum (SPA 인증)
- **응답 형식**: JSON

## 3. 공통 응답 형식

### 3.1 성공 응답
```json
{
  "success": true,
  "data": {},
}
```

### 3.2 에러 응답
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "유효성 검사 실패",
    "details": {}
  }
}
```

## 4. 인증 관련 API

### 4.1 로그인
```
POST /wms/login
```

**요청 본문:**
```json
{
  "username": "username",
  "password": "password"
}
```

**응답:**
```json
// 성공 시(200)
{
  "authenticated": true
}

//실패 시(401)
{
  "authenticated": false
}
```

### 4.2 로그아웃
```
POST /wms/logout
```

**헤더:**
```
Authorization: Bearer {token}
```

### 4.3 사용자 정보 조회
```
GET /wms/user
```

**응답:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "company_id": 1,
      "role": "Employee",
      "position": "팀원",
      "username": "demo",
      "caps_id": "A0001",
      "name": "홍길동",
      "email": "<EMAIL>",
      "cellphone": "01000000000",
      "status": 1,
      "menu": {"..."},
      "login_at": "2025-07-07 08:02:30",
      "login_ip": "*********",
      "login_os": "windows 11",
      "created_at": "2024-07-07 08:02:30",
      "updated_at": "2024-07-07 08:02:30",
    }
  }
}
```

### 4.3 사용자 정보 수정
```
PUT /wms/user/profile
```

## 5. 점검 요청(Req) 관련 API

### 5.1 점검 요청 목록 조회
```
GET /wms/reqs?page=1&pageSize=20&status=active
```

**쿼리 파라미터:**
- `page`: 페이지 번호 (기본값: 1)
- `pageSize`: 페이지당 항목 수 (기본값: 20)
- `status`: 상태 필터 (active, completed, all)
- `date_from`: 시작 날짜 (YYYY-MM-DD)
- `date_to`: 종료 날짜 (YYYY-MM-DD)

### 5.2 점검 요청 생성 (관리자 전용)
```
POST /wms/reqs
```

**요청 본문(payload):**
```json
{
  "title": "애플 제품 점검 요청",
  "description": "반품된 애플 제품들의 점검을 요청합니다.",
  "product_type": "apple",
  "expected_count": 100,
  "due_date": "2024-12-31"
}
```

## 6. 상품 관리 API

### 6.1 상품 검색
```
GET /wms/products/search?q={검색어}&type={제품타입}
```

**쿼리 파라미터:**
- `q`: 검색어 (QAID, 제품명, 모델명)
- `type`: 제품 타입 (general, apple)
- `status`: 상태 필터
- `page`: 페이지 번호

### 6.2 QAID로 상품 조회
```
GET /wms/products/qaid/{qaid}
```

**응답:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "qaid": "QA20241201001",
    "product_name": "iPhone 15 Pro",
    "model": "A2849",
    "serial_number": "123456789",
    "status": "inspection_completed",
    "repair_fee": 50000,
    "created_at": "2024-12-01T10:00:00Z"
  }
}
```

## 7. 입고 검수 API

### 7.1 검수 상품 목록
```
GET /wms/inspections?status=pending&page=1
```

### 7.2 개별 검수 완료
```
PATCH /wms/inspections/pass
```

**요청 본문:**
```json
{
  "product_id": 1,
  "status": "passed",
  "notes": "정상 입고 확인"
}
```

## 8. 수리/점검 API

### 8.1 수리 내용 기록
```
POST /wms/repairs/store
```

**요청 본문:**
```json
{
  "product_id": 1,
  "symptom_id": 5,
  "process_id": 3,
  "grade_id": 2,
  "parts_used": [1, 2, 3],
  "notes": "화면 교체 완료",
  "repair_fee": 80000
}
```

### 8.2 구성품 목록 조회
```
GET /wms/repairs/parts?category_id=1
```

## 9. 출고 및 팔레트 관리 API

### 9.1 팔레트 리스트 조회
팔레트 목록을 조회합니다.

``` 
GET /wms/pallets/list
```

**쿼리 파라미터:**
- `status` (선택): 팔레트 상태 필터 (10: 등록, 20: 적재중, 30: 적재마감, 40: 출고완료, 90: 삭제)
- `beginAt` (선택): 검색 시작일 (YYYY-MM-DD)
- `endAt` (선택): 검색 종료일 (YYYY-MM-DD)
- `keyword` (선택): 검색 키워드
- `pageSize` (선택): 페이지당 항목 수 (기본값: 15)

**응답:**
``` json
{
  "success": true,
  "data": {
    "pallets": [
      {
        "id": 123,
        "status": 20,
        "status_name": "적재중",
        "location": {
          "id": 45,
          "code": "A-1-1-Q12X-1234",
          "place": "KR-ESCS"
        },
        "repair_grade": {
          "id": 1,
          "name": "A급",
          "code": "A"
        },
        "registered_at": "2024-01-15T09:30:00Z",
        "registered_user": {
          "id": 5,
          "name": "홍길동"
        },
        "product_count": 25,
        "memo": "메모 내용"
      }
    ]
  }
}
```

### 9.2 팔레트 엑셀 다운로드
팔레트 목록을 엑셀 파일로 다운로드합니다.

``` 
POST /wms/pallets/download
```

**요청 본문:**
``` json
{
  "palletCode": "A-1-1-Q12X-1234",
  "palletIds": 12,
  "status": 20,
  "beginAt": "2024-01-01",
  "endAt": "2024-01-31",
  "origin": "http://localhost:5173"
}
```

### 9.3 팔레트 일괄 출고
선택된 팔레트들을 일괄 출고 처리합니다.

``` 
POST /wms/pallets/exports
```

**요청 본문:**
``` json
{
  "pallet_ids": [123, 124, 125],
  "memo": "일괄 출고 처리"
}
```

**응답:**
``` json
{
  "success": true,
  "data": {
    "exported_count": 3,
    "failed_count": 0,
    "exported_pallets": [
      {
        "id": 123,
        "status": 40,
        "exported_at": "2024-01-15T14:30:00Z"
      }
    ]
  }
}
```

### 9.4 팔레트 출고 취소
출고된 팔레트들을 다시 적재마감 상태로 되돌립니다.

``` 
PUT /wms/pallets/rollback-exports
```

**요청 본문:**
``` json
{
  "pallet_ids": [123, 124, 125],
  "reason": "출고 취소 사유"
}
```

**응답:**
``` json
{
  "success": true,
  "data": {
    "rollback_count": 3,
    "failed_count": 0,
    "rollback_pallets": [
      {
        "id": 123,
        "status": 30,
        "exported_at": null
      }
    ]
  }
}
```

### 9.5 팔레트 출고일 저장
팔레트의 출고일을 저장합니다.

``` 
PUT /wms/pallets/save-export-date
```

**요청 본문:**
``` json
{
  "pallet_ids": [123, 124, 125],
}
```

**응답:**
``` json
{
  "success": true,
  "data": {
    "set_pallets": [
      {
        "id": 123,
        "status": 30,
        "exported_at": "2024-01-15T14:30:00Z"
      }
    ]
  }
}
```

## 9.6 팔레트 상품 관리

### 9.6.1 팔레트 상품 리스트 조회
특정 팔레트의 상품 목록을 조회합니다.

``` 
GET /wms/pallets/products
```

**쿼리 파라미터:**
- `pallet_id` (필수): 팔레트 ID
- `checked_status` (선택): 상품 검수상태 필터
- `search_type` (선택): 타입 필터
- `pageSize` (선택): 페이지당 항목 수 (기본값: 1000)

**응답:**
``` json
{
  "success": true,
  "data": {
    "pallet": {
      "id": 123,
      "status": 20,
      "location": {
        "code": "A-1-1-Q12X-1234"
      }
    },
    "items": [
      {
        "id": 456,
        "qaid": "Q123456",
        "name": "삼성 갤럭시 S24",
        "status": 40,
        "quantity": 1,
        "amount": 850000,
        "repair_grade": {
          "id": 1,
          "name": "A급",
          "code": "A"
        },
        "invoice1": 50000,
        "invoice2": 10000,
        "invoice3": 5000,
        "registered_at": "2024-01-15T09:30:00Z",
        "registered_user": {
          "id": 5,
          "name": "홍길동"
        },
        "memo": "상품 메모"
      }
    ],
    "meta": {
      "current_page": 1,
      "pageSize": 20,
      "total": 25,
      "last_page": 2
    }
  }
}
```

### 9.6.2 팔레트 상품 출고 검수
팔레트 상품의 출고 검수를 수행합니다.

``` 
PATCH /wms/pallets/products
```

**요청 본문:**
``` json
{
  "pallet_product_ids": [789, 790, 791],
  "inspection_result": "pass",
  "memo": "검수 완료"
}
```

**응답:**
``` json
{
  "success": true,
  "data": {
    "inspected_count": 3,
    "failed_count": 0,
    "inspected_products": [
      {
        "id": 789,
        "status": 50,
        "inspected_at": "2024-01-15T14:30:00Z"
      }
    ]
  }
}
```

### 9.6.3 팔레트 마감
팔레트를 마감하여 출고 대기 상태로 변경합니다.

``` 
PUT /wms/pallets/products/close
```

**요청 본문:**
``` json
{
  "pallet_id": 123,
  "memo": "팔레트 마감 처리"
}
```

**응답:**
``` json
{
  "success": true,
  "data": {
    "pallet_id": 123,
    "status": 30,
    "closed_at": "2024-01-15T16:00:00Z",
    "product_count": 25
  }
}
```

### 9.6.4 팔레트 재오픈
마감된 팔레트를 다시 적재 상태로 되돌립니다.

``` 
PUT /wms/pallets/products/open
```

**요청 본문:**
``` json
{
  "pallet_id": 123,
  "reason": "재오픈 사유"
}
```

**응답:**
``` json
{
  "success": true,
  "data": {
    "pallet_id": 123,
    "status": 20,
    "reopened_at": "2024-01-15T16:30:00Z"
  }
}
```

### 9.6.5 팔레트에서 상품 제외
팔레트에서 상품을 제외하여 다시 검수완료 상태로 되돌립니다.

``` 
PATCH /wms/pallets/products/exclude-from-pallet
```

**요청 본문:**
``` json
{
  "pallet_product_ids": [789, 790],
  "reason": "상품 제외 사유"
}
```

**응답:**
``` json
{
  "success": true,
  "data": {
    "excluded_count": 2,
    "excluded_products": [
      {
        "id": 789,
        "qaid": "Q123456",
        "status": 30,
        "excluded_at": "2024-01-15T17:00:00Z"
      }
    ]
  }
}
```

## 9.7 팔레트 상품 적재

### 9.7.1 적재 가능한 팔레트 리스트 조회
상품을 적재할 수 있는 팔레트 리스트를 조회합니다.

``` 
GET /wms/pallets/loaded
```

**쿼리 파라미터:**
- `level` (선택): 팔레트 층 코드 (예: Q12X)
- `column` (선택): 팔레트 열 코드 (예: 1234)

**응답:**
``` json
{
  "success": true,
  "data": {
    "level": "Q12X",
    "column": "1234",
    "items": [
      {
        "pallet_no": "Q12X-1234",
        "isLocation": true,
        "palletGradeCode": "A",
        "palletProdCount": 15,
        "palletRecentProducts": [
          "삼성 갤럭시 S24",
          "아이폰 15 Pro",
          "LG 울트라PC"
        ]
      }
    ]
  }
}
```

### 9.7.2 출고 팔레트 임시 코드 생성
팔레트가 위치할 임시 코드를 생성합니다.

``` 
GET /wms/locations/generate/code/{place}
```

**경로 파라미터:**
- (필수): 지리적 위치 (예: KR-ESCS) `place`

**응답:**
``` json
{
  "success": true,
  "data": {
    "level": "Q12X",
    "column": "1234",
    "code": "A-1-1-Q12X-1234",
    "place": "KR-ESCS"
  }
}
```

### 9.7.3 출고 팔레트 위치 설정
팔레트 위치를 설정합니다.

``` 
GET /wms/pallets/loaded/set/{place}/{code}
```

**경로 파라미터:**
- (필수): 지리적 위치 (예: KR-ESCS) `place`
- `code` (필수): 팔레트 코드 (예: A-1-1-Q12X-1234)

**응답:**
``` json
{
  "success": true,
  "data": {
    "isLocation": true,
    "palletGradeCode": "A",
    "palletProdCount": 15,
    "palletRecentProducts": [
      "삼성 갤럭시 S24",
      "아이폰 15 Pro",
      "LG 울트라PC"
    ]
  }
}
```

### 9.7.4 상품 QA ID 체크
팔레트에 적재할 상품의 QA ID를 확인합니다.

``` 
GET /wms/pallets/loaded/check-product/{qaid}
```

**경로 파라미터:**
- `qaid` (필수): 상품 QA ID (예: Q123456)

**응답:**
``` json
{
  "success": true,
  "data": {
    "product": {
      "id": 456,
      "qaid": "Q123456",
      "name": "삼성 갤럭시 S24",
      "status": 30,
      "amount": 850000,
      "quantity": 1,
      "req_at": "2024년 1월 10일",
      "brand": "삼성",
      "model": "갤럭시 S24",
      "duplicated": "N"
    }
  }
}
```

### 9.7.5 송장 정보 체크
상품의 수리 비용 및 송장 정보를 확인합니다.

``` 
GET /wms/pallets/loaded/check-invoice/{id}/{repair_type}
```

**경로 파라미터:**
- `id` (필수): 상품 ID
- `repair_type` (필수): 수리 타입 (1: 일반, 2: 애플, 3: 모니터)

**응답:**
``` json
{
  "success": true,
  "data": {
    "isAlreadyChecked": false,
    "message": "",
    "feeType": "price",
    "feeUnit": "won",
    "minValue": 0,
    "maxValue": 100000,
    "invoiceAmount": 50000,
    "feeRangeList": [
      {
        "amount": 30000,
        "range": "0원 초과 ~ 500000원 이하"
      },
      {
        "amount": 50000,
        "range": "500000원 초과 ~ 1000000원 이하"
      }
    ]
  }
}
```

### 9.7.6 기타 비용 조회
상품의 기타 비용을 조회합니다.

``` 
GET /wms/pallets/loaded/other-expenses/{type}/{code}
```

**경로 파라미터:**
- `type` (필수): 비용 타입
- `code` (필수): 상품 코드

**응답:**
``` json
{
  "success": true,
  "data": {
    "expenses": [
      {
        "id": 1,
        "name": "배송비",
        "amount": 3000,
        "type": "shipping"
      },
      {
        "id": 2,
        "name": "포장비",
        "amount": 2000,
        "type": "packaging"
      }
    ],
    "total": 5000
  }
}
```

### 9.7.7 팔레트에 상품 저장
팔레트에 상품을 저장합니다.

``` 
POST /wms/pallets/loaded/save-on-pallet
```

**요청 본문:**
``` json
{
  "location_place": "KR-ESCS",
  "location_code": "A-1-1-Q12X-1234",
  "product_id": 456,
  "qaid": "Q123456",
  "repair_product_id": 789,
  "memo": "상품 적재 완료"
}
```

**응답:**
``` json
{
  "success": true,
  "data": {
    "pallet_product": {
      "id": 1001,
      "pallet_id": 123,
      "product_id": 456,
      "repair_product_id": 789,
      "status": 10,
      "registered_at": "2024-01-15T10:30:00Z",
      "registered_user_id": 5,
      "quantity": 1,
      "amount": 850000,
      "invoice1": 50000,
      "invoice2": 10000,
      "invoice3": 5000,
      "memo": "상품 적재 완료"
    }
  }
}
```

**에러 응답 예시:**
``` json
{
  "success": false,
  "status": 400,
  "message": "등록된 상품(Q123456)이 없습니다."
}
```

``` json
{
  "success": false,
  "status": 400,
  "message": "조회된 상품(QAID=Q123456)은 이미 적재된 상품입니다."
}
```

## 10. 핵심 비즈니스 규칙 API

### 10.1 QAID 중복 관리 API
- `GET /wms/products/duplicate-check/{qaid}`: QAID 중복 검사
- `POST /wms/notifications/duplicate-alert`: 중복 알림 발송
- `GET /wms/products/duplicate-list`: 중복 제품 목록 조회

### 10.2 제품 이력 추적 API
- `GET /wms/products/{id}/history`: 제품별 전체 이력 조회
- `GET /wms/products/history/statistics`: 제품 이력 통계 조회
- `POST /wms/products/{id}/history`: 제품 이력 기록

### 10.3 직원 작업 로그 API
- `POST /wms/employees/{id}/work-log`: 작업 로그 기록
- `GET /wms/employees/{id}/work-log`: 직원별 작업 로그 조회
- `GET /wms/employees/productivity`: 직원별 생산성 분석

### 10.4 수리비 차등 관리 API
- `GET /wms/repair-costs/categories`: 카테고리별 수리비 기준 조회
- `POST /wms/repair-costs/calculate`: 제품별 수리비 자동 계산
- `PUT /wms/repair-costs/categories/{id}`: 카테고리별 수리비 기준 수정

### 10.5 구성품 자동 관리 API
- `GET /wms/components/usage-statistics`: 구성품 사용량 통계
- `POST /wms/components/auto-order`: 자동 발주 요청
- `GET /wms/components/price-updates`: 시세 변동 알림
- `PUT /wms/components/{id}/price`: 구성품 가격 업데이트

## 11. 설정 관련 API

### 11.1 직원 관리

#### 직원 목록 조회
```
GET /wms/settings/members?role=employee&page=1
```

#### 직원 생성 (관리자 전용)
```
POST /wms/settings/members
```

**요청 본문:**
```json
{
  "name": "김철수",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "employee",
  "department": "수리팀"
}
```

### 11.2 수리 등급 관리

#### 수리 등급 목록
```
GET /wms/settings/repairs/grades
```

#### 수리 등급 생성 (관리자 전용)
```
POST /wms/settings/repairs/grades
```

**요청 본문:**
```json
{
  "name": "A등급",
  "description": "최상급 수리",
  "base_fee": 100000,
  "color": "#28a745"
}
```

## 12. 에러 코드

| 코드 | 설명 | HTTP 상태 코드 |
|------|------|----------------|
| `UNAUTHORIZED` | 인증 실패 | 401 |
| `FORBIDDEN` | 권한 없음 | 403 |
| `NOT_FOUND` | 리소스 없음 | 404 |
| `VALIDATION_ERROR` | 유효성 검사 실패 | 422 |
| `DUPLICATE_QAID` | 중복 QAID | 409 |
| `INVALID_STATUS` | 잘못된 상태 변경 | 400 |

## 13. 권한 관리

### 13.1 역할별 권한

| 기능 | 관리자 | 직원 | 협력사 |
|------|--------|------|--------|
| 사용자 관리 | ✅ | ❌ | ❌ |
| 점검 요청 관리 | ✅ | ✅ | ❌ |
| 상품 검색 | ✅ | ✅ | ✅ |
| 입고 검수 | ✅ | ✅ | ❌ |
| 수리/점검 | ✅ | ✅ | ❌ |
| 출고 관리 | ✅ | ✅ | ❌ |
| 설정 관리 | ✅ | ❌ | ❌ |

### 13.2 API별 권한

각 API 엔드포인트에는 미들웨어를 통해 권한을 확인합니다:

- `AdminAccessOnly`: 관리자만 접근 가능
- `Authenticate`: 로그인한 사용자만 접근 가능
- `CarryoutManagerAccessOnly`: 출고 관리자만 접근 가능

## 14. API 버전 관리

현재 버전: v1

향후 호환성을 위해 API 버전을 URL에 포함할 예정:
```
GET /api/v1/wms/products/search
```

## 15. 테스트

### 15.1 API 테스트 방법

1. **Postman Collection**: API 테스트를 위한 Postman 컬렉션 제공
2. **Unit Tests**: 각 API 엔드포인트에 대한 단위 테스트 작성
3. **Integration Tests**: 전체 워크플로우에 대한 통합 테스트

### 15.2 테스트 데이터

개발 환경에서 사용할 테스트 데이터:
- 테스트 사용자 계정
- 샘플 제품 데이터
- 샘플 팔레트 데이터 