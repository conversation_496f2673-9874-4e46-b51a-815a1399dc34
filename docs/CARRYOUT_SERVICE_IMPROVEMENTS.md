# CarryoutService storeCarryout 메서드 개선 사항

## 개요
외주 반출 팔레트 등록/수정 기능의 `storeCarryout` 메서드를 개선하여 안정성과 유지보수성을 향상시켰습니다.

## 주요 개선 사항

### 1. 의존성 주입 개선
**기존 코드:**
```php
$tokenService = new TokenService;
```

**개선된 코드:**
```php
public function __construct(
    WorkStatusService $workStatusService,
    TokenService $tokenService
) {
    $this->workStatusService = $workStatusService;
    $this->tokenService = $tokenService;
}
```

**개선 효과:**
- 테스트 용이성 향상
- 의존성 관리 개선
- 단일 책임 원칙 준수

### 2. 역할 분담 명확화

#### FormRequest (데이터 검증)
```php
// app/Http/Requests/Carryout/StoreRequest.php
public function rules(): array
{
    return [
        'id' => 'nullable|integer|exists:carryouts,id',
        'carryout_at' => 'required|date',
        'status' => 'nullable|integer|in:' . Carryout::STATUS_CARRIED_OUT . ',' . Carryout::STATUS_CARRIED_IN . ',' . Carryout::STATUS_CANCELED,
        'memo' => 'nullable|string|max:1000',
    ];
}
```

**FormRequest가 담당하는 검증:**
- ✅ 필수 필드 검증
- ✅ 데이터 타입 검증
- ✅ 데이터 형식 검증 (날짜, 이메일 등)
- ✅ 길이 제한 검증
- ✅ 존재 여부 검증 (exists)
- ✅ 값 범위 검증 (in, between 등)

#### Service (비즈니스 로직 검증)
```php
private function validateExistingCarryout(Carryout $carryout): void
{
    // 취소된 Carryout은 수정 불가
    if ($carryout->status === Carryout::STATUS_CANCELED) {
        throw CarryoutException::invalidOperation('취소된 외주 반출 정보는 수정할 수 없습니다.');
    }

    // 이미 반입 완료된 Carryout은 수정 불가
    if ($carryout->status === Carryout::STATUS_CARRIED_IN) {
        throw CarryoutException::invalidOperation('이미 반입 완료된 외주 반출 정보는 수정할 수 없습니다.');
    }
}
```

**Service가 담당하는 검증:**
- ✅ 비즈니스 규칙 검증
- ✅ 상태 기반 검증
- ✅ 권한 기반 검증
- ✅ 복잡한 조건 검증
- ✅ 데이터베이스 상태 기반 검증

### 3. 중복 검증 제거

**제거된 중복 검증:**
```php
// ❌ 제거됨 - FormRequest에서 이미 처리
private function validateCarryoutData(array $data): void
{
    // 필수 필드 검증
    if (empty($data['carryout_at'])) {
        throw CarryoutException::invalidData('반출 날짜는 필수 입력 항목입니다.');
    }
    // ... 기타 중복 검증
}
```

**개선 효과:**
- ✅ 코드 중복 제거
- ✅ 성능 향상
- ✅ 유지보수성 개선
- ✅ 책임 분리 명확화

### 4. 추가 검증이 필요한 경우

#### FormRequest에 추가할 검증 예시
```php
// app/Http/Requests/Carryout/StoreRequest.php
public function rules(): array
{
    return [
        'id' => 'nullable|integer|exists:carryouts,id',
        'carryout_at' => 'required|date|after_or_equal:today', // 오늘 이후 날짜만 허용
        'status' => 'nullable|integer|in:' . Carryout::STATUS_CARRIED_OUT . ',' . Carryout::STATUS_CARRIED_IN . ',' . Carryout::STATUS_CANCELED,
        'memo' => 'nullable|string|max:1000|regex:/^[가-힣a-zA-Z0-9\s\-_.,!?()]+$/', // 특수문자 제한
    ];
}

// 커스텀 검증 규칙
public function withValidator($validator)
{
    $validator->after(function ($validator) {
        // 복잡한 조건 검증
        if ($this->input('status') == Carryout::STATUS_CANCELED && empty($this->input('memo'))) {
            $validator->errors()->add('memo', '취소 시에는 메모를 입력해야 합니다.');
        }
    });
}
```

#### Service에 추가할 비즈니스 로직 검증 예시
```php
// app/Services/CarryoutService.php
private function validateBusinessRules(array $data, User $user): void
{
    // 사용자 권한 검증
    if (!$user->can('manage_carryout')) {
        throw CarryoutException::insufficientPermission('외주 반출 관리 권한이 없습니다.');
    }

    // 동시성 검증
    if ($this->hasConcurrentModification($data['id'] ?? null)) {
        throw CarryoutException::concurrentModification('다른 사용자가 동시에 수정하고 있습니다.');
    }

    // 비즈니스 규칙 검증
    if ($this->isHoliday($data['carryout_at'])) {
        throw CarryoutException::invalidOperation('휴일에는 반출 처리가 불가능합니다.');
    }
}
```

### 5. 예외 처리 개선
**기존 코드:**
```php
} catch (Exception $e) {
    DB::rollBack();
    SimpleLogService::error('carryout', '외주반출 정보등록 실패', [], $e);
    throw $e;
}
```

**개선된 코드:**
```php
} catch (CarryoutException $e) {
    DB::rollBack();
    SimpleLogService::error('carryout', '외주반출 정보등록 실패 (비즈니스 로직)', [
        'user_id' => $user->id,
        'data' => $data
    ], $e);
    throw $e;
} catch (Exception $e) {
    DB::rollBack();
    SimpleLogService::error('carryout', '외주반출 정보등록 실패 (시스템 오류)', [
        'user_id' => $user->id,
        'data' => $data
    ], $e);
    throw $e;
}
```

**개선 효과:**
- ✅ 구체적인 예외 타입 구분
- ✅ 상세한 로그 정보 기록
- ✅ 사용자 친화적인 에러 메시지

### 6. 로그 기록 개선
**기존 코드:**
```php
SimpleLogService::info('carryout', '외주반출 정보등록 성공', ['carryout_id' => $carryout->id]);
```

**개선된 코드:**
```php
SimpleLogService::info('carryout', '외주반출 정보등록 성공', [
    'carryout_id' => $carryout->id,
    'user_id' => $user->id,
    'action' => $carryout->exists ? '수정' : '등록'
]);
```

**개선 효과:**
- ✅ 사용자 정보 추적 가능
- ✅ 작업 유형 구분 (등록/수정)
- ✅ 감사 추적 강화

## 역할 분담 가이드라인

### FormRequest에 추가해야 할 검증
- ✅ 데이터 형식 검증 (날짜, 이메일, URL 등)
- ✅ 길이 제한 검증
- ✅ 필수 필드 검증
- ✅ 값 범위 검증
- ✅ 정규식 패턴 검증
- ✅ 존재 여부 검증 (데이터베이스 조회 없이)
- ✅ 간단한 조건부 검증

### Service에 추가해야 할 검증
- ✅ 복잡한 비즈니스 규칙 검증
- ✅ 데이터베이스 상태 기반 검증
- ✅ 사용자 권한 검증
- ✅ 동시성 검증
- ✅ 외부 API 연동 검증
- ✅ 상태 기반 검증
- ✅ 복합 조건 검증

## 예외 클래스 확장

### CarryoutException에 추가된 메서드
```php
/**
 * 잘못된 작업 시도
 */
public static function invalidOperation(string $message): self

/**
 * 권한 부족
 */
public static function insufficientPermission(string $message): self

/**
 * 동시 수정 감지
 */
public static function concurrentModification(string $message): self
```

## 성능 및 안정성 개선

### 1. 트랜잭션 안정성
- ✅ 모든 데이터베이스 작업을 트랜잭션으로 보호
- ✅ 예외 발생 시 자동 롤백
- ✅ 데이터 일관성 보장

### 2. 메모리 효율성
- ✅ 불필요한 객체 생성 방지
- ✅ 의존성 주입으로 리소스 관리 개선
- ✅ 중복 검증 제거로 성능 향상

### 3. 코드 가독성
- ✅ 명확한 메서드 분리
- ✅ 단계별 주석 추가
- ✅ 일관된 코딩 스타일
- ✅ 역할 분담 명확화

## 테스트 고려사항

### 1. 단위 테스트
- ✅ 비즈니스 로직 검증 메서드 테스트
- ✅ 예외 처리 테스트
- ✅ FormRequest 검증 규칙 테스트

### 2. 통합 테스트
- ✅ 전체 플로우 테스트
- ✅ 데이터베이스 트랜잭션 테스트
- ✅ 로그 기록 테스트

## 마이그레이션 가이드

### 1. 기존 코드 호환성
- ✅ 기존 API 인터페이스 유지
- ✅ 기존 데이터 구조 호환
- ✅ 점진적 적용 가능

### 2. 배포 시 고려사항
- ✅ 데이터베이스 마이그레이션 불필요
- ✅ 기존 기능에 영향 없음
- ✅ 롤백 가능

## 결론

이번 개선을 통해 `storeCarryout` 메서드의 안정성과 유지보수성이 크게 향상되었습니다. 특히 **FormRequest와 Service 레이어의 역할 분담을 명확히 하여 중복 검증을 제거**하고, **비즈니스 로직 검증에만 집중**할 수 있게 되었습니다.

주요 개선점:
- ✅ 중복 검증 제거로 성능 향상
- ✅ 역할 분담 명확화
- ✅ 비즈니스 로직 검증 강화
- ✅ 구체적인 예외 처리
- ✅ 상세한 로그 기록 