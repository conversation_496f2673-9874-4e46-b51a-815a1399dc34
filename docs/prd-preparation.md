# 코너스톤 프로젝트 WMS PRD(Product Requirements Document)

## 1. 제품 개요 (Product Overview)
- **제품명**: 제품의 입고, 점검/수리, 출고등 업무 WMS
- **목적**: 반품된 제품의 점검/수리를 위한 일련의 업무를 효과적으로 보조하기 위한 앱을 제작하는 프로젝트
- **개발 방식**: 애자일 방법론 (Agile Method)
- **아키텍처**: 백엔드(<PERSON><PERSON>, <PERSON><PERSON> Scout(meilisearch), mariaDB) + 프론트엔드(Tauri Version 2, Svelte Version 5, Sveltekit, Tailwind CSS Version 3.x + daisyUI Version 4.x)
- **배포**: docker를 이용한 배포

## 2. 사용자 정의 (User Definition)
- **주요 사용자**: 사내 모든 직원 및 협력사 직원
- **사용자 시나리오**: 
  - WMS를 개발하고 시스템을 관리하는 시스템 관리자
  - 직원을 및 사내 모든 업무를 통괄하는 관리자
  - 실제 작업을 하는 직원(입고담당, 입고검수 담당, 점검/수리 담당, 출고 담당 등)
  - 협력사 직원
  - API 서버를 통한 로그인으로 개인 데이터 보호 필요
  - Tauri로 빌드한 webview 앱으로 접근
  - 특히 창고 담당(입고 검수 및 출고 담당)의 경우 웹으로 접속해서 팔레트 입출고 처리

## 3. 핵심 기능 요구사항 (Core Features)
- **중요**: 모든 제품에는 유일한 QAID가 부여(중복된 QAID가 있다면 관리자에게 통보 되어야 함)
### 3.1 인증 시스템
- Laravel Sanctum을 이용한 최소한의 로그인 기능
- 이메일 인증 불필요
- (관리자에 의한)직원 계정 생성 및 관리

### 3.2 입고 목록 관리
- 입고 목록 항목 생성
    - (필수)일반, 애플 제품을 분류할 수 있는 기능
    - 엑셀 파일 업로드 및 DB 저장
        - 중복 제품 검사
- 입고 목록 항목 조회
    - 검색기능
        - 입고 날짜(기간) 검색
        - 일반, 애플 분류 검색
- 입고 목록 항목 수정
- 입고 목록 항목 삭제
- 입고 목록 완료/미완료 상태 변경
- 목록 엑셀 출력

### 3.3 입고 검수 관리
- 입고된 제품이 실제 입고 되었는지, 기존 상품과 중복된 제품은 없는지 등의 검수
    - 상품 검색 기능
- 제품의 입고/미입고/중복 등 상태 변경
- 창고에 팔레트 입고(웹)
    - 바코드 스캔으로 입고 처리
    - 목록 엑셀 출력

### 3.4 점검/수리 관리
- 창고에서 팔레트 출고(웹)
    - 바코드 스캔으로 출고 처리
- 입고 검수 완료된 제품에 대한 점검 및 수리
    - 라벨 프린트 기능(프린터 제어 자체 내장 - tauri 플러그인 이용)
- 점검/수리 상태에 따른 수리비 책정
    - **수리비 관리 시스템**: 수리비 산정 시스템
    - **구성품 관리 시스템**: 수리에 필요한 구성품을 관리할 수 있는 시스템
    - **수리 증상, 처리 내용, 수리 등급 시스템**: 증상에 따른 처리내용, 처리 후 상태를 등급별로 나눠 자동으로 수리비를 산정
- 제품 검색 페이지(meilisearch 이용)

### 3.5 출고 관리
- 점검/수리 완료된 상품에 대한 출고 검수 및 실제 출고 처리
- 협력사(외주 수리)에 대한 반입 처리(수리비 확인)
- 검수/수리와 같은 프로세스로 수리비를 수정할 수 있는 기능 필요

### 3.6 설정
- 직원 관리
- 점검/수리 코드 설정
    - 증상 내용
    - 처리 내용
    - 수리 등급
- 수리비 설정
- 구성품 설정

### 3.7 관리
- 제품 수리 로그
- 직원별 수리 로그
- 근태 관리

## 4. 기술 스택 고려사항
### 4.1 백엔드(Laravel)
- API 서버 구조
- 데이터베이스 설계
- 인증/보안 처리

### 4.2 프론트엔드(Svelte)
- 사용자 인터페이스
- 상태 관리
- API 통신

## 5. 비기능적 요구사항 (Non-functional Requirements)
- **성능**: 응답시간, 동시 사용자 수
- **보안**: 사용자 데이터 보호, 인증
- **사용성**: 직관적인 UI/UX
- **확장성**: 추후 기능 추가 가능성

## 6. 제약사항 (Constraints)
- TDD 방법론을 이용한 구현
- MVP 방식으로 최소 기능부터 구현
- 복잡한 기능의 구현시 작은 함수로 분리하여 구현
- 이메일 인증 기능 제외
- 오류 발생시 디버깅을 위해 자세한 로깅 필수 구현

## 7. 성공 지표 (Success Metrics)
- 기본 CRUD 기능 정상 동작
- 사용자 인증 시스템 동작
- 프론트엔드-백엔드 연동 완료

## 8. 개발 우선순위
1. **Phase 1**: 백엔드 API 설계 및 구현
2. **Phase 2**: 프론트엔드 기본 UI 구현
3. **Phase 3**: 인증 시스템 구현
4. **Phase 4**: 통합 테스트 및 배포

## 9. 향후 확장 가능성
- 브로드캐스트를 활용한 알림 시스템
- 제품 Lock 기능(관리자가 점검/수리를 보류할 상품을 선택하여 출고 되지 않도록 보호)
- 태블릿 앱 지원

## 10. 기술적 고려사항
- RESTful API 설계
- 데이터베이스 스키마 설계
- 보안 토큰 관리
- 에러 처리 방식
- 로깅 및 모니터링