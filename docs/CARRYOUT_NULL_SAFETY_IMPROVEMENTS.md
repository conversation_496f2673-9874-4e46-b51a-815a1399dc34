# CarryoutService Null Safety 개선 사항

## 개요
CarryoutService의 `updateProductExport` 메서드에서 발생할 수 있는 null pointer exception 위험을 제거하고, 안전한 검증 로직을 구현했습니다.

## 주요 개선 사항

### 1. 위험한 코드 패턴 제거
**기존 문제점:**
```php
// 위험: Product::where()->first()가 null을 반환할 수 있음
$isCarryoutProduct = CarryoutProduct::where('product_id', 
    Product::where('qaid', mb_strtoupper($data['qaid']))->first()->id
)->exists();
```

**개선 후:**
```php
// 안전: 단계별 검증으로 null safety 보장
$product = $this->findProduct($data['qaid']);
$this->checkCarryoutDuplicate($product);
```

### 2. 안전한 헬퍼 메서드 추가

#### `findProduct(string $qaid): Product`
- QAID로 상품을 안전하게 조회
- 상품이 존재하지 않으면 `ResourceNotFoundException` 발생
- 대소문자 구분 없이 조회 (`mb_strtoupper` 적용)

#### `checkCarryoutDuplicate(Product $product): void`
- 상품이 이미 외주 반출 처리되었는지 확인
- 중복 처리 시도 시 `CarryoutException::alreadyCarriedOut` 발생

#### `validateProductForExport(Product $product, string $qaid): void`
- 상품 상태가 외주 반출 가능한지 확인
- 잘못된 상태일 때 `CarryoutException::invalidProductStatus` 발생

#### `findProductByStatus(string $qaid, int $status): Product`
- QAID와 상태로 상품을 안전하게 조회
- 조건에 맞지 않으면 적절한 `CarryoutException` 발생

### 3. 예외 처리 개선
- 범용 `Exception` 대신 구체적인 `CarryoutException` 사용
- 일관된 오류 메시지 제공
- 적절한 HTTP 상태 코드와 함께 예외 발생

### 4. 메서드별 개선 내용

#### `updateProductExport` 메서드
```php
// 기존: 위험한 null pointer 패턴
$isCarryoutProduct = CarryoutProduct::where('product_id', 
    Product::where('qaid', mb_strtoupper($data['qaid']))->first()->id
)->exists();

// 개선: 안전한 단계별 검증
$product = $this->findProduct($data['qaid']);
$this->checkCarryoutDuplicate($product);
$this->validateProductForExport($product, $data['qaid']);
```

#### `updateProductImport` 메서드
```php
// 기존: 별도 null 체크 필요
$product = Product::where('qaid', mb_strtoupper($data['qaid']))
    ->where('status', Product::STATUS_CARRIED_OUT)
    ->first();
if ($product === null) {
    throw new Exception("...");
}

// 개선: 통합된 안전한 조회
$product = $this->findProductByStatus($data['qaid'], Product::STATUS_CARRIED_OUT);
```

## 요구사항 충족 확인

### 요구사항 1.1 ✅
- `updateProductExport`에서 존재하지 않는 QAID 조회 시 null 체크 없이 `first()->id` 호출하여 오류가 발생하지 않도록 수정
- `findProduct` 헬퍼 메서드로 안전한 조회 보장

### 요구사항 1.2 ✅
- `Product::where('qaid', $qaid)->first()`가 null을 반환할 때 적절한 예외 처리 구현
- `ResourceNotFoundException` 및 `CarryoutException` 사용

### 요구사항 1.3 ✅
- CarryoutProduct 중복 체크 시 Product가 존재하지 않는 경우를 먼저 검증
- `findProduct` → `checkCarryoutDuplicate` 순서로 안전한 검증

### 요구사항 1.4 ✅
- 데이터베이스 조회 결과가 null일 때 명확한 오류 메시지 제공
- `CarryoutException`의 구체적인 메시지 사용

## 테스트 커버리지
- `CarryoutServiceTest.php` 생성
- 모든 헬퍼 메서드에 대한 단위 테스트 작성
- 정상 케이스와 예외 케이스 모두 테스트

## 코드 품질 개선
- 명확한 메서드 이름과 주석
- 단일 책임 원칙 준수
- 일관된 예외 처리 패턴
- 타입 힌트 및 반환 타입 명시

## 결론
이번 개선으로 CarryoutService의 null safety가 크게 향상되었으며, 런타임 오류 발생 가능성이 현저히 줄어들었습니다. 모든 데이터베이스 조회 작업이 안전하게 처리되며, 적절한 예외 처리를 통해 시스템의 안정성이 보장됩니다.