# 코너스톤 프로젝트 WMS 문서

이 디렉토리는 코너스톤 프로젝트 WMS의 모든 문서를 포함합니다.

## 📋 문서 구조

```
.cursor/docs/
├── README.md                    # 문서 가이드 (현재 파일)
├── prd.md                       # 제품 요구사항 정의서
├── user-scenarios.md            # 사용자 시나리오
├── api-specification.md         # API 명세서
├── frontend.md                  # 프론트엔드 개발 가이드
├── checklist.md                 # 개발 체크리스트
├── prd-preparation.md           # PRD 작성 준비 문서
└── schemas/                     # 데이터베이스 스키마
    ├── user_model_schema.md     # 사용자 모델
    ├── req_model_schema.md      # 점검 요청 모델
    ├── repair_model_schema.md   # 수리 모델
    ├── pallet_model_schema.md   # 팔레트 모델
    ├── carryout_model_schema.md # 반출 모델
    └── board_model_schema.md    # 게시판 모델
```

## 🎯 문서별 목적

### 핵심 문서
- **[PRD](./prd.md)**: 제품의 전체적인 요구사항과 기능 정의
- **[사용자 시나리오](./user-scenarios.md)**: 실제 사용자가 어떻게 시스템을 사용하는지 상세한 시나리오
- **[API 명세서](./api-specification.md)**: 백엔드 API의 상세한 명세와 사용법

### 데이터베이스 설계
- **schemas/**: 각 도메인별 데이터베이스 모델과 관계 정의
- 각 스키마 문서는 해당 도메인의 테이블 구조, 관계, 제약조건을 포함

### 개발 가이드
- **[프론트엔드 가이드](./frontend.md)**: Svelte/SvelteKit 개발 가이드라인
- **[체크리스트](./checklist.md)**: 개발 진행 상황 추적 및 완료 항목 관리

## 📖 문서 읽기 순서

### 개발자용
1. **[PRD](./prd.md)** - 전체 시스템 이해
2. **[사용자 시나리오](./user-scenarios.md)** - 실제 사용 방법 이해
3. **[API 명세서](./api-specification.md)** - 백엔드 API 이해
4. **schemas/** - 데이터베이스 구조 이해
5. **[프론트엔드 가이드](./frontend.md)** - 프론트엔드 개발 시작

### 관리자용
1. **[PRD](./prd.md)** - 시스템 전체 개요
2. **[사용자 시나리오](./user-scenarios.md)** - 업무 프로세스 이해
3. **[체크리스트](./checklist.md)** - 개발 진행 상황 확인

## 🔄 문서 업데이트 규칙

### 문서 수정 시
1. **버전 관리**: 문서 상단에 수정 날짜와 버전 기록
2. **연관 문서 확인**: 수정 내용이 다른 문서에 영향을 주는지 확인
3. **리뷰**: 주요 변경사항은 팀 리뷰 후 반영

### 새 문서 추가 시
1. **README.md 업데이트**: 문서 목록에 새 문서 추가
2. **연관 문서 연결**: 관련된 기존 문서에 링크 추가
3. **템플릿 사용**: 기존 문서의 형식을 참고하여 일관성 유지

## 📝 문서 작성 가이드라인

### 공통 규칙
- **한국어 사용**: 모든 문서는 한국어로 작성
- **마크다운 형식**: GitHub 호환 마크다운 사용
- **명확한 제목**: 각 섹션의 제목은 명확하고 구체적으로 작성
- **코드 블록**: 코드 예시는 적절한 언어 태그 사용

### 기술 문서
- **구체적인 예시**: 추상적인 설명보다는 구체적인 예시 포함
- **에러 처리**: 가능한 에러 상황과 해결 방법 명시
- **성능 고려사항**: 성능에 영향을 주는 요소들 명시

### 사용자 문서
- **실제 시나리오**: 실제 업무 상황을 반영한 시나리오 작성
- **단계별 설명**: 복잡한 프로세스는 단계별로 분해하여 설명
- **예외 상황**: 예외 상황과 대응 방법 포함

## 🚀 빠른 시작

### 개발 환경 설정
1. 백엔드: Laravel + MariaDB + Meilisearch
2. 프론트엔드: Tauri + SvelteKit + Tailwind CSS
3. 배포: Docker + Docker Compose

### 주요 기능
- 반품 제품 입고/검수/수리/출고 관리
- QAID 기반 제품 추적 시스템
- 수리비 자동 계산 시스템
- 팔레트 기반 창고 관리
- 협력사 반출/반입 관리

## 📞 문의

문서 관련 문의사항이나 개선 제안이 있으시면 개발팀에 연락해주세요. 