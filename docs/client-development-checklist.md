# Vid.xlsx 업로드 시스템 클라이언트 개발 작업 리스트

## 🎯 1. API 엔드포인트 정보

- 작업할 파일은 src/routes/(app)/works/+page.svelte 파일입니다.
- 그 파일에서 입고 등록 버튼이 있는데 그 버튼 옆에 "링크등록" 버튼을 만들고, 그 버튼을 클릭하면 입고 등록 처럼 모달창이 뜨고 거기서 업로드 폼이 보였으면 합니다.
- 정상 업로드 되었다면 모달창이 닫히고 페이지는 새로 고침 되어야 합니다.

### **Vid.xlsx 파일 업로드**
```
POST /wms/req/vid/upload
Content-Type: multipart/form-data

Request:
- vid_file: Vid.xlsx 파일 (필수)

Response:
{
  "success": true,
  "message": "Vid.xlsx 파일이 성공적으로 업로드되었습니다.",
  "data": {
    "processed_rows": 150,      // 처리된 행 수
    "created_records": 120,     // 새로 생성된 레코드 수
    "skipped_records": 30       // 건너뛴 레코드 수 (중복)
  }
}

Error Response:
{
  "success": false,
  "message": "에러 메시지"
}
```

### **ProductLink 통계 조회**
```
GET /wms/req/vid/statistics

Response:
{
  "success": true,
  "data": {
    "total_links": 500,           // 전체 ProductLink 수
    "links_with_product_id": 450, // product_id가 있는 레코드 수
    "links_with_item_id": 400,    // item_id가 있는 레코드 수
    "complete_links": 380,        // product_id와 item_id 모두 있는 레코드 수
    "completion_rate": 76.0       // 완성도 (퍼센트)
  }
}
```

## 🔧 2. 파일 업로드 요구사항

### **파일 검증 규칙**
- **파일명**: 자유 (확장자만 중요)
- **파일 타입**: Excel 파일만 허용 (`.xlsx`, `.xls`)
- **파일 크기**: 최대 2MB
- **시트**: 첫 번째 시트만 사용 (시트명은 자유)
- **헤더 구조**: 1행은 헤더, 2행부터 데이터 시작

### **Excel 파일 구조**
```
| external_wms_sku_id | vendor_item_id | product_id | item_id |
|---------------------|----------------|------------|---------|
| EXT-001             | VENDOR-001     | PROD-001   | ITEM-001|
| EXT-002             | VENDOR-002     | PROD-002   | ITEM-002|
| EXT-003             | VENDOR-003     |            |         |
```

## 🎨 3. UI/UX 개발 작업

### **3.1 파일 업로드 컴포넌트**
- [ ] 파일 선택 버튼 (드래그 앤 드롭 지원)
- [ ] 파일명 표시 영역
- [ ] 파일 크기 표시
- [ ] 업로드 진행률 표시 (선택사항)
- [ ] 업로드 버튼 (파일 선택 후 활성화)

### **3.2 업로드 결과 표시**
- [ ] 성공/실패 메시지 표시
- [ ] 처리 통계 정보 표시
  - 처리된 행 수
  - 새로 생성된 레코드 수
  - 건너뛴 레코드 수 (중복)
- [ ] 에러 메시지 표시 (파일명 오류, 크기 초과 등)

### **3.3 통계 대시보드**
- [ ] 전체 ProductLink 수 표시
- [ ] 완성도 차트/게이지 (product_id + item_id 모두 있는 비율)
- [ ] 상세 통계 표시
  - product_id가 있는 레코드 수
  - item_id가 있는 레코드 수
  - 완전한 레코드 수

## 🔄 4. 업로드 플로우

### **4.1 기본 플로우**
1. 사용자가 Vid.xlsx 파일 선택
2. 파일 검증 (파일명, 크기, 타입)
3. 검증 실패 시 에러 메시지 표시
4. 검증 성공 시 업로드 버튼 활성화
5. 업로드 버튼 클릭 시 API 호출
6. 업로드 결과 표시
7. 성공 시 통계 정보 업데이트

### **4.2 에러 처리**
- [ ] 파일 크기가 2MB 초과인 경우
- [ ] Excel 파일이 아닌 경우
- [ ] 네트워크 오류
- [ ] 서버 오류

## 📱 5. 반응형 디자인

### **5.1 모바일 대응**
- [ ] 터치 친화적인 파일 선택 UI
- [ ] 작은 화면에서도 보기 좋은 통계 표시
- [ ] 모바일에서의 드래그 앤 드롭 처리

### **5.2 데스크톱 최적화**
- [ ] 드래그 앤 드롭 영역 표시
- [ ] 키보드 단축키 지원 (선택사항)
- [ ] 큰 화면에서의 효율적인 레이아웃

## 🎯 6. 사용자 경험 개선

### **6.1 시각적 피드백**
- [ ] 업로드 중 로딩 스피너
- [ ] 성공/실패 아이콘 표시
- [ ] 진행률 바 (대용량 파일의 경우)

### **6.2 사용자 안내**
- [ ] 파일 형식 안내 텍스트
- [ ] 업로드 전 체크리스트
- [ ] 도움말 툴팁

## ⚙️ 7. 기술적 구현 사항

### **7.1 파일 처리**
- [ ] FormData를 사용한 파일 업로드
- [ ] 파일 미리보기 (선택사항)
- [ ] 파일 크기 포맷팅 (KB, MB 단위)

### **7.2 상태 관리**
- [ ] 업로드 상태 관리 (idle, uploading, success, error)
- [ ] 통계 데이터 캐싱
- [ ] 에러 상태 관리

### **7.3 API 통신**
- [ ] axios 또는 fetch를 사용한 API 호출
- [ ] 에러 인터셉터 설정
- [ ] 타임아웃 설정

## 📊 8. 테스트 시나리오

### **8.1 정상 케이스**
- [ ] 올바른 Vid.xlsx 파일 업로드
- [ ] 중복 데이터가 포함된 파일 업로드
- [ ] 빈 셀이 포함된 파일 업로드

### **8.2 에러 케이스**
- [ ] 2MB 초과 파일 업로드
- [ ] Excel이 아닌 파일 업로드
- [ ] 네트워크 오류 상황

## 🚀 9. 배포 및 모니터링

### **9.1 배포 전 체크리스트**
- [ ] 모든 브라우저에서 테스트
- [ ] 모바일 디바이스 테스트
- [ ] 네트워크 오류 상황 테스트
- [ ] 대용량 파일 업로드 테스트

### **9.2 모니터링**
- [ ] 업로드 성공/실패율 추적
- [ ] 파일 크기별 처리 시간 모니터링
- [ ] 사용자 피드백 수집

## 📚 10. 문서화

### **10.1 개발자 문서**
- [ ] API 스펙 문서
- [ ] 컴포넌트 사용법
- [ ] 에러 코드 정의

### **10.2 사용자 가이드**
- [ ] 업로드 방법 안내
- [ ] 파일 형식 요구사항
- [ ] 문제 해결 가이드

## 🔗 11. 연관 시스템

### **11.1 상품 목록 업로드와의 연동**
- Vid.xlsx 업로드 후 상품 목록 업로드 시 `product_link_id` 자동 연결
- ProductLink 테이블의 `external_wms_sku_id`와 `vendor_item_id` 조합으로 매칭

### **11.2 데이터 플로우**
1. Vid.xlsx 업로드 → ProductLink 테이블에 데이터 저장
2. 상품 목록 업로드 → ProductLink에서 매칭되는 레코드 찾기
3. 매칭되는 레코드가 있으면 `product_link_id` 설정, 없으면 `null`

---

## 📝 참고사항

- 모든 API 응답은 JSON 형식으로 통일
- 에러 메시지는 사용자가 이해하기 쉽게 한글로 제공
- 파일 업로드 시 보안을 위해 파일명 검증 필수
- 대용량 파일 처리 시 사용자에게 적절한 피드백 제공 