# 🚀 Frontend 디렉토리 분석

## 🛠️ 기술 스택
- **프레임워크**: SvelteKit
- **스타일링**: TailwindCSS
- **빌드 도구**: Vite
- **데스크톱 앱**: <PERSON><PERSON> (Rust 기반)
- **패키지 관리자**: pnpm
- **언어**: TypeScript

## 🏗️ 주요 구조

### 📁 루트 파일
- `package.json`: 의존성 및 스크립트 정의
- `build.ps1`/`update.ps1`: PowerShell 빌드/업데이트 스크립트
- `vite.config.js`, `svelte.config.js`, `tailwind.config.js`: 각 도구 설정 파일
- `latest.json`: 업데이트 정보 관련 파일

### 📂 src/ 디렉토리
- **앱 진입점**: `app.html`, `app.pcss`, `app.d.ts`

#### 🔑 lib/
- **🔊 assets/**: 오디오 파일 (beep.wav, check_barcode.mp3 등)
- **🧩 components/**: UI 컴포넌트 모음
  - **Button/**: ExcelDownload, Print 등 버튼 컴포넌트
  - **Layouts/**: AppLayout, GuestLayout 등 레이아웃 컴포넌트
  - **Snippets/**: Barcode, DisplayKeyword 등 재사용 컴포넌트
  - **UI/**: 기본 UI 컴포넌트 (NavBar, Paginate 등)
- **💾 db/**: 프린터 데이터베이스 관리
- **📊 stores/**: Svelte 상태 관리 저장소 (barcodeScannerStore, boardStore 등)
- **🛠️ 유틸리티**: AxiosBackend.ts, BarcodeUtils.ts 등 헬퍼 기능

#### 🔀 routes/
- **파일 기반 라우팅** 구조
- **(app)/**: 로그인 후 접근 가능한 주요 기능
  - **📋 board/**: FAQ, 공지사항 관리
  - **📦 carryout/**: 반출 관련 기능
  - **📊 dashboard/**: 대시보드 화면
  - **⚙️ management/**: 출석, 위치, 팔레트, QAID 관리
  - **🔖 pallets/**: 팔레트 생성 및 관리
  - **⚙️ settings/**: 위치, 회원, 프린터, 프로세스, 수리 설정
  - **🔧 works/**: 검사, 수리, 검색 등 작업 관련 기능
- **🔒 login/**: 로그인 페이지
- **🖨️ print/**: 라벨, 팔레트, 창고 출력 기능

### 🦀 src-tauri/ 디렉토리
- Tauri 기반 데스크톱 앱 구현
- **Cargo.toml/Cargo.lock**: Rust 의존성 관리
- **tauri.conf.json**: Tauri 설정
- **capabilities/**: 앱 권한 설정 (default.json, desktop.json)
- **icons/**: 다양한 크기의 앱 아이콘 이미지

### 🗃️ static/ 및 vfs_fonts/
- **static/**: 정적 파일 (favicon.png)
- **🔤 vfs_fonts/**: PDF 생성에 사용되는 폰트 파일 (나눔, 맑은고딕, Roboto)

## ✨ 애플리케이션 특징
- 📦 창고 관리 시스템(WMS) 프론트엔드
- 📱 바코드 스캔 기능 내장
- 🔄 수리, 팔레트, 출고 관리 기능
- 💻 데스크톱 애플리케이션으로 배포 가능
- 🌏 다국어 지원 (한글 폰트 포함)

> ⚠️ 참고사항: 이 프로젝트는 SvelteKit과 Tauri를 활용한 모던 웹/데스크톱 하이브리드 애플리케이션입니다. 