# 문서 업데이트 체크리스트

## 새로운 기능 추가 시 필수 업데이트 문서

### 1. PRD 문서 업데이트
- [ ] 핵심 기능 요구사항에 새 기능 추가
- [ ] 중요 비즈니스 규칙이 있다면 별도 섹션으로 명시
- [ ] 기술 스택 변경사항 반영
- [ ] 비기능적 요구사항 업데이트 (필요시)

### 2. 사용자 시나리오 문서 업데이트
- [ ] 새 기능에 대한 사용자 시나리오 작성
- [ ] 예외 상황 및 에러 처리 시나리오 추가
- [ ] 성능 요구사항이 있다면 명시
- [ ] 사용자 역할별 시나리오 분류

### 3. API 명세서 업데이트
- [ ] 새 기능 관련 API 엔드포인트 추가
- [ ] 요청/응답 형식 정의
- [ ] 에러 코드 및 메시지 정의
- [ ] 권한 요구사항 명시
- [ ] API 테스트 예시 추가

### 4. 데이터베이스 스키마 업데이트
- [ ] 새 테이블 설계 및 문서화
- [ ] 기존 테이블 변경사항 반영
- [ ] 관계(Relationship) 정의
- [ ] 인덱스 및 제약조건 명시

### 5. 체크리스트 업데이트
- [ ] 새 기능 구현 체크리스트 추가
- [ ] 백엔드/프론트엔드 작업 분리
- [ ] 테스트 케이스 작성 체크리스트 추가
- [ ] 배포 관련 체크리스트 업데이트

### 6. 문서 간 일관성 검증
- [ ] 모든 문서에서 용어 통일성 확인
- [ ] API 엔드포인트 경로 일치성 확인
- [ ] 데이터베이스 필드명 일치성 확인
- [ ] 사용자 역할 및 권한 일치성 확인

### 7. 문서 품질 검증
- [ ] 맞춤법 및 문법 검토
- [ ] 기술적 정확성 검증
- [ ] 가독성 및 이해도 확인
- [ ] 예시 및 설명의 명확성 확인