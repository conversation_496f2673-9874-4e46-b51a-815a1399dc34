# 상태 관리 가이드

## Svelte Stores 구조
```typescript
// stores/palletStore.ts
export const palletStore = writable({
  pallets: [],
  loading: false,
  error: null,
  filters: {
    status: null,
    keyword: '',
    pageSize: 15
  }
});

// stores/barcodeStore.ts  
export const barcodeStore = writable({
  scannedCode: '',
  isScanning: false
});
```

## API 호출 패턴
```typescript
// lib/api/base.ts
export async function apiCall<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const response = await fetch(`/api${endpoint}`, {
    headers: {
      'Authorization': `Bearer ${getToken()}`,
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });

  if (!response.ok) {
    throw new ApiError(response.status, await response.text());
  }

  return response.json();
}
```
```

### 3. **에러 처리 가이드**
```markdown:.cursor/docs/client/error-handling.md
# 에러 처리 가이드

## 에러 타입별 처리
| 에러 코드 | 사용자 메시지 | 처리 방법 |
|-----------|---------------|-----------|
| 401 | "로그인이 필요합니다" | 로그인 페이지로 리다이렉트 |
| 403 | "권한이 없습니다" | 권한 부족 알림 표시 |
| 422 | "입력값을 확인해주세요" | 폼 검증 에러 표시 |
| 500 | "서버 오류가 발생했습니다" | 재시도 버튼 표시 |

## 네트워크 에러 처리
- 오프라인 상태 감지
- 로컬 캐시 활용
- 재시도 로직 구현
```

### 4. **성능 최적화 가이드**
```markdown:.cursor/docs/client/performance.md
# 성능 최적화 가이드

## 로딩 상태 관리
- 스켈레톤 UI 구현
- 무한 스크롤 vs 페이지네이션
- 이미지 지연 로딩

## 캐싱 전략
- API 응답 캐싱
- 브라우저 캐시 활용
- 서비스 워커 구현
```

### 5. **테스트 가이드**
```markdown:.cursor/docs/client/testing.md
# 테스트 가이드

## 단위 테스트
```typescript
// tests/palletStore.test.ts
import { describe, it, expect } from 'vitest';
import { palletStore } from '../stores/palletStore';

describe('PalletStore', () => {
  it('should update pallets list', () => {
    const mockPallets = [{ id: 1, name: 'Test Pallet' }];
    palletStore.update(state => ({ ...state, pallets: mockPallets }));
    
    get(palletStore).pallets.should.equal(mockPallets);
  });
});
```

## E2E 테스트
- 사용자 시나리오 기반 테스트
- 바코드 스캔 시뮬레이션
- API 모킹
```

### 6. **배포 가이드**
```markdown:.cursor/docs/client/deployment.md
# 배포 가이드

## 환경별 설정
- **개발**: `http://localhost:3000`
- **스테이징**: `https://staging-api.example.com`
- **프로덕션**: `https://api.example.com`

## 빌드 프로세스
```bash
# 개발 빌드
pnpm run dev

# 프로덕션 빌드
pnpm run build

# 데스크톱 앱 빌드
pnpm run tauri build
```
```

## 추천하는 문서 전달 순서

1. **API 명세서** (현재 `pallet-api.md`)
2. **UI/UX 가이드라인** (새로 생성)
3. **사용자 시나리오** (기존 `user-scenarios.md`)
4. **상태 관리 가이드** (새로 생성)
5. **에러 처리 가이드** (새로 생성)
6. **성능 최적화 가이드** (새로 생성)
7. **테스트 가이드** (새로 생성)
8. **배포 가이드** (새로 생성)

이렇게 하면 클라이언트 개발자가 **기술적 구현부터 사용자 경험까지** 모든 측면을 고려하여 개발할 수 있습니다.