# 테스트 가이드

## 단위 테스트
```typescript
// tests/palletStore.test.ts
import { describe, it, expect } from 'vitest';
import { palletStore } from '../stores/palletStore';

describe('PalletStore', () => {
  it('should update pallets list', () => {
    const mockPallets = [{ id: 1, name: 'Test Pallet' }];
    palletStore.update(state => ({ ...state, pallets: mockPallets }));
    
    get(palletStore).pallets.should.equal(mockPallets);
  });
});
```

## E2E 테스트
- 사용자 시나리오 기반 테스트
- 바코드 스캔 시뮬레이션
- API 모킹