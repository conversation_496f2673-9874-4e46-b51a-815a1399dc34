# 팔레트 관리 API - 클라이언트 명세서

## 개요
이 문서는 클라이언트(SvelteKit)에서 사용할 팔레트 관리 API의 명세서입니다.

## 기본 정보
- **Base URL**: `https://api.example.com`
- **인증**: <PERSON><PERSON> (Authorization 헤더)
- **응답 형식**: JSON
- **에러 응답**: HTTP 상태 코드 + JSON 에러 메시지

## 공통 응답 형식

### 성공 응답
```json
{
  "success": true,
  "data": { ... },
  "message": "처리 완료"
}
```

### 에러 응답
```json
{
  "success": false,
  "message": "에러 메시지",
  "errors": {
    "field": ["검증 에러 메시지"]
  }
}
```

## API 엔드포인트

### 1. 팔레트 목록 조회
**GET** `/wms/pallets/list`

#### 쿼리 파라미터
| 파라미터 | 타입 | 필수 | 설명 | 예시 |
|---------|------|------|------|------|
| status | number | ❌ | 팔레트 상태 | 10 (등록), 20 (적재중), 30 (마감), 40 (출고완료) |
| beginAt | string | ❌ | 시작일 | "2024-01-01" |
| endAt | string | ❌ | 종료일 | "2024-01-31" |
| keyword | string | ❌ | 검색 키워드 | "A1-B2" |
| pageSize | number | ❌ | 페이지 크기 (기본값: 15) | 20 |

#### 응답 예시
```json
{
  "success": true,
  "data": {
    "pallets": {
      "data": [
        {
          "id": 123,
          "pallet_number": "ABC123-DEF456",
          "status": 20,
          "status_name": "적재중",
          "location": {
            "id": 1,
            "name": "A1-B2-C3"
          },
          "product_count": 15,
          "product_amount": 750000,
          "registered_at": "2024-01-15T10:30:00Z",
          "registered_user": {
            "id": 1,
            "name": "홍길동"
          }
        }
      ],
      "current_page": 1,
      "per_page": 15,
      "total": 100,
      "last_page": 7
    }
  }
}
```

### 2. 팔레트 상세 조회
**GET** `/wms/pallets/{id}`

#### 경로 파라미터
| 파라미터 | 타입 | 설명 |
|---------|------|------|
| id | number | 팔레트 ID |

#### 응답 예시
```json
{
  "success": true,
  "data": {
    "pallet": {
      "id": 123,
      "pallet_number": "ABC123-DEF456",
      "status": 20,
      "status_name": "적재중",
      "location": {
        "id": 1,
        "name": "A1-B2-C3"
      },
      "repair_grade": {
        "id": 1,
        "name": "A등급",
        "code": "A"
      },
      "products": [
        {
          "id": 456,
          "product": {
            "id": 789,
            "qaid": "QA123456789",
            "name": "MacBook Pro 13인치"
          },
          "quantity": 1,
          "amount": 50000
        }
      ],
      "registered_at": "2024-01-15T10:30:00Z",
      "registered_user": {
        "id": 1,
        "name": "홍길동"
      }
    }
  }
}
```

### 3. 팔레트 생성
**POST** `/wms/pallets`

#### 요청 본문
```json
{
  "location_id": 1,
  "repair_grade_id": 1,
  "memo": "팔레트 생성 메모"
}
```

#### 응답 예시
```json
{
  "success": true,
  "data": {
    "pallet": {
      "id": 123,
      "pallet_number": "ABC123-DEF456",
      "status": 10,
      "status_name": "등록"
    }
  },
  "message": "팔레트가 성공적으로 생성되었습니다."
}
```

### 4. 팔레트 상품 적재
**POST** `/wms/pallets/loaded/save-on-pallet`

#### 요청 본문
```json
{
  "location_place": "A1-B2-C3",
  "location_code": "ABC123-DEF456",
  "product_id": 456,
  "qaid": "QA123456789",
  "repair_product_id": 789,
  "memo": "상품 적재 메모"
}
```

#### 응답 예시
```json
{
  "success": true,
  "data": {
    "pallet_item_id": 123
  },
  "message": "정상 처리 되었습니다."
}
```

## 상태 코드

### 팔레트 상태
| 코드 | 상태명 | 설명 |
|------|--------|------|
| 10 | 등록 | 팔레트가 생성된 상태 |
| 20 | 적재중 | 상품이 적재되고 있는 상태 |
| 30 | 마감 | 팔레트 적재가 완료된 상태 |
| 40 | 출고완료 | 팔레트가 출고된 상태 |

### 검수 상태
| 코드 | 상태명 | 설명 |
|------|--------|------|
| 10 | 미검수 | 출고 검수가 완료되지 않은 상태 |
| 20 | 검수완료 | 출고 검수가 완료된 상태 |

## 에러 코드

| HTTP 상태 코드 | 에러 코드 | 설명 |
|---------------|-----------|------|
| 400 | VALIDATION_ERROR | 입력값 검증 실패 |
| 401 | UNAUTHORIZED | 인증 실패 |
| 403 | FORBIDDEN | 권한 없음 |
| 404 | NOT_FOUND | 리소스를 찾을 수 없음 |
| 422 | UNPROCESSABLE_ENTITY | 비즈니스 로직 에러 |
| 500 | INTERNAL_SERVER_ERROR | 서버 내부 에러 |

## SvelteKit 사용 예시

### API 호출 함수
```typescript
// lib/api/pallets.ts
export async function getPalletList(params: {
  status?: number;
  beginAt?: string;
  endAt?: string;
  keyword?: string;
  pageSize?: number;
}) {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      searchParams.append(key, value.toString());
    }
  });

  const response = await fetch(`/wms/pallets/list?${searchParams}`, {
    headers: {
      'Authorization': `Bearer ${getToken()}`,
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error('팔레트 목록 조회 실패');
  }

  return response.json();
}
```

### 컴포넌트에서 사용
```svelte
<!-- +page.svelte -->
<script lang="ts">
  import { onMount } from 'svelte';
  import { getPalletList } from '$lib/api/pallets';

  let pallets = [];
  let loading = false;
  let error = null;

  onMount(async () => {
    try {
      loading = true;
      const response = await getPalletList({ pageSize: 20 });
      pallets = response.data.pallets.data;
    } catch (err) {
      error = err.message;
    } finally {
      loading = false;
    }
  });
</script>

{#if loading}
  <p>로딩 중...</p>
{:else if error}
  <p class="error">{error}</p>
{:else}
  {#each pallets as pallet}
    <div class="pallet-item">
      <h3>{pallet.pallet_number}</h3>
      <p>상태: {pallet.status_name}</p>
      <p>상품 수: {pallet.product_count}개</p>
    </div>
  {/each}
{/if}
```

## 주의사항

1. **인증**: 모든 API 호출 시 Bearer Token을 Authorization 헤더에 포함해야 합니다.
2. **에러 처리**: 네트워크 에러와 비즈니스 로직 에러를 구분하여 처리하세요.
3. **페이지네이션**: 목록 API는 페이지네이션을 지원하므로, 무한 스크롤이나 페이지 번호를 활용하세요.
4. **상태 관리**: 팔레트 상태 변경 시 실시간으로 UI를 업데이트하세요.