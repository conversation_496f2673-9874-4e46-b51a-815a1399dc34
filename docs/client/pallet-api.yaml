openapi: 3.0.0
info:
  title: 팔레트 관리 API
  version: 1.0.0
  description: 팔레트 상품 적재 및 출고 관리 API

paths:
  /wms/pallets/list:
    get:
      summary: 팔레트 목록 조회
      parameters:
        - name: status
          in: query
          schema:
            type: integer
            enum: [10, 20, 30, 40]
          description: 팔레트 상태
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 15
          description: 페이지 크기
      responses:
        '200':
          description: 성공
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PalletListResponse'