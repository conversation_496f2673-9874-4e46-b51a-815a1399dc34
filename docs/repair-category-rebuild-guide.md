# 수리 카테고리 재구성 가이드

## 문제 상황
- `cate4`, `cate5` 테이블이 변경됨
- `repair_categories`, `repair_fees`, `repair_fee_ranges`는 백업에서 복구됨
- `repair_categories`의 `cate4_id`, `cate5_id`가 현재 `cate4`, `cate5` 테이블과 맞지 않음
- `cate5`는 `cate4`에 종속적이어서 매칭이 복잡함
- `repair_fee_ranges`의 `repair_category_id`는 유지하되, `repair_categories`의 카테고리 정보만 업데이트 필요

## 해결 방법

### 1. 재구성 명령어 사용

#### 기본 사용법
```bash
# 1. 미리보기로 매핑 계획 확인
php artisan repair:rebuild-categories --dry-run

# 2. 실제 재구성 실행
php artisan repair:rebuild-categories

# 3. 강제 실행 (확인 없이 바로 실행)
php artisan repair:rebuild-categories --force
```

#### 매칭 전략 옵션
```bash
# 이름으로 정확히 매칭
php artisan repair:rebuild-categories --strategy=name

# ID로 매칭 (이름이 변경된 경우)
php artisan repair:rebuild-categories --strategy=id

# 자동 선택 (기본값) - 가장 유연한 매칭
php artisan repair:rebuild-categories --strategy=auto
```

### 2. 개선된 매칭 전략 설명

#### 3단계 매칭 프로세스

**1단계: cate4 매칭**
- 기존 `repair_categories`의 `cate4_id` 또는 `cate4` 이름으로 현재 `cate4` 테이블에서 매칭
- ID 매칭 → 이름 매칭 순서로 정확도 높은 매칭 우선

**2단계: cate4 하위의 모든 cate5 조회**
- 매칭된 `cate4`의 모든 하위 `cate5`들을 조회
- 기존에 4차 카테고리 1개에 5차 카테고리가 3개였다면, 현재도 3개의 `cate5`를 모두 찾음

**3단계: 기존 cate5와 새로운 cate5 1:1 매칭**
- 기존 `cate5` 이름으로 새로운 `cate5`들과 매칭
- **중요**: 이미 사용된 `cate5`는 재사용하지 않음
- 매칭이 실패한 경우, 사용되지 않은 `cate5` 중 첫 번째 사용
- 모든 `cate5`가 사용된 경우에만 첫 번째 `cate5` 재사용
- 각각의 `repair_category`에 대해 고유한 `cate5` 할당

#### 매칭 전략별 특징

- **name**: 정확한 이름 매칭만 사용
- **id**: ID 매칭만 사용 (현재는 name과 동일하게 처리)
- **auto**: ID 매칭 → 정확한 이름 매칭 → 유사한 이름 매칭 순서로 시도

### 3. 재구성 과정

#### 1단계: 현재 상황 분석
- `cate4`, `cate5`, `repair_categories`, `repair_fee_ranges` 개수 표시
- 깨진 관계 개수 확인

#### 2단계: 매핑 계획 생성
- 4단계 매칭 프로세스로 매칭 시도
- 각 단계별 매칭 결과 기록
- 매칭 방법 상세 표시

#### 3단계: 매핑 계획 표시
- 매핑할 수 있는 항목들을 테이블로 표시
- 각 항목의 매칭 방법과 단계 표시

#### 4단계: 재구성 실행
- `repair_categories`의 `cate4_id`, `cate5_id` 업데이트
- `repair_fee_ranges`의 `repair_category_id`는 유지
- 트랜잭션으로 안전하게 처리

### 4. 예시 출력

#### 미리보기 실행
```bash
$ php artisan repair:rebuild-categories --dry-run

🔧 수리 카테고리 재구성을 시작합니다...
📋 사용 전략: auto
📊 현재 상황 분석 중...

+-------------------+------+
| 항목              | 개수 |
+-------------------+------+
| cate4             | 50   |
| cate5             | 200  |
| repair_categories | 30   |
| repair_fee_ranges | 45   |
+-------------------+------+

⚠️  깨진 관계: 25개
🗺️  매핑 계획 생성 중...
📋 매핑 계획 (28개)

+----------------------+------------+------------+-------------+-------------+----------------------------------+
| repair_category_id   | 기존 cate4 | 기존 cate5 | 새 cate4_id | 새 cate5_id | 매칭 방법                        |
+----------------------+------------+------------+-------------+-------------+----------------------------------+
| 1                    | 노트북      | 삼성       | 5           | 15          | cate4_id로 매칭됨, cate5 이름으로 매칭됨 |
| 2                    | 노트북      | LG        | 5           | 16          | cate4_id로 매칭됨, 사용되지 않은 cate5 할당 |
| 3                    | 노트북      | HP        | 5           | 17          | cate4_id로 매칭됨, 사용되지 않은 cate5 할당 |
| 4                    | 데스크탑    | 삼성       | 7           | 28          | cate4_id로 매칭됨, cate5 이름으로 매칭됨 |
| 5                    | 모니터      | 대형       | 8           | 35          | cate4 이름으로 매칭됨, cate5 이름으로 매칭됨 |
+----------------------+------------+------------+-------------+-------------+----------------------------------+
```

#### 실제 실행
```bash
$ php artisan repair:rebuild-categories

🔧 수리 카테고리 재구성을 시작합니다...
📋 사용 전략: auto
📊 현재 상황 분석 중...
🗺️  매핑 계획 생성 중...
📋 매핑 계획 (28개)

위 계획대로 재구성을 진행하시겠습니까? (yes/no) [no]:
> yes

🔨 재구성 실행 중...
🔄 수정: repair_category_id 1 (cate4: 1 → 5, cate5: 10 → 15)
🔄 수정: repair_category_id 2 (cate4: NULL → 6, cate5: 12 → 22)
🔄 수정: repair_category_id 3 (cate4: 2 → 7, cate5: 13 → 28)
🔄 수정: repair_category_id 4 (cate4: 3 → 8, cate5: 14 → 35)
🔄 수정: repair_category_id 5 (cate4: 4 → 9, cate5: 15 → 42)
📊 재구성 결과: 28개 repair_categories 수정
✅ 수리 카테고리 재구성이 완료되었습니다!
```

### 5. 매칭 방법별 설명

#### "cate4_id로 매칭됨, cate5 이름으로 매칭됨"
- `cate4`는 ID로 정확히 매칭되고, `cate5`는 이름으로 매칭됨
- 가장 이상적인 매칭 결과

#### "cate4_id로 매칭됨, 사용되지 않은 cate5 할당"
- `cate4`는 ID로 매칭되었지만 `cate5` 이름 매칭이 실패
- 사용되지 않은 `cate5` 중 첫 번째를 할당

#### "cate4_id로 매칭됨, 모든 cate5 사용됨, 첫 번째 재사용"
- `cate4`는 ID로 매칭되었지만 모든 `cate5`가 이미 사용됨
- 첫 번째 `cate5`를 재사용

#### "cate4 이름으로 매칭됨, cate5 이름으로 매칭됨"
- `cate4`는 이름으로 매칭되고, `cate5`도 이름으로 매칭됨
- ID 매칭이 실패했지만 이름으로 성공한 경우

#### "cate4 이름으로 매칭됨, 사용되지 않은 cate5 할당"
- `cate4`는 이름으로 매칭되었지만 `cate5` 이름 매칭이 실패
- 사용되지 않은 `cate5` 중 첫 번째를 할당

#### "cate4 이름으로 매칭됨, 모든 cate5 사용됨, 첫 번째 재사용"
- `cate4`는 이름으로 매칭되었지만 모든 `cate5`가 이미 사용됨
- 첫 번째 `cate5`를 재사용

#### "cate4_id로 매칭됨, cate5 없음"
- `cate4`는 매칭되었지만 하위에 `cate5`가 없는 경우
- `cate5_id`는 `NULL`로 설정

#### "cate4 이름으로 매칭됨, cate5 없음"
- `cate4`는 이름으로 매칭되었지만 하위에 `cate5`가 없는 경우
- `cate5_id`는 `NULL`로 설정

#### "수동 확인 필요"
- `cate4` 매칭이 실패한 경우
- 수동으로 확인하여 매핑 필요

### 6. 주의사항

#### 데이터 백업
- 실행 전 반드시 데이터베이스 백업
- `--dry-run`으로 먼저 확인 후 실행

#### 매칭 품질
- auto 전략은 매칭률이 높지만 정확도가 떨어질 수 있음
- 중요한 데이터는 name 전략으로 정확한 매칭 권장

#### 수동 확인
- "수동 확인 필요"로 표시되는 항목은 별도 처리 필요
- 매칭 결과를 검토하여 필요시 수정

### 7. 문제 해결

#### 매칭이 안 되는 경우
1. `cate4`, `cate5` 테이블의 이름 확인
2. 다른 매칭 전략 시도
3. 수동으로 매핑 정보 확인

#### 매칭 품질 개선
1. name 전략으로 정확한 매칭 시도
2. 매칭되지 않는 항목 수동 처리
3. 필요시 cate4, cate5 테이블 정리

### 8. 검증

#### 재구성 후 확인
```bash
# 깨진 관계가 있는지 확인
php artisan repair:fix-categories --dry-run

# 수리비 조회 테스트
php artisan tinker
>>> App\Models\RepairCategory::with('cate4', 'cate5')->first();
```

#### 수동 검증
```sql
-- repair_categories와 cate4, cate5 관계 확인
SELECT rc.id, rc.cate4_id, rc.cate5_id, c4.name as cate4_name, c5.name as cate5_name
FROM repair_categories rc
LEFT JOIN cate4 c4 ON rc.cate4_id = c4.id
LEFT JOIN cate5 c5 ON rc.cate5_id = c5.id
WHERE c4.id IS NULL OR c5.id IS NULL;
```

## 지원
문제가 발생하면 다음 정보와 함께 문의해주세요:
- 실행한 명령어와 옵션
- 오류 메시지
- `storage/logs/laravel.log`의 관련 로그
- 매핑 계획 결과 