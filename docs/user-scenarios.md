# 사용자 시나리오 (User Scenarios)

## 1. 개요

이 문서는 코너스톤 프로젝트 WMS의 주요 사용자 시나리오를 정의합니다. 각 사용자 역할별로 실제 업무에서 시스템을 어떻게 사용하는지, 그리고 발생할 수 있는 예외 상황은 무엇인지 구체적으로 설명합니다.

---

## 2. 사용자 역할 및 책임

| 역할 | 담당 업무 | 주요 사용 기능 |
| --- | --- | --- |
| **시스템 관리자** | 시스템 전반의 기술적 관리 및 유지보수 | 시스템 설정, 데이터 백업/복구, 성능 모니터링 |
| **관리자** | WMS 운영 총괄, 직원/업무 관리, 최종 의사결정 | 대시보드, 통계/보고서, 직원 관리, 정책 설정 |
| **입고 담당자** | 반품/수리 제품의 물리적 입고 및 시스템 등록 | 입고 목록 관리, 입고 검수, 팔레트 적재 및 관리 |
| **수리 담당자** | 제품 점검, 고장 진단 및 수리 수행 | 제품 검색, 수리 내역 기록, 부품 사용 등록, 라벨 출력 |
| **출고 담당자** | 수리 완료품 출고 검수 및 협력사 반출/반입 관리 | 출고 목록 관리, 출고 검수, 협력사 반출/반입 처리 |
| **협력사 직원** | 외주 수리 업무 수행 | (제한된) 제품 검색, 수리 내역 기록 |

---

## 3. 핵심 사용자 시나리오

### 3.1 입고 프로세스

#### 시나리오 1: 신규 수리 제품 입고 등록

| 항목 | 설명 |
| --- | --- |
| **사용자** | 입고 목록 담당자 (이다윤) |
| **목표** | 수리 요청 업체로부터 받은 제품 목록(엑셀)을 시스템에 일괄 등록한다. |
| **사전 조건** | - 시스템에 로그인된 상태<br>- 수리 요청 업체로부터 제품 목록(엑셀 파일)을 수령한 상태 |
| **진행 단계** | 1. '입고 관리' 메뉴에서 '입고 등록' 화면(모달창)을 띄운다.<br>2. 입고일, 입고 사유 등 기본 정보를 입력한다.<br>3. 수령한 엑셀 파일을 업로드한다.<br>4. 시스템이 파일 내용을 분석하여 제품 목록을 임시 등록하고, 기존 데이터와 비교하여 중복 QAID를 검사한다.<br>5. 시스템이 중복 검사 결과를 알림으로 제공한다. (예: 중복 2건, 신규 98건)<br>6. '등록 완료' 버튼을 눌러 입고 목록 생성을 확정한다. |
| **성공 결과** | - 신규 제품들이 '입고 대기' 상태로 시스템에 등록된다.<br>- 중복된 제품은 별도 목록으로 분리되어 관리자에게 보고된다. |
| **예외 상황** | - 엑셀 파일 형식이 올바르지 않으면 오류 메시지를 표시하고 시스템 로그에도 상세하게 기록한다.<br>- 중복 QAID가 발견되면 관리자에게 알림(예: 텔레그램)을 전송한다. |
| **작업로그 기록** | 각 상품이 등록될 때 상품의 정보와 작업자를 기록한다. |

#### 시나리오 2: 중복 QAID 처리

| 항목 | 설명 |
| --- | --- |
| **사용자** | 관리자 (성재경) |
| **목표** | 시스템이 보고한 중복 QAID 제품에 대해 최종 처리를 결정한다. |
| **사전 조건** | - 시스템으로부터 중복 QAID 발생 알림을 받은 상태 |
| **진행 단계** | 1. 알림(텔레그램)을 통해 중복 QAID 발생을 인지한다.<br>2. 시스템의 '중복 제품 목록' 화면에서 상세 내역을 확인한다.<br>3. 원청 업체에 해당 사실을 보고하고 처리 방안을 논의한다.<br>4. 업체의 회신에 따라 중복된 데이터를 시스템에서 삭제하거나, 특정 조치를 취한다. |
| **성공 결과** | - 중복 데이터가 정리되어 데이터 정합성이 유지된다. |

### 3.2 입고 검수 프로세스

#### 시나리오 3: 입고 제품 실물 검수

| 항목 | 설명 |
| --- | --- |
| **사용자** | 입고 담당자 (김입고) |
| **목표** | 입고 목록의 제품과 실제 입고된 제품의 일치 여부를 확인하고 팔레트에 적재한다. |
| **사전 조건** | - '입고 대기' 상태인 입고 목록이 존재함<br>- 검수할 실물 제품들이 준비되어 있음 |
| **진행 단계** | 1. '입고 검수' 화면으로 이동하여 처리할 입고 목록을 선택한다.<br>2. 제품의 바코드(QAID)를 스캐너로 스캔한다.<br>3. 시스템이 해당 제품을 '검수 완료' 상태로 자동 변경한다.<br>4. 검수 완료된 제품을 물리적 팔레트에 적재한다.<br>5. 모든 제품 검수가 끝나면, 팔레트 바코드를 스캔하여 '창고 입고' 처리한다.<br>6. 마지막으로 '검수 완료' 버튼을 눌러 전체 프로세스를 종료한다. |
| **성공 결과** | - 입고 목록의 모든 제품이 '검수 완료' 또는 '미입고' 상태로 변경된다.<br>- 검수된 제품이 적재된 팔레트가 창고에 입고 처리된다. |
| **작업로그 기록** | 각 상품이 검수될 때 상품의 상태와 작업자를 기록한다. |

#### 시나리오 4: 미입고 제품 처리

> **미입고**: 입고 목록에는 존재하지만, 실제 검수 과정에서 실물이 확인되지 않은 제품을 의미합니다.

| 항목 | 설명 |
| --- | --- |
| **사용자** | 시스템 (자동), 관리자 (성재경), 팀장 |
| **목표** | 검수 과정에서 확인되지 않은 제품을 '미입고'로 자동 처리하고 관리자가 후속 조치를 한다. |
| **사전 조건** | - 입고 담당자가 '검수 완료' 버튼을 클릭한 시점 |
| **진행 단계** | 1. 시스템은 '검수 완료' 처리되지 않은 모든 제품을 자동으로 '미입고' 상태로 변경한다.<br>2. 시스템이 관리자에게 미입고 제품 목록을 알림(텔레그램)으로 전송한다.<br>3. 관리자는 해당 목록을 원청 업체에 전달하고 확인을 요청한다.<br>4. 업체의 회신에 따라 미입고 데이터를 시스템에서 삭제하거나 다른 상태로 변경한다. |
| **성공 결과** | - 누락된 제품 정보가 명확히 관리되어 재고의 정확성이 유지된다. |
| **작업로그 기록** | 미입고 상품의 정보와 작업자를 기록한다. |

### 3.3 수리/점검 프로세스

#### 시나리오 5: 제품 수리 작업

| 항목 | 설명 |
| --- | --- |
| **사용자** | 수리 담당자 (박기술) |
| **목표** | 창고에서 출고된 제품을 점검하고 수리 내역을 시스템에 기록한다. |
| **사전 조건** | - 수리할 제품이 창고에서 출고되어 작업대에 준비됨 |
| **진행 단계** | 1. '수리/점검' 화면에서 수리할 제품의 QAID를 검색하거나 스캔한다.<br>2. 제품의 상세 정보와 기존 수리 이력을 확인한다.<br>3. 진단 결과에 따라 '증상 코드', '처리 내용', '수리 등급'을 메뉴에서 선택한다.<br>4. 수리에 부품을 사용했다면 '구성품 선택'에서 사용한 부품을 추가한다.<br>5. 시스템이 설정된 기준에 따라 예상 수리비를 자동으로 계산하여 표시한다.<br>6. 박스 교체 등으로 라벨 재발행이 필요하면 '라벨 출력' 버튼을 누른다.<br>7. '작업 완료' 버튼을 눌러 모든 내역을 저장한다. |
| **성공 결과** | - 제품의 수리 내역, 사용 부품, 수리비가 시스템에 정확히 기록된다.<br>- 제품 상태가 '수리 완료'로 변경된다. |

#### 시나리오 6: 제품 수리 대기 작업

| 항목 | 설명 |
| --- | --- |
| **사용자** | 수리 담당자 (박기술) |
| **목표** | 수리 중 구성품이 부족한 경우 구성품이 채워지기 까지 대기한다. |
| **진행 단계** | 1. '수리/점검' 화면에서 수리할 제품의 QAID를 검색하거나 스캔한다.<br>2. 제품의 상세 정보와 기존 수리 이력을 확인한다.<br>3. 진단 결과에 따라 '증상 코드', '처리 내용'을 메뉴에서 선택한다.<br>3. 부족한 부품을 선택하기 등급을 대기로 선택하고 점검 대기 버튼을 클릭한다.<br>4. 구성품이 도착하면 시나리오 5로 돌아가 작업 완료 버튼을 눌러 모든 내역을 저장한다. |
| **성공 결과** | - 제품의 수리 내역, 사용 부품, 수리비가 시스템에 정확히 기록된다.<br>- 제품 상태가 '수리 완료'로 변경된다. |

### 3.4 출고 프로세스

#### 시나리오 7: 수리 완료 제품 출고

| 항목 | 설명 |
| --- | --- |
| **사용자** | 출고 담당자 (이출고) |
| **목표** | 수리가 완료된 제품들을 최종 검수하고 고객사로 출고 처리한다. |
| **사전 조건** | - '수리 완료' 상태인 제품들이 팔레트 단위로 준비됨 |
| **진행 단계** | 1. '출고 관리' 화면에서 출고할 팔레트의 바코드를 스캔한다.<br>2. 시스템에 해당 팔레트의 제품 목록과 각 제품의 수리비가 표시된다.<br>3. 목록과 실물을 대조하며 최종 출고 검수를 진행한다.<br>4. 이상이 없으면 '최종 출고' 버튼을 클릭한다. |
| **성공 결과** | - 제품 상태가 '출고 완료'로 변경된다.<br>- 출고 이력이 시스템에 기록된다. |

### 3.5 관리자 시나리오

#### 시나리오 8: 신규 직원 등록 및 관리

| 항목 | 설명 |
| --- | --- |
| **사용자** | 관리자 (최관리) |
| **목표** | 새로운 직원의 시스템 계정을 생성하고 적절한 권한을 부여한다. |
| **진행 단계** | 1. '설정 > 직원 관리' 메뉴로 이동한다.<br>2. '신규 직원 등록' 버튼을 클릭한다.<br>3. 직원의 이름, 아이디, 초기 비밀번호 등 정보를 입력한다.<br>4. 직원의 역할(예: 수리 담당자, 입고 담당자)에 맞는 권한 그룹을 선택한다.<br>5. '저장' 버튼을 눌러 계정 생성을 완료한다. |
| **성공 결과** | - 신규 직원이 자신의 계정으로 시스템에 로그인하고, 부여된 권한 내에서 업무를 수행할 수 있다. |

---

## 4. 핵심 비즈니스 규칙 시나리오

### 4.1 QAID 중복 감지 시나리오
**시나리오**: 입고 담당자가 중복 QAID가 포함된 엑셀 파일을 업로드
**기대 결과**: 
- 시스템이 중복 QAID를 즉시 감지
- 관리자에게 텔레그램/이메일 알림 발송
- 중복 제품은 별도 상태로 분리 관리
- 사용자에게 명확한 오류 메시지 표시

### 4.2 제품 이력 추적 시나리오
**시나리오**: 관리자가 특정 제품의 전체 처리 이력 조회
**기대 결과**:
- 입고부터 출고까지 모든 단계별 상태 표시
- 각 단계별 담당자, 처리 시간, 처리 내용 기록
- 통계 대시보드에서 제품별 처리 시간 분석 가능

### 4.3 직원 작업 로그 시나리오
**시나리오**: 관리자가 직원별 생산성 분석
**기대 결과**:
- 로그인/로그아웃 시간 자동 기록
- 작업별 처리 시간 및 완료율 측정
- 일별/주별/월별 생산성 리포트 생성

### 4.4 수리비 차등 적용 시나리오
**시나리오**: 수리 담당자가 24인치 모니터 수리비 산정
**기대 결과**:
- 모니터 카테고리 선택 시 인치별 가격 자동 적용
- 24인치 모니터 전용 수리비 기준 적용
- 구성품 사용량에 따른 추가 비용 자동 계산

### 4.5 구성품 자동 관리 시나리오
**시나리오**: 특정 구성품 재고 부족 발생
**기대 결과**:
- 시스템이 자동으로 재고 부족 감지
- 관리자에게 자동 발주 알림 발송
- 사용량 통계에서 해당 구성품의 인기도 표시
- 시세 변동 시 가격 업데이트 알림

## 5. 예외 상황 시나리오

### 5.1 시스템 오류

#### 시나리오 9: 네트워크 연결 끊김

| 항목 | 설명 |
| --- | --- |
| **상황** | 입고 검수를 위해 제품 바코드를 연속으로 스캔하던 중, 작업장의 네트워크 연결이 불안정해져 끊김 현상이 발생했다. |
| **대응 방안** | 1. **(시스템)** 네트워크 단절을 감지하고 오프라인 모드로 전환되었음을 알린다.<br>2. **(시스템)** 스캔된 데이터는 앱 내 로컬 데이터베이스에 임시 저장된다.<br>3. **(시스템)** 네트워크가 다시 연결되면, 로컬에 저장된 데이터를 서버와 자동으로 동기화한다.<br>4. **(시스템)** 만약 동기화에 실패하면, '미동기화 데이터' 목록을 생성하여 사용자가 수동으로 재시도할 수 있도록 안내하고 관리자에게 보고한다. |

#### 시나리오 10: 바코드 스캔 오류

| 항목 | 설명 |
| --- | --- |
| **상황** | 제품의 QAID 라벨이 훼손되어 바코드 스캐너가 인식하지 못한다. |
| **대응 방안** | 1. **(사용자)** 스캔 입력 필드에 QAID 번호를 직접 수동으로 입력한다.<br>2. **(사용자)** QAID를 알 수 없는 경우, 제품명이나 시리얼 번호 등 다른 정보로 검색하여 제품을 찾는다.<br>3. **(사용자)** 어떤 방법으로도 제품을 특정할 수 없다면, 해당 제품을 '확인 불가'로 따로 분류하고 관리자에게 보고하여 처리 방안을 결정한다. |

### 5.2 업무 예외

#### 시나리오 11: 수리 불가 제품 발견

| 항목 | 설명 |
| --- | --- |
| **상황** | 제품을 분해하여 점검한 결과, 메인보드 파손 등 수리가 불가능한 손상을 발견했다. |
| **대응 방안** | 1. **(사용자)** 제품 상태를 '수리 불가'로 변경한다.<br>2. **(사용자)** '메모' 란에 수리가 불가능한 구체적인 사유(예: 메인보드 침수)를 상세히 기록한다.<br>3. **(시스템)** '수리 불가' 상태로 변경되면 자동으로 관리자에게 보고된다.<br>4. **(관리자)** 보고 내용을 확인하고, 고객사와 협의 후 '폐기' 또는 '반송' 등 후속 조치를 지시한다. |

---

## 6. 성능 및 보안 시나리오

### 시나리오 12: 대용량 데이터 조회

| 항목 | 설명 |
| --- | --- |
| **상황** | 관리자가 분기 실적 보고를 위해 3개월간의 모든 수리 내역(약 50,000건)을 조회하고 엑셀로 다운로드하려고 한다. |
| **성능 요구사항** | - **데이터 조회**: 검색 조건 입력 후 5초 이내에 첫 페이지 데이터가 표시되어야 한다.<br>- **페이지네이션**: 다음 페이지로 이동 시 2초 이내에 로딩되어야 한다.<br>- **엑셀 다운로드**: 다운로드 시작 후 1분 이내에 파일 생성이 완료되어야 한다. (서버에서 비동기 생성 후 완료 시 다운로드 링크 제공 방식 권장) |

### 시나리오 13: 권한 없는 기능 접근 시도

| 항목 | 설명 |
| --- | --- |
| **상황** | '수리 담당자' 권한을 가진 직원이 URL을 직접 변경하여 '직원 관리' 페이지에 접근을 시도했다. |
| **보안 요구사항** | 1. **(시스템)** 서버는 해당 요청을 즉시 차단하고 '권한 없음(403 Forbidden)' 응답을 반환한다.<br>2. **(시스템)** 프론트엔드는 사용자에게 "이 페이지에 접근할 권한이 없습니다." 라는 명확한 메시지를 보여준다.<br>3. **(시스템)** 이 접근 시도는 보안 로그에 기록되며, 비정상적인 시도가 반복될 경우 관리자에게 알림을 보낸다. |
