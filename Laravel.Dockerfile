FROM php:8.3-fpm

ENV APP_ENV=local
ENV APP_DEBUG=true
ENV COMPOSER_ALLOW_SUPERUSER=1

WORKDIR /root

RUN apt update && apt install -y \
        libzip-dev \
        curl \
        zip \
        unzip \
        openssl \
        libssl-dev \
        libcurl4-openssl-dev \
        libfreetype6-dev \
        libicu-dev \
        libjpeg62-turbo-dev \
        libmagickwand-dev \
        libpng-dev \
        libwebp-dev \
        libmemcached-dev \
        supervisor && \
    apt-get clean && rm -rf /var/lib/apt/lists/* \
    docker-php-ext-install pdo_mysql bcmath exif intl soap && \
    docker-php-ext-configure zip && \
    docker-php-ext-install zip && \
    curl -L -o /usr/local/bin/pickle https://github.com/FriendsOfPHP/pickle/releases/latest/download/pickle.phar && \
    chmod +x /usr/local/bin/pickle && \
    docker-php-ext-enable opcache && \
    docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp && \
    docker-php-ext-install -j$(nproc) gd && \
    pecl channel-update pecl.php.net && \
    pickle install memcached && \
    docker-php-ext-enable memcached && \
    pickle install redis && \
    docker-php-ext-enable redis

# 사용자 및 그룹 설정
ARG user
ARG uid
# Create system user to run Composer and Artisan Commands
RUN useradd -G www-data,root -u $uid -d /home/<USER>
RUN mkdir -p /home/<USER>/.composer && \
    chown -R $user:$user /home/<USER>

# Set working directory
WORKDIR /var/www/html

EXPOSE 9000

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

COPY ./conf/supervisor/supervisord.conf /etc/supervisor/supervisord.conf
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/supervisord.conf", "-n"]

# 처음 설치할 때 사용하는 코드(위 2줄 주석 처리)
#COPY docker-entrypoint.sh /docker-entrypoint.sh
#ENTRYPOINT ["/docker-entrypoint.sh"]

# Switch to this user
USER $user