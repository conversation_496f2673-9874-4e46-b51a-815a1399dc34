# 코너스톤 프로젝트 WMS 작업 체크리스트

## 1. 프로젝트 초기 설정

### 1.1 개발 환경 구축
- [ ] Docker 환경 구축
  - [ ] Docker compose 설정 파일 작성
  - [ ] Laravel 서비스 컨테이너 구성
  - [ ] MariaDB 컨테이너 구성
  - [ ] Meilisearch 컨테이너 구성
- [ ] .env.example 파일 작성 및 환경 변수 관리
- [ ] README에 로컬 개발 환경 설정 상세 문서화
- [ ] 패키지 매니저 pnpm 설정 및 lockfile 고정

### 1.2 백엔드 초기 설정
- [ ] Laravel 프로젝트 생성
- [ ] 필요한 패키지 설치
  - [ ] Laravel Sanctum 설치 및 설정
  - [ ] Laravel Excel 설치 및 설정
  - [ ] Laravel Scout(meilisearch) 설치 및 설정
- [ ] 데이터베이스 연결 설정
- [ ] API 미들웨어 설정 (권한/인증)
- [ ] API 요청/응답 로깅 및 에러 로깅 설정
- [ ] CORS, HTTPS, Rate Limiting, 보안 설정

### 1.3 프론트엔드 초기 설정
- [ ] Tauri + SvelteKit 프로젝트 생성
- [ ] Tailwind CSS 설치 및 설정
- [ ] daisyUI 설치 및 설정
- [ ] API 통신 모듈 설정 (Fetch, 에러/로딩 처리)
- [ ] 상태 관리(Svelte Stores) 설정
- [ ] 타입 정의(TypeScript) 설정
- [ ] 반응형 레이아웃 및 접근성 기본 설정

## [🎯 우선순위 1: 동적 WorkStatus 관리 시스템 구현 (최우선)]
- [ ] [동적 WorkStatus 관리 시스템 구현](./checklist-00.md)

## 2. 인증 시스템 구현

### 2.1 백엔드 인증 시스템
- [ ] 사용자 모델 설정 
- [ ] 로그인 API 엔드포인트 구현 (`POST /wms/login`)
- [ ] 로그아웃 API 엔드포인트 구현 (`POST /wms/logout`)
- [ ] 현재 로그인 사용자 정보 조회 API 구현 (`GET /wms/user`)
- [ ] 사용자 정보 수정 API 구현 (`PUT /wms/user/profile`)
- [ ] 인증 미들웨어 구현 (권한별 접근 제한)
- [ ] 비밀번호 정책/해싱(bcrypt), 세션 만료, 토큰 관리
- [ ] 인증/권한 관련 에러 응답 및 로그 기록

### 2.2 프론트엔드 인증 시스템
- [ ] 로그인 페이지 UI 구현
- [ ] 로그인 폼 및 유효성 검사 구현
- [ ] 인증 상태 관리 구현
- [ ] 인증 후 라우팅 처리
- [ ] 내 정보 수정 페이지 구현
- [ ] 권한 없는 접근 시 UX 처리 및 안내 메시지

## 3. 입고 목록 관리 기능

### 3.1 백엔드 입고 목록 관리
- [ ] 입고 목록 데이터베이스 모델 구현
- [ ] 입고 목록 생성 API 구현 (엑셀 업로드, 중복 검사 포함)
- [ ] 입고 목록 조회 API 구현 (기간/분류별 검색)
- [ ] 입고 목록 수정/삭제 API 구현
- [ ] 입고 목록 상태 변경 API 구현 (완료/미완료)
- [ ] 엑셀 파일 업로드/파싱/중복 제품 검사 구현
- [ ] 엑셀 파일 출력 API 구현
- [ ] 중복 QAID 발생 시 관리자 알림(텔레그램 등) 및 로그 기록
- [ ] 예외 상황(엑셀 오류, 중복 등) 에러 응답 및 상세 로그 기록

### 3.2 프론트엔드 입고 목록 관리
- [ ] 입고 목록 페이지 UI 구현
- [ ] 입고 목록 생성 폼 구현 (엑셀 업로드)
- [ ] 입고 목록 조회/필터링 UI 구현
- [ ] 입고 목록 수정/삭제/상태 변경 UI 구현
- [ ] 엑셀 파일 업로드/다운로드 UI 구현
- [ ] 중복 QAID/엑셀 오류 등 예외 상황 UX 처리 및 안내

## 4. 입고 검수 관리 기능

### 4.1 백엔드 입고 검수 관리
- [ ] 검수 데이터베이스 모델 구현
- [ ] 검수 상품 목록 API 구현 (`GET /wms/inspections`)
- [ ] 개별/일괄 검수 완료 API 구현 (`PATCH /wms/inspections/pass` 등)
- [ ] 상품 검색 API 구현
- [ ] 검수 상태(입고/미입고/중복) 변경 로직 구현
- [ ] 미입고 자동 처리 및 관리자 알림(텔레그램 등)
- [ ] 검수/미입고/중복 등 작업 로그 기록

### 4.2 프론트엔드 입고 검수 관리
- [ ] 검수 상품 목록 페이지 UI 구현
- [ ] 상품 검색/스캔 UI 구현
- [ ] 개별/일괄 검수 처리 UI 구현
- [ ] 상태 변경 UI 구현 (입고/미입고/중복)
- [ ] 바코드 스캔 입력 처리 구현
- [ ] 미입고/중복 등 예외 상황 UX 처리 및 안내

## 5. 창고 팔레트 관리 기능

### 5.1 백엔드 창고 팔레트 관리
- [ ] 팔레트/팔레트 상품 데이터베이스 모델 구현
- [ ] 입고 상품 적재 API 구현 (`POST /wms/pallets/loaded/save-on-pallet`)
- [ ] 팔레트 목록 API 구현 (`GET /wms/pallets/list`)
- [ ] QAID로 상품 확인 API 구현 (`GET /wms/pallets/loaded/check-product/{qaid}`)
- [ ] 팔레트 번호 생성/위치 설정 API 구현
- [ ] 팔레트 저장/마감/재오픈 API 구현
- [ ] 팔레트 상품 목록/출고/제외 API 구현
- [ ] 팔레트 관련 예외(중복, 미등록 등) 에러 응답 및 로그 기록

### 5.2 프론트엔드 창고 팔레트 관리
- [ ] 입고 상품 적재/팔레트 목록/QAID 확인/번호 생성/저장/마감/재오픈 UI 구현
- [ ] 팔레트 상품 목록/출고/제외/바코드 스캔 UI 구현
- [ ] 예외 상황(중복, 미등록 등) UX 처리 및 안내

## 6. 점검/수리 관리 기능

### 6.1 백엔드 점검/수리 관리
- [ ] 수리/점검/구성품/카테고리 데이터베이스 모델 구현
- [ ] 수리/점검 내용 기록 API 구현 (`POST /wms/repairs/store`)
- [ ] 구성품/증상/처리과정/등급 코드/목록 API 구현
- [ ] QAID로 상품 확인 API 구현
- [ ] 수리비 자동 산정/조정 로직 구현
- [ ] 라벨 프린트(Tauri 플러그인) 연동
- [ ] 수리/점검/부품 사용/작업 로그 기록
- [ ] 수리 불가/대기 등 예외 처리 및 관리자 알림

### 6.2 프론트엔드 점검/수리 관리
- [ ] 팔레트 출고/수리/점검/구성품/증상/처리과정/등급/라벨 프린트 UI 구현
- [ ] 수리비 자동 산정/조정 UI 구현
- [ ] 수리 불가/대기 등 예외 상황 UX 처리 및 안내

## 7. 출고 검수(출고 팔레트) 관리 기능

### 7.1 백엔드 출고 검수 관리
- [ ] 팔레트 목록/엑셀 다운로드/출고/출고 취소/출고 날짜 저장 API 구현
- [ ] 팔레트 상품 목록/출고 검수/마감/마감 취소/제외/적재/위치 코드 설정 API 구현
- [ ] QAID/송장/기타 비용 확인 API 구현
- [ ] 팔레트/상품 출고/검수/마감/제외 등 작업 로그 기록
- [ ] 출고 검수/마감/제외 등 예외 처리 및 관리자 알림

### 7.2 프론트엔드 팔레트 관리
- [ ] 팔레트 목록/엑셀 다운로드/출고/출고 취소/출고 날짜 설정/상품 목록/출고 검수/마감/마감 취소/제외/적재/위치 코드/QAID/송장/기타 비용 UI 구현
- [ ] 예외 상황(출고 실패, 중복 등) UX 처리 및 안내

## 8. 반출(Carryout) 관리 기능

### 8.1 백엔드 반출 관리
- [ ] 반출/반입 데이터베이스 모델 구현
- [ ] 반출/반입/삭제/엑셀 다운로드/상품 목록/출고/입고 API 구현
- [ ] 반출/반입/삭제 등 작업 로그 기록
- [ ] 예외 상황(중복, 미등록 등) 에러 응답 및 관리자 알림

### 8.2 프론트엔드 반출 관리
- [ ] 반출/반입/삭제/엑셀 다운로드/상품 목록/출고/입고 UI 구현
- [ ] 예외 상황(중복, 미등록 등) UX 처리 및 안내

## 9. 게시판 관리 기능

### 9.1 백엔드 게시판 관리
- [ ] 게시판/공지사항/FAQ 데이터베이스 모델 구현
- [ ] 공지사항/FAQ CRUD API 구현
- [ ] 게시글/FAQ 작업 로그 기록
- [ ] 예외 상황(중복, 미등록 등) 에러 응답 및 관리자 알림

### 9.2 프론트엔드 게시판 관리
- [ ] 공지사항/FAQ 목록/생성/수정/상세 UI 구현
- [ ] 예외 상황(중복, 미등록 등) UX 처리 및 안내

## 10. 설정 관리 기능

### 10.1 직원 관리
- [ ] 백엔드: 직원 목록/생성/수정/삭제/복구/상세 API 구현 (권한 체크)
- [ ] 프론트엔드: 직원 목록/생성/수정/상세 UI 구현
- [ ] 권한 없는 접근 시 에러 응답 및 UX 처리

### 10.2 출퇴근 관리
- [ ] 백엔드: 출퇴근 목록/등록 API 구현
- [ ] 프론트엔드: 출퇴근 목록/등록 UI 구현

### 10.3 수리 등급 관리
- [ ] 백엔드: 수리 등급 목록 API 구현
- [ ] 백엔드: 수리 등급 생성 API 구현
- [ ] 백엔드: 수리 등급 상세 API 구현
- [ ] 백엔드: 수리 등급 수정 API 구현
- [ ] 백엔드: 수리 등급 삭제 API 구현
- [ ] 프론트엔드: 수리 등급 목록 페이지 UI 구현
- [ ] 프론트엔드: 수리 등급 생성 UI 구현
- [ ] 프론트엔드: 수리 등급 수정 UI 구현

### 10.4 처리 내용 관리
- [ ] 백엔드: 처리 내용 목록 API 구현
- [ ] 백엔드: 처리 내용 생성 API 구현
- [ ] 백엔드: 처리 내용 상세 API 구현
- [ ] 백엔드: 처리 내용 수정 API 구현
- [ ] 백엔드: 처리 내용 삭제 API 구현
- [ ] 백엔드: 처리 내용별 등급 목록 API 구현
- [ ] 백엔드: 처리 내용별 등급 동기화 API 구현
- [ ] 프론트엔드: 처리 내용 목록 페이지 UI 구현
- [ ] 프론트엔드: 처리 내용 생성 UI 구현
- [ ] 프론트엔드: 처리 내용 수정 UI 구현
- [ ] 프론트엔드: 처리 내용별 등급 관리 UI 구현

### 10.5 증상 내용 관리
- [ ] 백엔드: 증상 목록 API 구현
- [ ] 백엔드: 증상 생성 API 구현
- [ ] 백엔드: 증상 상세 API 구현
- [ ] 백엔드: 증상 수정 API 구현
- [ ] 백엔드: 증상 삭제 API 구현
- [ ] 백엔드: 증상별 처리 내용 목록 API 구현
- [ ] 백엔드: 증상별 처리 내용 동기화 API 구현
- [ ] 프론트엔드: 증상 목록 페이지 UI 구현
- [ ] 프론트엔드: 증상 생성 UI 구현
- [ ] 프론트엔드: 증상 수정 UI 구현
- [ ] 프론트엔드: 증상별 처리 내용 관리 UI 구현

### 10.6 점검코드 관리
- [ ] 백엔드: 프로세스 목록 API 구현
- [ ] 백엔드: 프로세스 생성 API 구현
- [ ] 백엔드: 프로세스 수정 API 구현
- [ ] 백엔드: 프로세스 삭제 API 구현
- [ ] 프론트엔드: 프로세스 목록 페이지 UI 구현
- [ ] 프론트엔드: 프로세스 생성 UI 구현
- [ ] 프론트엔드: 프로세스 수정 UI 구현

### 10.7 위치 설정
- [ ] 백엔드: 위치 목록 API 구현
- [ ] 백엔드: 위치 수정 API 구현
- [ ] 프론트엔드: 위치 목록 페이지 UI 구현
- [ ] 프론트엔드: 위치 수정 UI 구현

### 10.8 작업 상태 설정
- [ ] 백엔드: 작업 상태 목록 API 구현
- [ ] 백엔드: 작업 상태 생성 API 구현
- [ ] 백엔드: 작업 상태 수정 API 구현
- [ ] 백엔드: 작업 상태 삭제 API 구현
- [ ] 프론트엔드: 작업 상태 목록 페이지 UI 구현
- [ ] 프론트엔드: 작업 상태 생성 UI 구현
- [ ] 프론트엔드: 작업 상태 수정 UI 구현

### 10.9 수리비 설정
- [ ] 백엔드: 수리비 목록 API 구현
- [ ] 백엔드: 수리비 생성 API 구현
- [ ] 백엔드: 수리비 수정 API 구현
- [ ] 백엔드: 수리비 삭제 API 구현
- [ ] 백엔드: 수리 옵션 목록 API 구현
- [ ] 프론트엔드: 수리비 목록 페이지 UI 구현
- [ ] 프론트엔드: 수리비 생성 UI 구현
- [ ] 프론트엔드: 수리비 수정 UI 구현
- [ ] 프론트엔드: 수리 옵션 관리 UI 구현

### 10.10 구성품 관리
- [ ] 백엔드: 구성품 목록 API 구현
- [ ] 백엔드: 구성품 생성 API 구현
- [ ] 백엔드: 구성품 수정 API 구현
- [ ] 백엔드: 구성품 삭제 API 구현
- [ ] 프론트엔드: 구성품 목록 페이지 UI 구현
- [ ] 프론트엔드: 구성품 생성 UI 구현
- [ ] 프론트엔드: 구성품 수정 UI 구현

### 10.11 구성품 카테고리 관리
- [ ] 백엔드: 구성품 카테고리 목록 API 구현
- [ ] 백엔드: 구성품 카테고리 생성 API 구현
- [ ] 백엔드: 구성품 카테고리 수정 API 구현
- [ ] 백엔드: 구성품 카테고리 삭제 API 구현
- [ ] 프론트엔드: 구성품 카테고리 목록 페이지 UI 구현
- [ ] 프론트엔드: 구성품 카테고리 생성 UI 구현
- [ ] 프론트엔드: 구성품 카테고리 수정 UI 구현

## 11. 핵심 비즈니스 규칙 구현

### 11.1 QAID 중복 관리 구현
- [ ] 백엔드: QAID 중복 검사 로직 구현
- [ ] 백엔드: 중복 감지 시 알림 발송 시스템 구현
- [ ] 백엔드: 중복 제품 상태 관리 로직 구현
- [ ] 프론트엔드: 중복 QAID 업로드 시 오류 처리 UI 구현
- [ ] 프론트엔드: 중복 제품 목록 조회 UI 구현
- [ ] 테스트: QAID 중복 시나리오 테스트 케이스 작성 및 실행

### 11.2 제품 이력 추적 구현
- [ ] 백엔드: 제품 이력 테이블 설계 및 구현
- [ ] 백엔드: 각 단계별 이력 자동 기록 로직 구현
- [ ] 백엔드: 제품별 전체 이력 조회 API 구현
- [ ] 백엔드: 이력 통계 분석 API 구현
- [ ] 프론트엔드: 제품 이력 조회 UI 구현
- [ ] 프론트엔드: 이력 통계 대시보드 UI 구현
- [ ] 테스트: 제품 이력 추적 시나리오 테스트 케이스 작성 및 실행

### 11.3 직원 작업 로그 구현
- [ ] 백엔드: 직원 작업 로그 테이블 설계 및 구현
- [ ] 백엔드: 로그인/로그아웃 자동 기록 로직 구현
- [ ] 백엔드: 작업별 로그 자동 기록 로직 구현
- [ ] 백엔드: 직원별 생산성 분석 API 구현
- [ ] 프론트엔드: 직원 작업 로그 조회 UI 구현
- [ ] 프론트엔드: 생산성 분석 리포트 UI 구현
- [ ] 테스트: 직원 작업 로그 시나리오 테스트 케이스 작성 및 실행

### 11.4 수리비 차등 관리 구현
- [ ] 백엔드: 카테고리별 수리비 기준 테이블 설계 및 구현
- [ ] 백엔드: 제품별 수리비 자동 계산 로직 구현
- [ ] 백엔드: 수리비 차등 적용 API 구현
- [ ] 프론트엔드: 카테고리별 수리비 설정 UI 구현
- [ ] 프론트엔드: 수리비 자동 계산 결과 표시 UI 구현
- [ ] 테스트: 수리비 차등 적용 시나리오 테스트 케이스 작성 및 실행

### 11.5 구성품 자동 관리 구현
- [ ] 백엔드: 구성품 사용량 통계 테이블 설계 및 구현
- [ ] 백엔드: 자동 발주 로직 구현 (재고 임계값 기반)
- [ ] 백엔드: 구성품 시세 변동 감지 및 알림 로직 구현
- [ ] 백엔드: 구성품 가격 관리 API 구현
- [ ] 프론트엔드: 구성품 사용량 통계 UI 구현
- [ ] 프론트엔드: 자동 발주 설정 UI 구현
- [ ] 프론트엔드: 구성품 가격 관리 UI 구현
- [ ] 테스트: 구성품 자동 관리 시나리오 테스트 케이스 작성 및 실행

## 12. QAID 관리 기능

### 12.1 백엔드 QAID 관리
- [ ] QAID 히스토리 테이블 설계 (product_histories: 코드 기반, 인덱스/파티션 설계)
- [ ] 부품 이력 N:M 테이블 설계 (product_history_parts)
- [ ] 코드 테이블 설계 (symptoms, processes, grades, parts 등)
- [ ] 모든 작업(입고/검수/수리/출고 등) 이력 기록 로직 구현
- [ ] 코드 기반 이력 기록/검색/통계 API 구현 (LIKE 금지, 정확 일치/IN만 허용)
- [ ] 파티셔닝/아카이빙 정책 수립 및 적용
- [ ] QAID별 타임라인/상세 이력 조회 API 구현
- [ ] 증상/처리내용/등급/부품/담당자 등으로 검색/통계 API 구현
- [ ] QAID 히스토리 조회 프론트엔드 UI 구현

### [참고: 대용량 이력 추적 설계 원칙]
- 모든 검색 필드는 코드(정수/짧은 문자열)로 정규화, 별도 코드 테이블과 조인
- LIKE 검색 금지, 정확 일치/IN 검색만 허용
- 증상, 처리내용, 등급, 부품, 담당자 등은 모두 인덱스 적용
- 부품 등 N:M 관계는 별도 테이블로 관리
- 파티셔닝(월별/연도별 등) 또는 아카이빙으로 성능 유지
- 코드 테이블과 조인하여 한글명 등 표시
- 대용량 환경에서의 통계/분석/검색 성능을 최우선 고려

## 13. 테스트

### 13.1 백엔드 테스트
- [ ] 단위 테스트(TDD): 모델/컨트롤러/서비스/미들웨어/권한/에러/성능/보안
- [ ] API 엔드포인트 테스트 (Postman Collection 등)
- [ ] 대용량 데이터/권한/보안/성능 테스트
- [ ] 에러/예외/로그/알림 테스트

### 13.2 프론트엔드 테스트
- [ ] 컴포넌트/화면/상호작용/API 통신/권한/에러/로딩/UX 테스트
- [ ] 예외 상황(네트워크 단절, 바코드 오류 등) 시나리오 테스트

### 13.3 통합 테스트
- [ ] 백엔드-프론트엔드 통합/E2E 테스트 (실제 업무 시나리오 기반)
- [ ] 관리자/담당자/협력사 등 역할별 시나리오 테스트

## 14. 배포 및 운영

### 14.1 배포 준비
- [ ] Docker 이미지 빌드
- [ ] Docker Compose 설정 최적화
- [ ] 환경 변수 설정
- [ ] SSL 인증서 설정
- [ ] 운영/테스트/로컬 환경 분리 및 설정 관리
- [ ] CI/CD 파이프라인 구축
- 자동 테스트 실행
- 스테이징 배포
- 프로덕션 배포
- [ ] 모니터링 시스템
  - 애플리케이션 성능 모니터링
  - 에러 추적 시스템
  - 알림 시스템

### 14.2 배포 및 검증
- [ ] 로컬/테스트/운영 서버 배포 및 검증
- [ ] 통합 테스트/실서버 검증/롤백 계획 준비

### 14.3 모니터링 및 유지보수
- [ ] 로그 분석 도구
  - [ ] 중앙화 된 로그 수집
  - [ ] 로그 검색 및 분석
- [ ] 성능 분석 도구 및 모니터링
  - [ ] 응답시간 모니터링
  - [ ] 리소스 사용량 추적
- [ ] 에러 추적/백업 시스템 구축
- [ ] 장애 발생 시 15분 내 알림/자동 복구 테스트
- [ ] 보안 스캔/취약점 점검/정기 백업/복구 테스트

## 15. 문서화

### 15.1 기술 문서
- [ ] API/DB/아키텍처/배포/운영/보안/테스트 문서화
- [ ] 코드/문서 동기화 및 최신화

### 15.2 사용자 문서
- [ ] 관리자/사용자 매뉴얼/기능 가이드 작성
- [ ] 예외 상황/업무 시나리오/알림/로그 등 실제 업무 흐름 기반 문서화
- 