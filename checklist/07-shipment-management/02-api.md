# 출고 관리 - 백엔드 API 개발

## 1. 출고 팔레트 목록 API
- [ ] Route: GET /wms/pallets/list
- [ ] Service: PalletService::getShipmentList() 구현
- [ ] Repository: PalletRepository::fetchShipmentList() 구현
- [ ] 필터/페이징/정렬 기능 구현
- [ ] 권한/상태 체크 미들웨어 적용
- [ ] 응답 포맷(Resource) 정의
- [ ] Feature 테스트 작성

## 2. 팔레트 엑셀 다운로드 API
- [ ] Route: POST /wms/pallets/download
- [ ] 엑셀 생성/다운로드 서비스 구현
- [ ] 대용량/성능 최적화(청크 등)
- [ ] 권한/상태 체크
- [ ] 에러/예외 처리
- [ ] Feature 테스트 작성

## 3. 팔레트 출고/출고 취소 API
- [ ] Route: POST /wms/pallets/exports, PUT /wms/pallets/rollback-exports
- [ ] 출고/취소 서비스/리포지토리 구현
- [ ] 상태/권한/예외 처리
- [ ] 트랜잭션 처리
- [ ] Feature 테스트 작성

## 4. 팔레트 출고일 저장 API
- [ ] Route: PUT /wms/pallets/save-export-date
- [ ] 출고일 저장 서비스/리포지토리 구현
- [ ] 상태/권한/예외 처리
- [ ] Feature 테스트 작성

## 5. 팔레트 상품 목록/출고 검수/마감/제외 API
- [ ] Route: GET/PATCH/PUT /wms/pallets/products 등
- [ ] 상품별 출고 검수/마감/제외 서비스 구현
- [ ] 상태/권한/예외 처리
- [ ] Feature 테스트 작성

## 6. 기타
- [ ] API별 Request Validation 클래스 생성
- [ ] API별 예외/에러 응답 포맷 통일
- [ ] API별 성능/보안 요구사항(응답시간, 권한, 로깅 등) 반영 