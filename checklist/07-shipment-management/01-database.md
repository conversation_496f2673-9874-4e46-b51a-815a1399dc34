# 출고 관리 - 데이터베이스 설계

## 1. 테이블/마이그레이션
- [ ] pallets 테이블 출고 관련 필드 설계 및 마이그레이션
- [ ] pallet_products 테이블 출고 상태/검수/마감 필드 설계 및 마이그레이션
- [ ] 출고 이력 테이블(예: shipment_histories) 설계 및 마이그레이션
- [ ] 출고 관련 인덱스/파티셔닝 설계
- [ ] 외래키/관계(팔레트-상품-출고이력) 설정

## 2. Eloquent 모델
- [ ] Pa<PERSON><PERSON>, PalletProduct, ShipmentHistory 모델 생성 및 관계 설정
- [ ] 모델별 fillable/hidden/casts/protected 필드 정의

## 3. 단위 테스트
- [ ] Pallet/PalletProduct/ShipmentHistory 모델 단위 테스트
- [ ] 관계/인덱스/제약조건 테스트 