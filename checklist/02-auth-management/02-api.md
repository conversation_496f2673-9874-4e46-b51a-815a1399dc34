# 인증 및 권한 관리 - 백엔드 API 개발 (Service/Repository 패턴)

## 1. 로그인/로그아웃/프로필 API
- [ ] Route 설정: `POST /wms/login`, `POST /wms/logout`, `GET /wms/user`, `PUT /wms/user/profile`
- [ ] Request Validation 클래스 생성 (LoginRequest, UpdateProfileRequest)
- [ ] Service: AuthService::login(), logout(), getUser(), updateProfile() 구현
- [ ] Repository: UserRepository::findByUsername(), updateProfile() 구현
- [ ] 토큰 발급/폐기/갱신 로직 구현
- [ ] 세션 만료/자동 로그아웃 처리
- [ ] 비밀번호 정책/해싱(bcrypt) 적용
- [ ] 개인정보 암호화/복호화 처리
- [ ] 권한별 접근 제어 미들웨어 구현
- [ ] 응답 데이터 포맷 정의 (Resource 활용)
- [ ] 예외 및 에러 처리 (인증/권한/유효성/서버)
- [ ] Feature 테스트 작성 (Pest)

## 2. 권한/역할 관리 API
- [ ] Route 설정: `GET /wms/settings/members`, `POST /wms/settings/members` 등
- [ ] Request Validation 클래스 생성 (CreateMemberRequest 등)
- [ ] Service: MemberService::create(), getList(), update(), delete() 구현
- [ ] Repository: MemberRepository::store(), fetchList(), update(), delete() 구현
- [ ] 권한별 접근 제어 미들웨어 구현 (AdminAccessOnly 등)
- [ ] 응답 데이터 포맷 정의
- [ ] 예외 및 에러 처리 (권한/유효성/서버)
- [ ] Feature 테스트 작성

## 3. 기타
- [ ] 로그인/접근 로그 기록 API 구현
- [ ] Rate Limiting, CORS, HTTPS 적용
- [ ] API 응답 시간/성능 측정 및 로그 