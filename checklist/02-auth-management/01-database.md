# 인증 및 권한 관리 - 데이터베이스 설계 및 모델

## 1. 마이그레이션/테이블 설계

> **중요**: 마이그레이션 파일 위치: `app/database/migrations`<br>
> **중요**: SPA 로그인을 통한 인증으로 Bear Token 인증 사용하지 않음

- companies 테이블 마이그레이션: 회사
    - [x] 파일명: 2014_10_01_000000_create_companies_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 회사 식별자 | 기본키, 자동 증가 |
        | code | string | 거래코드 | Nullable |
        | name | string | 소속사 이름(영어) | unique |
        | kr_name | string | 소속사 이름(한글) | - |
        | logo_uri | string | 로고위치, 상대경로 | Nullable |
        | type | unsignedTinyInteger | 타입(0: 일반, 1: 거래처관리, 2: 상품분류관리, 3: 거래처및분류관리) | 기본값: 0 |
        | f_default | string | 사용여부 | 기본값: N |
        | description | text | 회사 설명 | Nullable |
        | status | unsignedTinyInteger | 상태(0: 유효, 9: 중지) | index, 기본값: 0 |
        | created_at | timestamp | 생성 시간 | - |
        | updated_at | timestamp | 수정 시간 | - |

- users 테이블 마이그레이션: 사용자
    - [x] 파일명: 2014_10_12_000000_create_users_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 직원 고유 식별자 | 기본키, 자동 증가 |
        | mem_no | unsignedInteger | 이전 프로그램 멤버 번호 | index, 기본값: 0 |
        | company_id | foreignId | 소속사 이름(영어) | 외래키(companies.id), onUpdate('cascade'), onDelete('set null'), Nullable |
        | role | string | 사용자 권한: Super-Admin, Admin, Manager(입고, 출고, 외주, 점검), Employee, Guest | 기본값: Guest |
        | username | string | 사용자 아이디 | unique |
        | caps_id | string | 캡스용 사용자  ID | unique, Nullable |
        | name | string | 사용자 이름 | index |
        | part | enum(['IT', '경영관리', '물류', '생산관리', '일반가전']) | IT, 경영관리, 물류, 생산관리, 일반가전 | 기본값: 일반가전 |
        | position | enum(['대표', '이사', '공장장', '과장', '팀장', '반장', '팀원']) | 대표, 이사ㅏ, 공장장, 과장, 팀장, 반장, 팀원 등 | 기본값: 팀원 |
        | email | string | 메일주소 | index, Nullable |
        | email_verified_at | datetime | - | Nullable |
        | cellphone | string | 연락처(휴대폰) | index, Nullable |
        | telephone | string | 연락처(기타) | Nullable |
        | status | unsignedTinyInteger | index, 사용자 상태(1: 활성, 2: 비활성(휴면), 3: 이용불가, 9: 삭제) | 기본값: 0 |
        | menu | json | 사용자 접근 가능 메뉴 | Nullable |
        | login_at | datetime | 마지막 로그인 시간 | Nullable |
        | login_ip | string | 마지막 로그인 IP | Nullable |
        | login_os | string | 마지막 로그인 OS | Nullable |
        | password | string | 비밀번호 | - |
        | remember_token | string | 토큰 | Nullable |
        | created_at | timestamp | 생성 시간 | - |
        | updated_at | timestamp | 수정 시간 | - |
        | deleted_at | timestamp | 삭제 시간 | - |

- user_attendances 테이블 마이그레이션: 사용자 근태 관리
    - [x] 파일명: 2014_10_12_000000_create_users_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 직원 고유 식별자 | 기본키, 자동 증가 |
        | user_id | foreignId | 사용자 인덱스 | 외래키(users.id), onUpdate('cascade') |
        | work_date | date | 근무일자 | index |
        | day_type | enum(['평일', '휴일']) | 근무일 명칭 | index, 기본값: 평일 |
        | clock_in | time | 출근 시간 | Nullable |
        | clock_out | time | 퇴근 시간 | Nullable |
        | is_late | boolean | 지각 여부 | 기본값: false |
        | late_hours | time | 지각 시간 | 기본값: 00:00:00 |
        | is_early_leave | boolean | 조퇴 여부 | 기본값: false |
        | early_leave_hours | time | 조퇴 시간 | 기본값: 00:00:00 |
        | regular_hours | time | 기본 근무 시간 | 기본값: 00:00:00 |
        | overtime_hours | time | 연장 근무 시간 | 기본값: 00:00:00 |
        | total_hours | time | 총 근무 시간 | 기본값: 00:00:00 |
        | created_at | timestamp | 생성 시간 | - |
        | updated_at | timestamp | 수정 시간 | - |
    - [x] 복합 unique 인덱스 설정
        - ['user_id', 'work_date']

## 2. 인덱스 및 제약조건 최적화
- [x] 각 테이블의 스키마 구조를 파악한 후 인덱스 및 제약조건 최적화

## 3. Eloquent 모델/관계
- 스키마를 바탕으로 한 모델간 관계 정의 (hasMany, belongsTo 등)
    - [x] Company 모델 생성
    - [x] User 모델 생성
    - [x] UserAttendance 모델 생성
- [x] 모델간 관계 정의 (hasMany, belongsTo 등)
- [x] 모델 단위 테스트 (Pest)

## 4. 샘플 데이터 시드
- [ ] 각 모델의 샘플 데이터 시드 작성
