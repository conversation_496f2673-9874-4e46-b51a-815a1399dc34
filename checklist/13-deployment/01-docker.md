# 배포 - Docker/Compose/이미지 관리

## 1. Dockerfile 작성/최적화
- [ ] 백엔드(<PERSON><PERSON>) 멀티스테이지 Dockerfile 작성
- [ ] 프론트엔드(<PERSON><PERSON>/SvelteKit) 멀티스테이지 Dockerfile 작성
- [ ] 이미지 빌드 최적화(캐시, 용량, 보안)
- [ ] .dockerignore 파일 작성

## 2. Docker Compose 설정
- [ ] docker-compose.yml 작성 (서비스별 분리)
- [ ] MariaDB, Meilisearch 등 외부 서비스 포함
- [ ] 환경별(로컬/테스트/운영) Compose 파일 분리

## 3. 이미지 빌드/푸시
- [ ] 태그 기반 이미지 빌드/푸시 자동화
- [ ] 레지스트리(예: AWS ECR, Docker Hub) 연동
- [ ] 이미지 취약점 스캔

## 4. 테스트
- [ ] 로컬 Docker 빌드/실행 테스트
- [ ] CI에서 Docker 빌드/테스트 자동화 