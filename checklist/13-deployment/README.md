# 배포(Deployment) 관리

## 📊 전체 진행 상황
- **백엔드**: 0% 완료
- **프론트엔드**: 0% 완료
- **테스트**: 0% 완료
- **전체**: 0% 완료

## 📁 세부 체크리스트
- [01-docker.md](./01-docker.md) - Docker/Compose/이미지 관리
- [02-env.md](./02-env.md) - 환경 변수/설정 관리
- [03-ci-cd.md](./03-ci-cd.md) - CI/CD 파이프라인
- [04-server.md](./04-server.md) - 서버/인프라/배포 자동화
- [05-backup.md](./05-backup.md) - 백업/복구/롤백
- [06-monitoring.md](./06-monitoring.md) - 모니터링/로깅/알림
- [07-security.md](./07-security.md) - 배포 보안/취약점 관리
- [08-testing.md](./08-testing.md) - 배포 전후 테스트/검증
- [09-issue.md](./09-issue.md) - 배포 관련 이슈 트래킹

## 🎯 이번 주 목표
- CI/CD 파이프라인 구축
- Docker 이미지 자동 빌드/배포

## 💥 주요 이슈
- 운영/테스트/로컬 환경 분리 필요
- 롤백 자동화 미구현 