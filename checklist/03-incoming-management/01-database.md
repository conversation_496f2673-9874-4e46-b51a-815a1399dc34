# 입고 목록 관리 - 데이터베이스 설계 및 모델

## 1. 마이그레이션/테이블 설계

> **중요**: 마이그레이션 파일 위치: `app/database/migrations`

- reqs 테이블 마이그레이션: 입고 목록
    - [x] 파일명: 2024_07_01_001110_create_reqs_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 입고 목록 고유 식별자 | 기본키, 자동 증가 |
        | req_at | date | 요청일 | - |
        | req_type | unsignedTinyInteger | 요청타입(1: 쿠팡-등록, 2: 쿠팡-애플, 9:미등록) | 기본값: 9 |
        | status | unsignedTinyInteger | 등록상태(10: 등록, 90: 취소) | 기본값: 10 |
        | user_id | foreignId | 등록자 인덱스 | 외래키(users.id), Nullable, onUpdate('cascade') |
        | checked_user_id | foreignId | 검수완료 직원 인덱스 | 외래키(users.id), Nullable |
        | checked_at | datetime | 검수완료일 | Nullable |
        | memo | text | 입고 메모 | Nullable |
        | total_count | unsignedInteger | 총 입고 개수 | 기본값: 0 |
        | created_at | timestamp | 생성 시간 | - |
        | updated_at | timestamp | 수정 시간 | - |

- req_counts 테이블 마이그레이션: 입고 목록 작업 카운터
    - [x] 파일명: 2024_07_01_001120_create_req_counts_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 입고 목록 카운터 고유 식별자 | 기본키, 자동 증가 |
        | req_id | foreignId | 입고 목록 인덱스 | onUpdate('cascade'), onDelete('cascade') |
        | undelivered | unsignedBigInteger | 미입고 개수 | 기본값: 0 |
        | unchecked | unsignedBigInteger | 입고(검수)대기 개수 | 기본값: 0 |
        | checked | unsignedBigInteger | 점검대기 개수 | 기본값: 0 |
        | carryout | unsignedBigInteger | 외주반출 수리중 개수 | 기본값: 0 |
        | waiting | unsignedBigInteger | 수리대기(구성품 신청) 개수 | 기본값: 0 |
        | repaired | unsignedBigInteger | 미입고 개수 | 기본값: 0 |
        | checkout | unsignedBigInteger | 미입고 개수 | 기본값: 0 |
        | exporting | unsignedBigInteger | 미입고 개수 | 기본값: 0 |
        | duplicated | unsignedBigInteger | 미입고 개수 | 기본값: 0 |
        | unlinked | unsignedBigInteger | 미입고 개수 | 기본값: 0 |
        | completed | unsignedBigInteger | 미입고 개수 | 기본값: 0 |
        | deleted | unsignedBigInteger | 미입고 개수 | 기본값: 0 |

- cate4 테이블 마이그레이션: 카테고리 4
    - [x] 파일명: 2024_07_01_001210_create_cate4_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 입고 목록 카운터 고유 식별자 | 기본키, 자동 증가 |
        | cate4_no | unsignedBigInteger | 정리하기 전 cate4(추후 삭제할 것) | Nullable |
        | name | string | 카테고리 이름 | - |

- cate5 테이블 마이그레이션: 카테고리 5
    - [x] 파일명: 2024_07_01_001220_create_cate5_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 입고 목록 카운터 고유 식별자 | 기본키, 자동 증가 |
        | cate5_no | unsignedBigInteger | 정리하기 전 cate5(추후 삭제할 것) | Nullable |
        | cate4_id | foreignId | 카테고리4 인덱스 | 외래키(cate4.id), onUpdate('cascade'), onDelete('cascade') |
        | name | string | 카테고리 이름 | - |

- product_vendors 테이블 마이그레이션: 상품 벤더
    - [x] 파일명: 2024_07_01_001230_create_product_vendors_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 상품 벤더 고유 식별자 | 기본키, 자동 증가 |
        | name | string | 벤더 이름 | unique |

- product_lots 테이블 마이그레이션: 상품 로트 번호
    - [x] 파일명: 2024_07_01_001240_create_product_lots_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 상품 로트 번호 고유 식별자 | 기본키, 자동 증가 |
        | name | string | 로트 번호 | unique |

- product_barcodes 테이블 마이그레이션: 상품 바코드
    - [x] 파일명: 2024_07_01_001250_create_product_barcodes_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 상품 바코드 고유 식별자 | 기본키, 자동 증가 |
        | barcode | string | 바코드 | - |
        | wms_sku_id | string | 관리 번호 | - |
        | external_wms_sku_id | string | 외부 관리 번호 | - |

- product_links 테이블 마이그레이션: 상품 쿠팡 링크 번호
    - [x] 파일명: 2024_07_01_001260_create_product_links_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 상품 링크 고유 식별자 | 기본키, 자동 증가 |
        | vendor_item_id | string | 공급처 아이템 ID | Nullable |
        | product_id | string | 쿠팡 상품 ID | Nullable |
        | item_id | string | 쿠팡 상품의 아이템 ID | Nullable |
    - [x] 복합 Unique 인덱스
        - ['vendor_item_id', 'product_id', 'item_id']
        - 인덱스 이름: product_links_unique

- products 테이블 마이그레이션: 상품
    - [x] 파일명: 2024_07_01_001270_create_products_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | id | bigint | 상품 고유 식별자 | 기본키, 자동 증가 |
        | req_id | foreignId | 점검 요청서 인덱스 | 외래키(reqs.id), onUpdate('cascade'), onDelete('cascade'), index |
        | qaid | string | 품질관리번호 | **index** |
        | barcode | string | 바코드 | index |
        | name | string | 상품명 | - |
        | cate4_id | foreignId | 카테고리4 인덱스 | 외래키(cate4.id), onUpdate('cascade'), onDelete('set null'), Nullable, index |
        | cate5_id | foreignId | 카테고리5 인덱스 | 외래키(cate5.id), onUpdate('cascade'), onDelete('set null'), Nullable, index |
        | quantity | unsignedMediumInteger | 상품 개수 | 기본값: 1 |
        | amount | unsignedInteger | 판매단가 | 기본값: 0 |
        | user_id | foreignId | 등록 직원 인덱스 | 외래키(users.id), Nullable |
        | status | unsignedTinyInteger | 상품 처리 상태<br>- 10: 수리대기중(창고)<br>- 19: 구성품 신청중(대기)<br>- 20: 수리/점검완료(창고)<br>- 30: 점검완료(적재중)<br>- 50: 반출중<br>- 70: 출고완료<br>- 80: 출고보류<br>- 90: 삭제 | 기본값: 10, index |
        | rg | enum('Y', 'N') | RG 상품(Y: RG상품, N: 일반상품) | 기본값: N, index |
        | duplicated | enum('Y', 'N') | 중복 QAID 여부 | 기본값: N, index |
        | checked_at | datetime | 검수일 | Nullable, index |
        | checked_status | unsignedTinyInteger | 입고 검수 여부(10-검수대기, 20-검수완료) | Nullable, index |
        | checked_user_id | foreignId | 검수한 직원 인덱스 | 외래키(users.id), Nullable, index |
        | product_lot_id | foreignId | Lots 인덱스 | 외래키(product_lots.id), onUpdate('cascade'), onDelete('set null'), Nullable |
        | product_vendor_id | foreignId | 공급처 인덱스 | 외래키(product_vendors.id), onUpdate('cascade'), onDelete('set null'), Nullable |
        | product_link_id | foreignId | 쿠팡 연결 인덱스 | 외래키(product_links.id), onUpdate('cascade'), onDelete('set null'), Nullable |
        | memo | text | 메모 | Nullable |
        | created_at | timestamp | 생성 시간 | index |
        | updated_at | timestamp | 수정 시간 | - |

- delete_logs 테이블 마이그레이션: 삭제된 내역들 저장
    - [x] 파일명: 2024_07_01_001300_create_delete_logs_table.php
    - [x] 스키마 구조
        | 컬럼명 | 데이터 타입 | 설명 | 제약 조건 |
        |--------|------------|------|-----------|
        | user_id | foreignId | 삭제한 직원 인덱스 | 외래키(users.id), onUpdate('cascade') |
        | deletable_type | string | 삭제된 모델(전체 경로 \App\Models\User 형식) | Nullable |
        | deletable_id | unsignedBigInteger | 해당 테이블의 인덱스 | - |
        | content | text | 삭제된 레코드의 값 | - |
        | ip | unsignedBigInteger | 삭제한 IP | Nullable |
        | created_at | timestamp | 생성 시간 | - |
        | updated_at | timestamp | 수정 시간 | - |
    - [x] 복합 Unique 인덱스
        - ['deletable_type', 'deletable_id']

## 2. 인덱스 및 제약조건 최적화
- [x] 각 테이블의 스키마 구조를 파악한 후 인덱스 및 제약조건 최적화

## 3. Eloquent 모델/관계
- 스키마를 바탕으로 한 모델간 관계 정의 (hasMany, belongsTo 등)
    - [x] Req 모델 생성
    - [x] ReqCount 모델 생성
    - [x] Cate4 모델 생성
    - [x] Cate5 모델 생성
    - [x] ProductVendor 모델 생성
    - [x] ProductLot 모델 생성
    - [x] ProductBarcode 모델 생성
    - [x] ProductLink 모델 생성
    - [x] Product 모델 생성
    - [x] DeleteLog 모델 생성
- [x] 모델간 관계 정의 (hasMany, belongsTo 등)
- 모델 단위 테스트 (Pest)
    - [ ] Req 모델 단위 테스트
        - [ ] 모델 생성/수정/삭제 테스트
        - [ ] 관계 정의 테스트 (hasMany, belongsTo)
        - [ ] 모델 속성 및 캐스팅 테스트
        - [ ] 모델 유효성 검사 테스트
    - [ ] ReqCount 모델 단위 테스트
        - [ ] 모델 생성/수정/삭제 테스트
        - [ ] 관계 정의 테스트 (belongsTo)
        - [ ] 카운터 업데이트 로직 테스트
    - [ ] Cate4/Cate5 모델 단위 테스트
        - [ ] 모델 생성/수정/삭제 테스트
        - [ ] 관계 정의 테스트 (hasMany, belongsTo)
        - [ ] 계층 구조 테스트
    - [ ] ProductVendor/ProductLot/ProductBarcode/ProductLink 모델 단위 테스트
        - [ ] 모델 생성/수정/삭제 테스트
        - [ ] 관계 정의 테스트
        - [ ] 유니크 제약조건 테스트
    - [ ] Product 모델 단위 테스트
        - [ ] 모델 생성/수정/삭제 테스트
        - [ ] 관계 정의 테스트 (belongsTo)
        - [ ] 상태 변경 로직 테스트
        - [ ] 중복 QAID 처리 테스트
    - [ ] DeleteLog 모델 단위 테스트
        - [ ] 모델 생성 테스트
        - [ ] 폴리모픽 관계 테스트
        - [ ] 삭제 로그 기록 테스트

## 4. 샘플 데이터 시드
- [ ] 각 모델의 샘플 데이터 시드 작성
    - [ ] Req 시드 데이터 생성
    - [ ] ReqCount 시드 데이터 생성
    - [ ] Cate4/Cate5 시드 데이터 생성
    - [ ] ProductVendor/ProductLot/ProductBarcode/ProductLink 시드 데이터 생성
    - [ ] Product 시드 데이터 생성
    - [ ] 시드 데이터 간 관계 정의