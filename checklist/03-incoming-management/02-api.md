# 입고 목록 관리 - 백엔드 API 개발 (Service/Repository 패턴)

## 1. 입고 목록 생성 API (엑셀 업로드/비동기 처리)
- [ ] Route 설정: `POST /wms/reqs`
    - 요청 타입: multipart/form-data
    - 요청 필드
        - req_at (string, 필수)
        - req_type (int, 필수)
        - memo (string, 선택)
        - excel (file, 필수, 엑셀 파일)
    - 요청 예시
        ```text
        Content-Type: multipart/form-data

        req_at: 2025-07-07
        req_type: 1
        memo: 25년 7월 1주차 입고
        excel: (업로드할 엑셀 파일)
        ```
- [ ] Request Validation 클래스 생성 (StoreReqRequest)
  - [ ] 날짜 형식 검증, 요청타입 범위 검증(Req 모델의 상수 참고)
- [ ] 엑셀 파일 유효성 검사 및 저장 (Storage)
    - 파일 유효성 검사(확장자, 크기 등)
    - 파일 저장(스토리지 경로 명시)
- [ ] 엑셀 파싱 Job 생성 (ParseExcelJob)
    - [ ] 업로드된 엑셀 파일 파싱
    - [ ] 데이터 유효성 검사 및 오류 행 분리
- [ ] 입고 데이터 저장 Job 생성 (StoreReqDataJob)
    - [ ] 유효 데이터만 DB에 저장 (트랜잭션 처리)
    - [ ] 오류 발생 시 롤백 및 에러 로그 기록
- [ ] Job 디스패치 및 체이닝 처리 (Excel → DB)
- [ ] 처리 결과 사용자 알림 (이메일/텔레그램/슬랙 등)
- [ ] 처리 결과/에러 로그 기록 (DB/파일)
- [ ] Repository 단위 테스트 (ReqRepository)
    - [ ] 입고 목록 생성 메서드 테스트
    - [ ] 트랜잭션 처리 테스트
    - [ ] 예외 상황 처리 테스트
- [ ] Service 단위 테스트 (ReqService)
    - [ ] 입고 목록 생성 비즈니스 로직 테스트
    - [ ] 엑셀 파일 처리 로직 테스트
    - [ ] Job 디스패치 로직 테스트
- [ ] Feature 테스트 (입고 목록 생성 API)
    - [ ] 정상 케이스 테스트
        - [ ] 유효한 엑셀 파일 업로드 성공
        - [ ] 요청 데이터 유효성 검사 통과
        - [ ] 비동기 처리 Job 디스패치 확인
    - [ ] 실패 케이스 테스트
        - [ ] 잘못된 파일 형식 업로드
        - [ ] 요청 데이터 유효성 검사 실패
        - [ ] 파일 저장 실패
        - [ ] 권한 없는 사용자 접근
    - [ ] 예외/에러 테스트
        - [ ] 인증/권한 에러 (401, 403)
        - [ ] 데이터 유효성 에러 (422)
        - [ ] 서버 에러/롤백 케이스 (500)
        - [ ] 파일 업로드 제한 에러

## 2. 입고 목록 조회 API
- [ ] Route 설정: `GET /wms/reqs`, `GET /wms/reqs/{id}`
- [ ] 필터/페이징/정렬 기능 구현 (Repository)
- [ ] Eager Loading, 쿼리 최적화
- [ ] Service: ReqService::getList(), getDetail() 구현
- [ ] Repository: ReqRepository::fetchList(), fetchDetail() 구현
- [ ] 응답 데이터 포맷 정의 (Resource 활용)
- [ ] Repository 단위 테스트 (ReqRepository)
    - [ ] 목록 조회 메서드 테스트
    - [ ] 상세 조회 메서드 테스트
    - [ ] 필터/페이징/정렬 로직 테스트
    - [ ] 쿼리 최적화 테스트
- [ ] Service 단위 테스트 (ReqService)
    - [ ] 목록 조회 비즈니스 로직 테스트
    - [ ] 상세 조회 비즈니스 로직 테스트
    - [ ] 권한 검증 로직 테스트
- [ ] Feature 테스트 (입고 목록 조회 API)
    - [ ] 정상 케이스 테스트
        - [ ] 목록 조회 성공
        - [ ] 상세 조회 성공
        - [ ] 필터/페이징/정렬 동작 확인
    - [ ] 다양한 필터/권한 케이스 테스트
        - [ ] 날짜 범위 필터 테스트
        - [ ] 상태별 필터 테스트
        - [ ] 사용자별 권한 테스트
        - [ ] 페이징 경계값 테스트
    - [ ] 예외/에러 테스트
        - [ ] 존재하지 않는 ID 조회 (404)
        - [ ] 권한 없는 데이터 접근 (403)
        - [ ] 잘못된 필터 파라미터 (422)

## 3. 입고 목록 수정/삭제 API
- [ ] Route 설정: `POST /wms/reqs/{id}`, `DELETE /wms/reqs/{id}`
- [ ] Request Validation 클래스 생성 (UpdateReqRequest)
- [ ] Service: ReqService::update(), destroy() 구현
- [ ] Repository: ReqRepository::update(), delete() 구현
- [ ] 권한/상태 검증 (Service)
- [ ] 예외 및 에러 처리
- [ ] Repository 단위 테스트 (ReqRepository)
    - [ ] 수정 메서드 테스트
    - [ ] 삭제 메서드 테스트
    - [ ] 트랜잭션 처리 테스트
- [ ] Service 단위 테스트 (ReqService)
    - [ ] 수정 비즈니스 로직 테스트
    - [ ] 삭제 비즈니스 로직 테스트
    - [ ] 권한/상태 검증 로직 테스트
- [ ] Feature 테스트 (입고 목록 수정/삭제 API)
    - [ ] 정상 케이스 테스트
        - [ ] 수정 성공
        - [ ] 삭제 성공
        - [ ] 상태 변경 로직 확인
    - [ ] 권한/상태별 케이스 테스트
        - [ ] 권한 있는 사용자 수정/삭제
        - [ ] 권한 없는 사용자 접근
        - [ ] 특정 상태에서 수정/삭제 불가
        - [ ] 연관 데이터 존재 시 삭제 불가
    - [ ] 예외/에러 테스트
        - [ ] 존재하지 않는 ID 수정/삭제 (404)
        - [ ] 권한 없는 수정/삭제 (403)
        - [ ] 유효성 검사 실패 (422)
        - [ ] 서버 에러/롤백 케이스 (500)

## 4. 기타
- [ ] 엑셀 업로드/다운로드 API (Service/Repository 분리)
    - [ ] 엑셀 업로드 파일 유효성 검사 및 저장
    - [ ] 엑셀 파싱 Job 생성 및 디스패치
    - [ ] 데이터 유효성 검사 및 오류 행 분리
    - [ ] 입고 데이터 저장 Job 생성 및 트랜잭션 처리
    - [ ] 처리 결과 사용자 알림 및 에러 로그 기록
    - [ ] Feature 테스트 (엑셀 업로드/다운로드 API)
        - [ ] 엑셀 업로드 정상/실패 케이스
        - [ ] 엑셀 다운로드 정상/실패 케이스
        - [ ] 대용량 파일 처리 테스트
        - [ ] 잘못된 엑셀 형식 처리 테스트
- [ ] 상태 카운트 갱신 API
- [ ] 이벤트/리스너, Job/스케줄러 활용

## �� 의존성
- **사용자 관리**: 사용자 정보 조회 API 필요
- **권한 관리**: 역할별 접근 제어 필요

## 📈 성능 지표
- API 응답시간: 평균 200ms (목표: 500ms 이하)
- 동시 처리량: 50 TPS (목표: 100 TPS)

## 다른 곳에서 사용하는 API
- [ ] Route 설정
    - 검수대기 요청서 리스트: `GET /wms/reqs/unchecked`
    - id에 해당하는 요청서의 카운터 업데이트: `GET /wms/reqs/update-count/{id}`