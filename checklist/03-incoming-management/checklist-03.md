# 3. 입고 목록 관리 기능

## 3.1 백엔드 입고 목록 관리 – 실제 코드 기반 상세 체크리스트

### 3.1.1 입고 목록(Reqs) 데이터베이스 설계 및 모델 구현
- [ ] **reqs 테이블 구조 설계 및 마이그레이션 작성**
  - [ ] 필수 컬럼: id, req_at(요청일), req_type(요청타입), status(등록상태), user_id(등록자), checked_user_id(검수자), checked_at(검수일), memo, total_count(총 상품수), timestamps
  - [ ] 외래키 설정: user_id → users.id, checked_user_id → users.id (cascade)
  - [ ] 인덱스 설정: req_at, req_type, user_id, checked_user_id, created_at
  - [ ] 마이그레이션 파일: `2024_07_01_001101_create_reqs_table.php`

- [ ] **req_counts 테이블 구조 설계 및 마이그레이션 작성**
  - [ ] 필수 컬럼: id, req_id(입고요청 참조), undelivered(미입고), unchecked(입고대기), checked(점검대기), carryout(외주반출), waiting(수리대기), repaired(수리완료), checkout(적재중), exporting(출고대기), duplicated(중복등록), unlinked(미등록), completed(출고완료), deleted(보류)
  - [ ] 외래키 설정: req_id → reqs.id (cascade)
  - [ ] 인덱스 설정: req_id
  - [ ] 마이그레이션 파일: `2024_07_01_001102_create_req_counts_table.php`

- [ ] **Eloquent 모델(Req, ReqCount) 생성 및 관계 설정**
  - [ ] Req 모델: ReqCount와 1:1 관계, User와 2개 관계(등록자, 검수자), Product와 1:n 관계
  - [ ] ReqCount 모델: Req와 1:1 관계
  - [ ] Product 모델: Req와 n:1 관계, 중복 여부 관리(duplicated 필드)
  - [ ] 모델 단위 테스트 작성 (Pest 테스트)

### 3.1.2 입고 목록 생성 API 구현
- [ ] **Route 설정**: `POST /wms/reqs`
- [ ] **Request 유효성 검사 구현**
  - [ ] 필수 입력값: req_at(날짜), req_type(숫자), memo(텍스트)
  - [ ] 날짜 형식 검증, 요청타입 범위 검증
  - [ ] ReqRequest 클래스 생성
- [ ] **ReqService::create() 비즈니스 로직 구현**
  - [ ] 트랜잭션 처리: Req, ReqCount 동시 생성
  - [ ] 예외 상황 처리: 중복 데이터, 필수값 누락, 권한 검증
  - [ ] 사용자 정보 자동 설정 (현재 로그인 사용자)
  - [ ] Product 모델 연동: 엑셀 업로드나 API를 통해 입력된 제품 데이터로 Product 생성
  - [ ] 중복 제품 자동 감지 및 처리: duplicated 필드 설정
- [ ] **예외 및 에러 처리**
  - [ ] 적절한 HTTP 상태 코드 반환 (201, 400, 403, 500)
  - [ ] 한글 에러 메시지 제공
- [ ] **Feature 테스트 작성**
  - [ ] 정상 케이스: 올바른 데이터로 생성
  - [ ] 실패 케이스: 잘못된 데이터, 권한 없음
  - [ ] 에러 케이스: 서버 오류

### 3.1.3 입고 목록 조회 API 구현
- [ ] **Route 설정**:
  - [ ] `GET /wms/reqs` (전체 목록/필터/페이징)
  - [ ] `GET /wms/reqs/{id}` (상세 조회)
  - [ ] `GET /wms/reqs/unchecked` (검수대기 목록)
- [ ] **필터링 기능 구현**
  - [ ] 입고 날짜 기간 검색 (req_at)
  - [ ] 요청타입별 검색 (req_type: 1-쿠팡등록, 2-쿠팡애플, 9-미등록)
  - [ ] 상태별 검색 (status: 10-등록, 90-취소)
  - [ ] 등록자/검수자별 검색
- [ ] **쿼리 최적화 및 Eager Loading 적용**
  - [ ] User 관계 로딩 (등록자, 검수자 정보)
  - [ ] ReqCount 관계 로딩
  - [ ] Product 관계 로딩 및 필터링 (Req::products())
  - [ ] 인덱스 활용 쿼리 최적화
- [ ] **응답 데이터 포맷 정의**
  - [ ] 리스트: id, req_at, req_type, status, user_name, checked_user_name, total_count, created_at
  - [ ] 상세: 모든 필드 + ReqCount 정보 + 연결된 Product 정보
  - [ ] 메타데이터: 총 개수, 페이지 정보, Product 상태별 카운트
- [ ] **Feature 테스트 작성**
  - [ ] 다양한 조회/필터 케이스
  - [ ] 권한별 접근 제어 테스트

### 3.1.4 입고 목록 수정 API 구현
- [ ] **Route 설정**: `POST /wms/reqs/{id}` (admin.access 미들웨어 적용)
- [ ] **수정 가능한 필드 정의**
  - [ ] req_at, req_type, memo, status
  - [ ] checked_user_id, checked_at (검수 완료 시)
- [ ] **Request 유효성 검사**
  - [ ] 수정 가능 필드만 Validation Rule 적용
  - [ ] 기존 데이터 존재 여부 확인
- [ ] **ReqService::update() 비즈니스 로직 구현**
  - [ ] 권한 검증 (수정 권한 확인)
  - [ ] 상태 변경 시 추가 로직 (검수 완료 시 checked_at 설정)
  - [ ] 연결된 Product 상태 동기화
  - [ ] Product의 중복 상태 재확인 및 필요시 업데이트
- [ ] **예외 및 에러 처리**
  - [ ] 존재하지 않는 데이터 (404)
  - [ ] 권한 없음 (403)
  - [ ] 유효성 검사 실패 (422)
- [ ] **Feature 테스트 작성**

### 3.1.5 입고 목록 삭제 API 구현
- [ ] **Route 설정**: `DELETE /wms/reqs/{id}` (admin.access 미들웨어 적용)
- [ ] **삭제 정책 결정**
  - [ ] Soft Delete 적용 (deleted_at 컬럼 추가 고려)
  - [ ] 또는 Hard Delete (연관 데이터 함께 삭제)
- [ ] **ReqService::destroy() 비즈니스 로직 구현**
  - [ ] 연관 데이터 처리: ReqCount 자동 삭제 (cascade)
  - [ ] 권한 검증 (삭제 권한 확인)
  - [ ] 연결된 Product 처리 (삭제 또는 상태 변경)
  - [ ] MeiliSearch 인덱스 업데이트 (Product::unsearchable())
- [ ] **예외 및 에러 처리**
  - [ ] 존재하지 않는 데이터 (404)
  - [ ] 권한 없음 (403)
  - [ ] 연관 데이터로 인한 삭제 불가 (409)
- [ ] **Feature 테스트 작성**

### 3.1.6 입고 목록 상태 변경/카운트 갱신 API 구현
- [ ] **Route 설정**: `GET /wms/reqs/update-count/{id}`
- [ ] **상태 값 정의**
  - [ ] status: 10(등록), 90(취소)
  - [ ] req_type: 1(쿠팡등록), 2(쿠팡애플), 9(미등록)
- [ ] **ReqCount 동기화 처리**
  - [ ] Products 테이블의 상태별 개수 집계 (Product 모델 상태값 기준)
  - [ ] ReqCount 테이블 자동 업데이트
  - [ ] Product 모델 상태 업데이트 (필요 시)
  - [ ] MeiliSearch 인덱스 업데이트
- [ ] **예외 및 에러 처리**
- [ ] **Feature 테스트 작성**

### 3.1.7 엑셀 파일 업로드 처리 구현
- [ ] **파일 업로드 API 엔드포인트 설계**
  - [ ] Route: `POST /wms/reqs/upload-excel`
  - [ ] multipart/form-data 처리
- [ ] **파일 유효성 검사**
  - [ ] 확장자 검증 (.xlsx, .xls)
  - [ ] 파일 크기 제한 (예: 2MB)
  - [ ] 파일 내용 검증
- [ ] **엑셀 파싱 로직 구현**
  - [ ] Laravel Excel 패키지 활용
  - [ ] 컬럼 매핑 및 데이터 변환
  - [ ] 중복 제품 검사 및 처리
  - [ ] **Product 모델 연동 처리**
  - [ ] 일련번호, 바코드 등으로 기존 Product 검색
  - [ ] 중복 제품 플래그 처리 (duplicated 필드 설정)
  - [ ] Product 모델 데이터 생성 및 Req 연결
- [ ] **업로드 결과 응답 포맷 정의**
  - [ ] 성공/실패 개수
  - [ ] 중복 제품 수
  - [ ] 에러 상세 정보
- [ ] **Feature 테스트 작성**

### 3.1.8 엑셀 파일 출력 API 구현
- [ ] **엑셀 다운로드 API 엔드포인트 설계**
  - [ ] Route: `POST /wms/reqs/download-excel`
  - [ ] 필터 조건을 받아서 해당 데이터만 다운로드
- [ ] **엑셀 생성 및 데이터 매핑 로직 구현**
  - [ ] Laravel Excel 패키지 활용
  - [ ] 헤더 설정 및 데이터 포맷팅
  - [ ] 대용량 데이터 처리 (청크 단위)
- [ ] **파일 다운로드 응답 처리**
  - [ ] Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
  - [ ] 파일명 설정 (한글 지원)
- [ ] **Feature 테스트 작성**

### 3.1.9 제품 동기화 및 중복 처리 작업 스케줄러 구현
- [ ] **Product 처리 Job 클래스 생성**
  - [ ] `ProcessDuplicateProducts` Job 구현 (중복 제품 처리)
  - [ ] `SyncProductsStatus` Job 구현 (상태 동기화)
  - [ ] `UpdateAllCount` Job 구현 (카운트 업데이트)
- [ ] **Job 로직 구현**
  - [ ] 중복 제품 자동 감지 (qaid, barcode 등 기준)
  - [ ] 중복 제품 플래그 설정 (Product::duplicated 필드 업데이트)
  - [ ] 제품별 상태 자동 업데이트
  - [ ] ReqCount 테이블 자동 갱신
  - [ ] MeiliSearch 인덱스 업데이트
- [ ] **스케줄러 설정**
  - [ ] Laravel 스케줄러에 작업 등록 (app/Console/Kernel.php)
  - [ ] 주기적 실행 설정 (매시간 또는 매일)
  - [ ] 로그 및 모니터링 설정
- [ ] **스케줄러 테스트 작성**
  - [ ] 정상 동작 테스트
  - [ ] 중복 처리 테스트
  - [ ] 에러 복구 테스트

### 3.1.10 RESTful API 및 이벤트 리스너 구현
- [ ] **입고 관련 이벤트 리스너 구현**
  - [ ] ReqCreated, ReqUpdated, ProductStatusChanged 이벤트 클래스 생성
  - [ ] 이벤트 핸들러 구현
- [ ] **큐를 활용한 비동기 처리**
  - [ ] 대량 제품 처리 시 큐 활용
  - [ ] 실패 시 재시도 로직
- [ ] **Feature 테스트 작성**

### 3.1.11 MeiliSearch 인덱싱 관리
- [ ] **Product 모델 인덱싱 자동화**
  - [ ] 생성/수정/삭제 시 인덱스 자동 업데이트
  - [ ] 대량 처리 시 효율적인 인덱싱 방법 구현
  - [ ] 검색 최적화 (필터, 정렬 등)
- [ ] **검색 성능 테스트**
  - [ ] 대량 데이터 검색 성능 테스트
  - [ ] 인덱스 설정 최적화

### 3.1.12 타입 안전성 및 코드 품질 개선
- [ ] ⚠️ 타입 안전성 개선
- [ ] 🔍 데이터 유효성 검증
- [ ] 🚨 핵심 기능 누락
- [ ] **Auth::user() 타입 안전성 개선**
  - [ ] null 체크 추가
  - [ ] 타입 캐스팅 개선
  - [ ] 미들웨어에서 인증 검증 강화
- [ ] **Request Validation 클래스 생성**
  - [ ] StoreReqRequest 클래스 생성
  - [ ] UpdateReqRequest 클래스 생성
  - [ ] 한글 에러 메시지 정의
- [ ] **코드 리팩토링**
  - [ ] 중복 코드 제거
  - [ ] 메소드 분리 및 재사용성 향상
  - [ ] 예외 처리 통일화

### 3.1.13 기타 고려사항
- [ ] **API 응답 표준화**
  - [ ] 성공: `{"success": true, "data": {...}, "message": "..."}`
  - [ ] 실패: `{"success": false, "error": "...", "code": "..."}`
- [ ] **Swagger API 문서화**
  - [ ] 각 엔드포인트별 상세 설명
  - [ ] 요청/응답 예시 포함
- [ ] **코드 품질 관리**
  - [ ] 한글 주석 및 변수명 명확화
  - [ ] 에러 발생 시 상세 분석 및 mdc 규칙화
  - [ ] 불필요한 복잡성 제거, 함수 분리로 재사용성 향상
- [ ] **성능 최적화**
  - [ ] 대량 데이터 처리 시 청크 단위 처리
  - [ ] 인덱스 활용 최적화
  - [ ] 캐싱 적용 (필요 시)

## 발견된 이슈 (날짜: YYYY-MM-DD)
- [ ] 이슈: Auth::user() null 체크 필요
- [ ] 우선순위: High
- [ ] 영향도: 타입 안전성
- [ ] 해결방안: 미들웨어에서 강제 인증 검증

## 즉시 추가할 작업
- [ ] 🚨 Auth::user() null 체크 추가
  - [ ] ReqController::store() 수정
  - [ ] ReqController::update() 수정
  - [ ] ReqController::destroy() 수정
  - [ ] 테스트 코드 추가

## 제거할 작업
- [ ] ~~엑셀 파일 크기 제한 10MB~~ → 2MB로 변경됨
- [ ] ~~Soft Delete 구현~~ → Hard Delete로 결정됨

## 대안 작업으로 변경
- [ ] ~~실시간 알림 기능~~ → 큐 기반 비동기 처리로 변경
- [ ] ~~복잡한 검색 필터~~ → 단순화된 필터로 변경

## 🚨 즉시 수정 필요 (이번 주)
- [ ] Auth::user() 타입 안전성 개선
- [ ] Request Validation 클래스 생성

## 다음 스프린트 계획 (2주 후)
- [ ] 엑셀 다운로드 API 구현
- [ ] 이벤트 리스너 구현
- [ ] Swagger API 문서화

## 제거 고려사항
- [ ] ~~복잡한 검색 필터~~ → 단순화
- [ ] ~~실시간 알림~~ → 큐 기반으로 변경

## 이번 주 완료된 작업 ✅
- [ ] ReqService::create() 구현
- [ ] 엑셀 업로드 기능 구현

## 새로 발견된 이슈 🆕
- [ ] 타입 안전성 문제
- [ ] 성능 이슈

## 우선순위 재조정
- [ ] 다음 주 집중 작업 선정
- [ ] 불필요한 작업 제거