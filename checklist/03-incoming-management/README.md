# 입고 목록 관리 (Incoming Management)

## 📋 체크리스트 개요
입고 목록 관리 시스템의 전체 개발 체크리스트입니다.

## 🗂️ 파일 구조
- [01-database.md](01-database.md): 데이터베이스 설계 및 모델 (모델 단위 테스트 포함)
- [02-api.md](02-api.md): 백엔드 API 개발 (Repository/Service 단위 테스트 및 Feature 테스트 포함)
- [04-frontend-ui.md](04-frontend-ui.md): 프론트엔드 UI 컴포넌트
- [05-frontend-pages.md](05-frontend-pages.md): 프론트엔드 페이지
- [06-frontend-intergration.md](06-frontend-intergration.md): 프론트엔드 통합
- [07-frontend-testing.md](07-frontend-testing.md): 프론트엔드 테스트
- [08-e2e-testing.md](08-e2e-testing.md): E2E 테스트
- [09-issue.md](09-issue.md): 이슈 및 문제 해결

## 🔄 작업 순서
1. **데이터베이스 설계**: 01-database.md
2. **백엔드 API 개발**: 02-api.md
3. **프론트엔드 UI 개발**: 04-frontend-ui.md
4. **프론트엔드 페이지 개발**: 05-frontend-pages.md
5. **프론트엔드 통합**: 06-frontend-intergration.md
6. **프론트엔드 테스트**: 07-frontend-testing.md
7. **E2E 테스트**: 08-e2e-testing.md

## 📝 주요 특징
- **백엔드**: Laravel Service/Repository 패턴
- **프론트엔드**: Vue.js + Tailwind CSS + DaisyUI
- **테스트**: Pest (백엔드), Vitest (프론트엔드), E2E
- **비동기 처리**: 큐 Job을 활용한 엑셀 파일 처리