# 팔레트 관리 - 데이터베이스 설계 및 모델

## 1. 마이그레이션/테이블 설계
- [x] locations(팔레트 위치) 테이블 마이그레이션 (`database/migrations/2024_07_01_003100_create_locations_table.php`)
    ```php
    return new class extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('locations', function (Blueprint $table) {
                $table->id();
                $table->string('place')->default('KR-CJ')->comment('위치 지점(국가-도시), 한국-충주');
                $table->string('code')->nullable()->comment('위치 코드, 팔레트 Full name');
                $table->string('name')->nullable()->comment('위치 설명, 한글명');
                $table->enum('enable', ['Y', 'N'])->default('Y')->comment('적재 가능 여부(Y, N)');
                $table->timestamps();

                $table->index('code');
                $table->index('enable');
                $table->index('created_at');
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('locations');
        }
    };
    ```

- [x] pallets(팔레트 리스트트) 테이블 마이그레이션 작성 (`database/migrations/2024_07_01_003101_create_pallets_table.php`)
    ```php
    return new class extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('pallets', function (Blueprint $table) {
                $table->id();
                $table->foreignId('location_id')->comment('팔레트 적재 위치 인덱스');
                $table->foreignId('repair_grade_id')->nullable()->comment('팔레트 등급');
                $table->unsignedTinyInteger('status')->default(10)->comment('상태(10-등록, 20-패킹, 30-출고, 90-폐기)');
                $table->dateTime('registered_at')->nullable()->comment('팔레트 등록 시간');
                $table->unsignedBigInteger('registered_user_id')->nullable()->comment('팔레트 등록 직원 인덱스');
                $table->dateTime('checked_at')->nullable()->comment('팔레트 출고전 점검 시간');
                $table->unsignedBigInteger('checked_user_id')->nullable()->comment('팔레트 출고전 검수 직원 인덱스');
                $table->dateTime('exported_at')->nullable()->comment('팔레트 출고 시간');
                $table->unsignedBigInteger('exported_user_id')->nullable()->comment('팔레트 출고 직원 인덱스');
                $table->text('memo')->nullable();
                $table->timestamps();

                $table->foreign('location_id')
                    ->on('locations')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->foreign('repair_grade_id')
                    ->on('repair_grades')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->foreign('registered_user_id')
                    ->on('users')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->foreign('checked_user_id')
                    ->on('users')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->foreign('exported_user_id')
                    ->on('users')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->index('location_id');
                $table->index('status');
                $table->index('registered_at');
                $table->index('checked_at');
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('pallets');
        }
    };
    ```

- [x] pallet_products 테이블 마이그레이션 작성 (`database/migrations/2024_07_01_003102_create_pallet_products_table.php`)
    ```php
    return new class extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('pallet_products', function (Blueprint $table) {
                $table->id();
                $table->foreignId('pallet_id')->comment('팔레트 인덱스');
                $table->foreignId('product_id')->comment('상품 인덱스');
                $table->foreignId('repair_product_id')->nullable()->comment('수리된 상품 인덱스');
                $table->unsignedTinyInteger('status')->default(10)->comment('상태(10: 등록(적재), 90: 삭제(취소))');
                $table->unsignedBigInteger('registered_user_id')->nullable()->comment('검수한 회원 인덱스');
                $table->dateTime('registered_at')->nullable()->comment('등록 일시');
                $table->unsignedMediumInteger('quantity')->default(0)->comment('검수 수량');
                $table->unsignedInteger('amount')->default(0)->comment('판매 단가(상품 등록값과 동일)');
                $table->unsignedBigInteger('process_check_id')->nullable()->comment('진단 내용(상품 상태) 번호');
                $table->unsignedBigInteger('process_repair_id')->nullable()->comment('처리 내용(수리 내역) 번호');
                $table->unsignedBigInteger('process_grade_id')->nullable()->comment('분류 등급(상품 등급) 번호');
                $table->foreignId('repair_symptom_id')->nullable()->comment('증상 내용(상품 상태) 인덱스');
                $table->foreignId('repair_process_id')->nullable()->comment('처리 내용(수리 내역) 인덱스');
                $table->foreignId('repair_grade_id')->nullable()->comment('수리 등급 인덱스');
                $table->unsignedInteger('invoice1')->default(0)->comment('청구금액1(기본)');
                $table->unsignedInteger('invoice2')->default(0)->comment('청구금액2(추가)');
                $table->unsignedInteger('invoice3')->default(0)->comment('청구금액3(추가)');
                $table->unsignedBigInteger('checked_user_id')->nullable()->comment('최종 검수 회원 인덱스');
                $table->unsignedTinyInteger('checked_status')->default(10)->comment('최종 상태(10: 적재, 20: 검수)');
                $table->dateTime('checked_at')->nullable()->comment('최종 검수 일시');
                $table->text('memo')->nullable();
                $table->timestamps();
                $table->dateTime('deleted_at')->nullable()->comment('삭제 일시');

                $table->foreign('pallet_id')
                    ->on('pallets')
                    ->references('id')
                    ->onUpdate('cascade')
                    ->onDelete('cascade');

                $table->foreign('product_id')
                    ->on('products')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->foreign('repair_product_id')
                    ->on('repair_products')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->foreign('registered_user_id')
                    ->on('users')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->foreign('repair_symptom_id')
                    ->on('repair_symptoms')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->foreign('repair_process_id')
                    ->on('repair_processes')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->foreign('repair_grade_id')
                    ->on('repair_grades')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->foreign('checked_user_id')
                    ->on('users')
                    ->references('id')
                    ->onUpdate('cascade');

                $table->index('pallet_id');
                $table->index('product_id');
                $table->index('status');
                $table->index('registered_at');
                $table->index('repair_symptom_id');
                $table->index('repair_process_id');
                $table->index('repair_grade_id');
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('pallet_products');
        }
    };
    ```

- [x] 인덱스/파티션 설계 및 적용 (pallet_id, status 등)
- [x] 외래키/관계 설정 (products, repairs 등)

## 2. Eloquent 모델/관계
- [x] Location 모델 생성  (`app/Models/Location.php`)
    ```php
    class Location extends Model
    {
        const LOCATION_COUNTRY_KR = "KR";
        const LOCATION_COUNTRY_US = "US";

        public static $LOCATION_COUNTRY_NAMES = [
            self::LOCATION_COUNTRY_KR => "한국",
            self::LOCATION_COUNTRY_US => "미국",
        ];

        const LOCATION_CITY_GS = "GS";
        const LOCATION_CITY_PJ = "PJ";
        const LOCATION_CITY_LA = "LA";
        const LOCATION_CITY_ESCS = "ESCS";

        public static $LOCATION_CITY_NAMES = [
            self::LOCATION_CITY_GS => "고산(주덕)",
            self::LOCATION_CITY_PJ => "파주",
            self::LOCATION_CITY_LA => "Los Angeles",
            self::LOCATION_CITY_ESCS => "음성나동(코너스톤)",
        ];

        const F_ENABLE_Y = 'Y';
        const F_ENABLE_N = 'N';

        public static $F_ENABLE_NAMES = [
            self::F_ENABLE_Y => "적재가능",
            self::F_ENABLE_N => "사용불가"
        ];

        protected $fillable = [
            'place', 'code', 'name', 'enable'
        ];

        protected $hidden = [];
        protected $casts = [];

        public function pallets(): HasMany
        {
            return $this->hasMany(Pallet::class);
        }
    }
    ```

- [x] Pallet 모델 생성 (`app/Models/Pallet.php`)
    ```php
    class Pallet extends Model
    {
        const STATUS_REGISTERED = 10;
        const STATUS_LOADED = 20;
        const STATUS_CLOSED = 30;
        const STATUS_EXPORTED = 40;
        const STATUS_DELETED = 90;

        public static array $STATUS_NAME = [
            self::STATUS_REGISTERED => "등록",
            self::STATUS_LOADED => "적재중",
            self::STATUS_CLOSED => "적재마감(출고대기)",
            self::STATUS_EXPORTED => "출고완료",
            self::STATUS_DELETED => "삭제",
        ];

        protected $fillable = [
            'location_id',
            'repair_grade_id',
            'status',
            'registered_at', 'registered_user_id',
            'checked_at', 'checked_user_id',
            'exported_at', 'exported_user_id',
            'memo',
        ];

        protected $hidden = [];

        protected $casts = [
            'registered_at' => 'datetime',
            'checked_at' => 'datetime',
            'exported_at' => 'datetime',
        ];

        protected $with = [
            'location',
            'repairGrade:id,name,code',
            'registeredUser:id,name',
            'checkedUser:id,name',
            'exportedUser:id,name',
        ];

        public function location(): BelongsTo
        {
            return $this->belongsTo(Location::class);
        }

        public function repairGrade(): BelongsTo
        {
            return $this->belongsTo(RepairGrade::class, 'repair_grade_id', 'id');
        }

        public function registeredUser(): BelongsTo
        {
            return $this->belongsTo(User::class, 'registered_user_id', 'id')->withTrashed();
        }

        public function checkedUser(): BelongsTo
        {
            return $this->belongsTo(User::class, 'checked_user_id', 'id')->withTrashed();
        }

        public function exportedUser(): BelongsTo
        {
            return $this->belongsTo(User::class, 'exported_user_id', 'id')->withTrashed();
        }

        public function palletProducts(): HasMany
        {
            return $this->hasMany(PalletProduct::class);
        }

        public function deleteLog(): MorphOne
        {
            return $this->morphOne(DeleteLog::class, 'deletable', 'deletable_type', 'deletable_id');
        }
    }
    ```

- [x] PalletProduct 모델 생성 (`app/Models/PalletProduct.php`)
    ```php
    class PalletProduct extends Model
    {
        use HasFactory;

        const STATUS_REGISTERED = 10;
        const STATUS_EXPORTED = 30;
        const STATUS_DELETED = 90;

        public static $STATUS_NAME = [
            self::STATUS_REGISTERED => "적재",
            self::STATUS_EXPORTED => "출고",
            self::STATUS_DELETED => "삭제",
        ];

        const CHECK_STATUS_ON_PALLET = 10;
        const CHECK_STATUS_CHECKED = 20;

        public static $CHECK_STATUS_NAME = [
            self::CHECK_STATUS_ON_PALLET => "미검수",
            self::CHECK_STATUS_CHECKED => "검수완료",
        ];

        protected $fillable = [
            'pallet_id', 'product_id', 'repair_product_id', 'status',
            'registered_user_id', 'registered_at',
            'quantity', 'amount',
            'repair_symptom_id', 'repair_process_id', 'repair_grade_id',
            'invoice1', 'invoice2', 'invoice3',
            'checked_user_id', 'checked_status', 'checked_at',
            'deleted_at',
            'memo',
        ];

        protected $casts = [
            'registered_at' => 'datetime',
            'checked_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];

        protected $hidden = [];

        protected $with = [
            'pallet',
            'repairSymptom',
            'repairProcess',
            'repairGrade',
            'registeredUser:id,name',
            'checkedUser:id,name',
        ];

        public function pallet(): BelongsTo
        {
            return $this->belongsTo(Pallet::class);
        }

        public function product(): BelongsTo
        {
            return $this->belongsTo(Product::class, 'product_id', 'id');
        }

        public function repairProduct(): BelongsTo
        {
            return $this->belongsTo(RepairProduct::class, 'repair_product_id', 'id');
        }

        public function registeredUser(): BelongsTo
        {
            return $this->belongsTo(User::class, 'registered_user_id')->withTrashed();
        }

        public function checkedUser(): BelongsTo
        {
            return $this->belongsTo(User::class, 'checked_user_id')->withTrashed();
        }

        public function repairSymptom(): BelongsTo
        {
            return $this->belongsTo(RepairSymptom::class, 'repair_symptom_id');
        }

        public function repairProcess(): BelongsTo
        {
            return $this->belongsTo(RepairProcess::class, 'repair_process_id');
        }

        public function repairGrade(): BelongsTo
        {
            return $this->belongsTo(RepairGrade::class, 'repair_grade_id');
        }

        public function deleteLog(): MorphOne
        {
            return $this->morphOne(DeleteLog::class, 'deletable', 'deletable_type', 'deletable_id');
        }
    }
    ```

- [x] 모델 간 관계 정의 (hasMany, belongsTo 등)
- [x] 모델 단위 테스트 (Pest)

## 3. 샘플 데이터/시드
- [x] 팔레트/위치/상태 샘플 데이터 시드 작성 
