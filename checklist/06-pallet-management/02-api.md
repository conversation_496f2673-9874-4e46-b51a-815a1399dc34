# 팔레트 관리 - 백엔드 API 개발 (Service/Repository)

## 1. 팔레트 상품 적재 API

### 1.1 팔레트 생성/설정 API
- [ ] **Route**: `GET /wms/locations/generate/code/{place}`
  - **Parameters**: 
    - `place` (string): 위치 코드 (예: "KR-ESCS")
  - **Response**: 
    ```json
    {
      "level": "UZ5Q",
      "column": "4605"
    }
    ```

- [ ] **Route**: `GET /wms/pallets/loaded/set/{place}/{code}`
  - **Parameters**:
    - `place` (string): 위치 코드 (예: "KR-ESCS")
    - `code` (string): 팔레트 코드 (예: "A-1-1-UZ5Q-4605")
  - **Response**:
    ```json
    {
      "isLocation": true,
      "palletGradeCode": "",
      "palletProdCount": 0,
      "palletRecentProducts": []
    }
    ```

### 1.2 팔레트에 상품 적재/제외 API
- [ ] **Route**: `POST /wms/pallets/loaded/save-on-pallet`
  - **Request Body**:
    ```json
    {
      "location_place": "KR-ESCS",
      "location_code": "A-1-1-UZ5Q-4605", 
      "product_id": 456,
      "qaid": "QA123456789",
      "repair_product_id": 789,
      "memo": "상품 적재 메모"
    }
    ```
  - **Validation**: `StorePalletProductRequest` 클래스 활용
  - **Response**:
    ```json
    {
      "success": true,
      "status": 200,
      "message": "Success",
      "data": {
        "message": "정상 처리 되었습니다.",
      }
    }
    ```

- [ ] **Route**: `PATCH /wms/pallets/products/exclude-from-pallet`
  - **Request Body**:
    ```json
    {
      "pallet_item_id": 123,
      "reason": "상품 제외 사유"
    }
    ```

### 1.3 팔레트 상품 목록/상세 API
- [ ] **Route**: `GET /wms/pallets/products`
  - **Query Parameters**:
    - `palletId` (integer, required): 팔레트 ID
    - `checkedStatus` (integer, optional): 검수 상태 (10: 미검수, 20: 검수완료)
    - `searchType` (string, optional): 검색 타입 ("qaid" | "user")
    - `keyword` (string, optional): 검색 키워드
    - `pageSize` (integer, default: 1000): 페이지 크기
  - **Response**:
    ```json
    {
      "pallet": {
        "id": 123,
        "pallet_number": "A-1-1-UZ5Q-4605",
        "status": "in_use"
      },
      "items": [
        {
          "id": 456,
          "product": {
            "id": 789,
            "qaid": "QA123456789",
            "name": "상품명"
          },
          "quantity": 1,
          "status": "stored"
        }
      ]
    }
    ```

### 1.4 팔레트 상품 출고/검수/마감 API
- [ ] **Route**: `PATCH /wms/pallets/products`
  - **Request Body**:
    ```json
    {
      "pallet_item_ids": [123, 456, 789],
      "action": "delivery_inspection"
    }
    ```

- [ ] **Route**: `PUT /wms/pallets/products/close`
  - **Request Body**:
    ```json
    {
      "pallet_id": 123,
      "memo": "팔레트 마감 메모"
    }
    ```

### 1.5 송장/기타 비용 확인 API
- [ ] **Route**: `GET /wms/pallets/loaded/check-invoice/{id}/{repair_type}`
  - **Parameters**:
    - `id` (integer): 상품 ID
    - `repair_type` (string): 수리 타입 ("apple" | "general" | "monitor")
  - **Response**:
    ```json
    {
      "invoice_amount": 50000,
      "fee_type": "fixed",
      "fee_unit": "KRW",
      "price_ranges": []
    }
    ```

- [ ] **Route**: `GET /wms/pallets/loaded/other-expenses/{type}/{code}`
  - **Parameters**:
    - `type` (string): 비용 타입
    - `code` (string): 비용 코드
  - **Response**:
    ```json
    {
      "name": "별도 수리비용명",
      "amount": 10000
    }
    ```

### 1.6 엑셀 업로드/다운로드 API
- [ ] **Route**: `POST /wms/pallets/download`
  - **Request Body**:
    ```json
    {
      "pallet_ids": [123, 456],
      "format": "excel"
    }
    ```

## 2. 팔레트 목록/상세 API

### 2.1 팔레트 목록 API
- [ ] **Route**: `GET /wms/pallets/list`
  - **Query Parameters**:
    - `status` (integer, optional): 팔레트 상태 (10: 등록, 20: 적재중, 30: 마감, 40: 출고완료)
    - `beginAt` (string, optional): 시작일 (YYYY-MM-DD)
    - `endAt` (string, optional): 종료일 (YYYY-MM-DD)
    - `keyword` (string, optional): 검색 키워드 (위치명)
    - `pageSize` (integer, default: 15): 페이지 크기
  - **Response**:
    ```json
    {
      "pallets": {
        "data": [
          {
            "id": 123,
            "pallet_number": "A-1-1-UZ5Q-4605",
            "status": 20,
            "status_name": "적재중",
            "location": {
              "id": 1,
              "name": "KR-ESCS"
            },
            "product_count": 15,
            "product_amount": 750000,
            "registered_at": "2024-01-15T10:30:00Z",
            "registered_user": {
              "id": 1,
              "name": "홍길동"
            }
          }
        ],
        "current_page": 1,
        "per_page": 15,
        "total": 100
      }
    }
    ```

### 2.2 팔레트 상세 API
- [ ] **Route**: `GET /wms/pallets/{id}`
  - **Parameters**:
    - `id` (integer): 팔레트 ID
  - **Response**:
    ```json
    {
      "pallet": {
        "id": 123,
        "pallet_number": "A-1-1-UZ5Q-4605",
        "status": 20,
        "status_name": "적재중",
        "location": {
          "id": 1,
          "name": "KR-ESCS"
        },
        "repair_grade": {
          "id": 1,
          "name": "A등급",
          "code": "A"
        },
        "products": [
          {
            "id": 456,
            "product": {
              "id": 789,
              "qaid": "QA123456789",
              "name": "상품명"
            },
            "quantity": 1,
            "amount": 50000
          }
        ],
        "registered_at": "2024-01-15T10:30:00Z",
        "registered_user": {
          "id": 1,
          "name": "홍길동"
        }
      }
    }
    ```

## 3. 팔레트 생성/저장/마감/재오픈 API

### 3.1 팔레트 생성 API
- [ ] **Route**: `POST /wms/pallets`
  - **Request Body**:
    ```json
    {
      "location_id": 1,
      "repair_grade_id": 1,
      "memo": "팔레트 생성 메모"
    }
    ```
  - **Validation**: `StorePalletRequest` 클래스
  - **Response**:
    ```json
    {
      "pallet": {
        "id": 123,
        "pallet_number": "A-1-1-UZ5Q-4605",
        "status": 10,
        "status_name": "등록"
      }
    }
    ```

### 3.2 팔레트 수정 API
- [ ] **Route**: `PUT /wms/pallets/{id}`
  - **Parameters**:
    - `id` (integer): 팔레트 ID
  - **Request Body**:
    ```json
    {
      "location_id": 2,
      "repair_grade_id": 2,
      "memo": "팔레트 수정 메모"
    }
    ```
  - **Validation**: `UpdatePalletRequest` 클래스

### 3.3 팔레트 마감 API
- [ ] **Route**: `PUT /wms/pallets/{id}/close`
  - **Parameters**:
    - `id` (integer): 팔레트 ID
  - **Request Body**:
    ```json
    {
      "memo": "팔레트 마감 메모"
    }
    ```

### 3.4 팔레트 재오픈 API
- [ ] **Route**: `PUT /wms/pallets/{id}/open`
  - **Parameters**:
    - `id` (integer): 팔레트 ID
  - **Request Body**:
    ```json
    {
      "memo": "팔레트 재오픈 메모"
    }
    ```

## 4. Request Validation 클래스

### 4.1 StorePalletRequest
```php
public function rules(): array
{
    return [
        'location_id' => 'required|integer|exists:locations,id',
        'repair_grade_id' => 'nullable|integer|exists:repair_grades,id',
        'memo' => 'nullable|string|max:1000',
    ];
}
```

### 4.2 UpdatePalletRequest
```php
public function rules(): array
{
    return [
        'location_id' => 'sometimes|integer|exists:locations,id',
        'repair_grade_id' => 'nullable|integer|exists:repair_grades,id',
        'memo' => 'nullable|string|max:1000',
    ];
}
```

## 5. Service/Repository 구현

### 5.1 PalletService
- [ ] `getList(array $filters): LengthAwarePaginator`
- [ ] `getDetail(int $id): ?Pallet`
- [ ] `create(array $data, User $user): Pallet`
- [ ] `update(int $id, array $data, User $user): Pallet`
- [ ] `close(int $id, User $user): Pallet`
- [ ] `open(int $id, User $user): Pallet`

### 5.2 PalletRepository
- [ ] `fetchList(array $filters): Builder`
- [ ] `fetchDetail(int $id): ?Pallet`
- [ ] `store(array $data): Pallet`
- [ ] `update(int $id, array $data): Pallet`
- [ ] `close(int $id): Pallet`
- [ ] `open(int $id): Pallet`

## 6. 성능/보안/로깅

### 6.1 성능 최적화
- [ ] Eager Loading 구현 (with 관계 설정)
- [ ] 쿼리 최적화 (인덱스 활용)
- [ ] 캐싱 전략 수립
- [ ] 페이지네이션 최적화

### 6.2 보안
- [ ] 권한 검증 (Middleware)
- [ ] 입력값 검증 (Request Validation)
- [ ] SQL Injection 방지
- [ ] XSS 방지

### 6.3 로깅
- [ ] API 요청/응답 로깅
- [ ] 에러 로깅
- [ ] 성능 모니터링
- [ ] 감사 로그 (Audit Log)

### 6.4 테스트
- [ ] Feature 테스트 작성
- [ ] Unit 테스트 작성
- [ ] 성능 테스트
- [ ] 보안 테스트 