# 반출 관리 - 백엔드 API 개발 (Service/Repository 패턴)

## 1. 반출 목록 API
- [ ] Route 설정: `GET /wms/carryouts`, `GET /wms/carryouts/{id}`
- [ ] 필터/페이징/정렬 기능 구현 (Repository)
- [ ] Service: CarryoutService::getList(), getDetail() 구현
- [ ] Repository: CarryoutRepository::fetchList(), fetchDetail() 구현
- [ ] 응답 데이터 포맷 정의 (Resource 활용)
- [ ] Feature 테스트 작성

## 2. 반출 생성/수정/삭제 API
- [ ] Route 설정: `POST /wms/carryouts`, `PUT /wms/carryouts/{id}`, `DELETE /wms/carryouts/{id}`
- [ ] Request Validation 클래스 생성 (StoreCarryoutRequest, UpdateCarryoutRequest)
- [ ] Service: CarryoutService::create(), update(), destroy() 구현
- [ ] Repository: CarryoutRepository::store(), update(), delete() 구현
- [ ] 권한/상태 검증 (Service)
- [ ] 예외 및 에러 처리 (Custom Exception 활용)
- [ ] Feature 테스트 작성

## 3. 반출 상품 관리 API
- [ ] 반출 상품 목록/추가/삭제 API 구현
- [ ] 반출 상품 출고/입고 처리 API 구현
- [ ] 엑셀 다운로드 API 구현 (대용량/청크 처리)
- [ ] 상태 변경/로그 기록 API 구현

## 4. 기타
- [ ] 협력사/담당자 관리 API 연동
- [ ] 권한별 접근 제한 미들웨어 적용
- [ ] API 응답시간/성능 테스트 