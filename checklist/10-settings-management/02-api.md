# 설정 관리 - 백엔드 API 개발

## 1. 직원 관리 API
- [ ] 직원 목록 조회 API (`GET /wms/settings/members`)
- [ ] 직원 생성 API (`POST /wms/settings/members`)
- [ ] 직원 수정 API (`PUT /wms/settings/members/{id}`)
- [ ] 직원 삭제/복구 API (`DELETE /wms/settings/members/{id}`)
- [ ] 직원 상세 정보 API (`GET /wms/settings/members/{id}`)
- [ ] 권한/역할별 접근 제어 미들웨어 적용
- [ ] Request Validation 클래스 생성
- [ ] Service/Repository 패턴 적용
- [ ] 예외/에러 처리(권한, 유효성, 서버)
- [ ] Feature 테스트(Pest)

## 2. 수리 등급/처리/증상/구성품/위치/상태/수리비 관리 API
- [ ] 등급 목록/생성/수정/삭제 API (`/wms/settings/repairs/grades` 등)
- [ ] 처리 내용/증상/구성품/위치/상태/수리비 CRUD API
- [ ] 각 항목별 Request Validation/Service/Repository/권한 미들웨어 적용
- [ ] 예외/에러 처리(중복, 권한, 유효성 등)
- [ ] Feature 테스트(Pest)

## 3. 코드/인덱스/성능/보안
- [ ] 코드/등급/구성품 등 코드 테이블 인덱스 적용
- [ ] 개인정보 암호화/보안 처리 