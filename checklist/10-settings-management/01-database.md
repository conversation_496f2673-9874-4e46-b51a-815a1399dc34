# 설정 관리 - 데이터베이스 설계

## 1. 테이블/마이그레이션 설계
- [ ] 직원(members) 테이블 설계 및 마이그레이션
- [ ] 수리 등급(grades) 테이블 설계 및 마이그레이션
- [ ] 처리 내용(processes) 테이블 설계 및 마이그레이션
- [ ] 증상(symptoms) 테이블 설계 및 마이그레이션
- [ ] 구성품(parts) 테이블 설계 및 마이그레이션
- [ ] 구성품 카테고리(part_categories) 테이블 설계 및 마이그레이션
- [ ] 위치(locations) 테이블 설계 및 마이그레이션
- [ ] 작업 상태(statuses) 테이블 설계 및 마이그레이션
- [ ] 수리비(repair_fees) 테이블 설계 및 마이그레이션

## 2. Eloquent 모델/관계
- [ ] Member, Grade, Process, Symptom, Part, PartCategory, Location, Status, RepairFee 모델 생성
- [ ] 모델 간 관계 설정(외래키, N:M 등)

## 3. 인덱스/성능/보안
- [ ] 검색/조인 필드 인덱스 설계
- [ ] 코드/등급/구성품 등 코드 테이블 인덱스 적용
- [ ] 개인정보 암호화/보안 필드 설계

## 4. 단위 테스트
- [ ] 각 모델 단위 테스트(Pest)
- [ ] 마이그레이션/롤백 테스트 