# 게시판 관리 - 데이터베이스 설계

## 1. 테이블/마이그레이션
- [ ] boards 테이블 설계 및 마이그레이션 작성
- [ ] board_categories(분류) 테이블 설계 및 마이그레이션 작성
- [ ] board_comments(댓글) 테이블 설계 및 마이그레이션 작성
- [ ] boards, categories, comments 관계 설정 (FK, 인덱스)
- [ ] boards 테이블에 공지/FAQ 구분 컬럼 추가
- [ ] boards 테이블에 작성자, 수정자, 삭제자 FK 추가
- [ ] boards 테이블 soft delete, timestamps 적용

## 2. Eloquent 모델
- [ ] Board, BoardCategory, BoardComment 모델 생성 및 관계 설정
- [ ] 모델별 단위 테스트(Pest) 