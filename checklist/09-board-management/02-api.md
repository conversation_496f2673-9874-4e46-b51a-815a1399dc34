# 게시판 관리 - 백엔드 API 개발

## 1. 공지사항/FAQ 목록 API
- [ ] Route: GET /wms/boards, GET /wms/boards/{id}
- [ ] 카테고리/검색/페이징/정렬 기능 구현
- [ ] Service: BoardService::getList(), getDetail() 구현
- [ ] Repository: BoardRepository::fetchList(), fetchDetail() 구현
- [ ] Resource: BoardResource 정의
- [ ] Feature 테스트 작성

## 2. 공지사항/FAQ 등록/수정/삭제 API
- [ ] Route: POST /wms/boards, PUT /wms/boards/{id}, DELETE /wms/boards/{id}
- [ ] Request Validation 클래스 생성 (StoreBoardRequest, UpdateBoardRequest)
- [ ] Service: BoardService::create(), update(), destroy() 구현
- [ ] Repository: BoardRepository::store(), update(), delete() 구현
- [ ] 권한/상태 검증 (관리자만 등록/수정/삭제 가능)
- [ ] 예외/에러 처리 (권한, 유효성, 서버 에러)
- [ ] Feature 테스트 작성

## 3. 댓글/답글 API
- [ ] Route: POST /wms/boards/{id}/comments, DELETE /wms/boards/comments/{id}
- [ ] Service/Repository/Validation/테스트 구현

## 4. 기타
- [ ] API 요청/응답 로깅
- [ ] API 성능(응답시간) 측정 