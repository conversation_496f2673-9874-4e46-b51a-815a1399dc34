# 수리/점검 관리 - 이슈 트래킹

## 🚨 High Priority
### 이슈 #001: 수리비 자동 산정 로직 개선 필요
- **발견일**: 2024-12-01
- **상태**: 진행중
- **담당자**: 김수리
- **설명**: 수리비 자동 산정 로직에서 등급별 계산 오류 발생, 복잡한 조건 처리 필요
- **해결방안**: 
  - RepairFee 모델과 RepairGrade 모델 간 관계 재정의
  - 수리비 산정 알고리즘 개선 (등급 → 처리내용 → 증상 순서)
  - 단위 테스트 케이스 추가
- **예상 완료일**: 2024-12-05

### 이슈 #002: 라벨 프린트(Tauri) 연동 안정화
- **발견일**: 2024-12-01
- **상태**: 대기중
- **담당자**: 이프린트
- **설명**: Tauri 플러그인 연동 시 프린터 인식 오류, 수리/점검 완료 후 라벨 출력 실패
- **해결방안**: 
  - 프린터 드라이버/플러그인 버전 점검
  - 예외 처리 및 재시도 로직 추가
  - 프린터 상태 확인 API 구현
- **예상 완료일**: 2024-12-08

### 이슈 #003: 구성품 재고 관리 연동
- **발견일**: 2024-12-01
- **상태**: 조사중
- **담당자**: 박재고
- **설명**: 수리/점검 시 구성품 사용 시 재고 차감 로직 미구현
- **해결방안**: 
  - RepairProductParts 모델과 재고 시스템 연동
  - 트랜잭션 처리로 재고 일관성 보장
  - 재고 부족 시 알림 기능 구현
- **예상 완료일**: 2024-12-10

## ⚠️ Medium Priority
### 이슈 #004: 대용량 데이터 조회 성능 최적화
- **발견일**: 2024-12-01
- **상태**: 조사중
- **담당자**: 박성능
- **설명**: 수리/점검 이력 대량 조회 시 응답 지연, 관계 테이블 조인 성능 이슈
- **해결방안**: 
  - 인덱스 최적화 (repair_products, repair_product_parts 테이블)
  - 쿼리 최적화 (Eager Loading, 쿼리 빌더 활용)
  - 캐싱 전략 적용 검토
- **예상 완료일**: 2024-12-15

### 이슈 #005: 관계 데이터 동기화 이슈
- **발견일**: 2024-12-01
- **상태**: 대기중
- **담당자**: 김관계
- **설명**: 증상-처리내용-등급 간 관계 설정 시 데이터 일관성 문제
- **해결방안**: 
  - 관계 테이블 제약조건 강화
  - 동기화 API 응답 검증 로직 추가
  - 관계 변경 시 영향도 분석 기능 구현
- **예상 완료일**: 2024-12-12

## 🔵 Low Priority
### 이슈 #006: 코드 주석 및 문서화 부족
- **발견일**: 2024-12-01
- **상태**: 대기중
- **담당자**: 김주석
- **설명**: 복잡한 수리/점검 로직에 한글 주석 부족, API 문서 미완성
- **해결방안**: 
  - 주요 메소드에 상세 주석 추가
  - API 문서화 (Swagger/OpenAPI) 완성
  - 사용자 매뉴얼 작성
- **예상 완료일**: 2024-12-20

### 이슈 #007: UI/UX 개선 필요
- **발견일**: 2024-12-01
- **상태**: 대기중
- **담당자**: 이디자인
- **설명**: 수리/점검 등록 시 단계별 진행 표시 부족, 모바일 반응형 개선 필요
- **해결방안**: 
  - 단계별 진행 바 컴포넌트 구현
  - 모바일 반응형 레이아웃 개선
  - 사용자 피드백 반영
- **예상 완료일**: 2024-12-25

## ✅ 해결된 이슈
### 이슈 #000: 데이터베이스 스키마 설계 완료
- **해결일**: 2024-12-01
- **해결방안**: 
  - 수리비, 구성품, 증상/처리내용/등급, 수리제품 관련 테이블 설계 완료
  - 마이그레이션 파일 생성 완료
  - 모델 클래스 생성 완료
- **검증**: 마이그레이션 실행 및 모델 관계 설정 확인

### 이슈 #000-1: API 라우트 구조 설계 완료
- **해결일**: 2024-12-01
- **해결방안**: 
  - 수리 코드 관리 API 라우트 구조 정의
  - 수리비 및 구성품 설정 API 라우트 구조 정의
  - 수리/점검 실행 API 라우트 구조 정의
- **검증**: 라우트 등록 및 컨트롤러 기본 구조 확인 