# 수리/점검 관리 - 프론트엔드 연동

## 1. 수리 코드 관리 API 연동
### 1.1 수리 등급 관리 API 연동
- [ ] 등급 목록 조회 API 연동 (`GET /wms/settings/repair/grades`)
- [ ] 등급 등록 API 연동 (`POST /wms/settings/repair/grades`)
- [ ] 등급 수정 API 연동 (`PUT /wms/settings/repair/grades/{grade}`)
- [ ] 등급 삭제 API 연동 (`DELETE /wms/settings/repair/grades/{grade}`)

### 1.2 처리 내용 관리 API 연동
- [ ] 처리 내용 목록 조회 API 연동 (`GET /wms/settings/repair/processes`)
- [ ] 처리 내용 등록/수정/삭제 API 연동
- [ ] 등급 연결 조회 API 연동 (`GET /wms/settings/repair/processes/{process}/grades`)
- [ ] 등급 연결 동기화 API 연동 (`POST /wms/settings/repair/processes/{process}/sync-grades`)

### 1.3 증상 내용 관리 API 연동
- [ ] 증상 목록 조회 API 연동 (`GET /wms/settings/repair/symptoms`)
- [ ] 증상 등록/수정/삭제 API 연동
- [ ] 처리 내용 연결 조회 API 연동 (`GET /wms/settings/repair/symptoms/{symptom}/processes`)
- [ ] 처리 내용 연결 동기화 API 연동 (`POST /wms/settings/repair/symptoms/{symptom}/sync-processes`)

## 2. 수리비 및 구성품 설정 API 연동
### 2.1 수리비 관리 API 연동
- [ ] 수리비 목록 조회 API 연동 (`GET /wms/settings/repair/fees`)
- [ ] 수리비 등록/수정/삭제 API 연동
- [ ] 수리비 옵션 조회 API 연동 (`GET /wms/settings/repair/fees/options`)

### 2.2 구성품 관리 API 연동
- [ ] 구성품 목록 조회 API 연동 (`GET /wms/settings/repair/parts`)
- [ ] 구성품 등록/수정/삭제 API 연동
- [ ] 구성품 카테고리 관리 API 연동 (`GET/POST/PUT/DELETE /wms/settings/repair/parts-categories`)

## 3. 수리/점검 실행 API 연동
### 3.1 수리/점검 기록 API 연동
- [ ] 수리/점검 등록 API 연동 (`POST /wms/repairs/store`)
- [ ] 제품 조회 API 연동 (`GET /wms/repairs/check-product/{qaid}`)

### 3.2 구성품 관련 API 연동
- [ ] 구성품 카테고리 조회 API 연동 (`GET /wms/repairs/parts-categories`)
- [ ] 구성품 목록 조회 API 연동 (`GET /wms/repairs/parts/{category_id?}`)

### 3.3 코드 조회 API 연동
- [ ] 증상 조회 API 연동 (`GET /wms/repairs/code/symptoms/{req_type}`)
- [ ] 처리 내용 조회 API 연동 (`GET /wms/repairs/code/processes/symptom/{id}`)
- [ ] 등급 조회 API 연동 (`GET /wms/repairs/code/grade/process/{id}`)

## 4. 상태 관리
- [ ] Svelte Store로 상태 관리 구현
- [ ] 수리 코드 관리 상태 (등급, 처리 내용, 증상)
- [ ] 수리비 및 구성품 상태
- [ ] 수리/점검 실행 상태
- [ ] 로딩/에러/성공 상태 처리

## 5. 예외/에러/권한 처리
- [ ] 인증/권한 에러 처리
- [ ] API 에러 메시지 표시
- [ ] 네트워크 에러 처리
- [ ] 유효성 검증 에러 처리
- [ ] 대용량 데이터 처리 최적화
- [ ] 보안 관련 예외 처리

## 6. 기타 연동
- [ ] 라벨 프린트(Tauri 플러그인) 연동
- [ ] 파일 업로드/다운로드 연동
- [ ] 실시간 알림 연동 (WebSocket)
- [ ] 로그 기록 연동 