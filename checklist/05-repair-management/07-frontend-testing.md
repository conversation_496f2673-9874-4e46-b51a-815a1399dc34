# 수리/점검 관리 - 프론트엔드 테스트

## 1. 컴포넌트 단위 테스트
### 1.1 공통 컴포넌트 테스트
- [ ] 필터 입력 컴포넌트 테스트
- [ ] 테이블 컴포넌트 테스트
- [ ] 페이징 컴포넌트 테스트
- [ ] 로딩/에러/알림 컴포넌트 테스트
- [ ] 모달/다이얼로그 컴포넌트 테스트
- [ ] 폼 컴포넌트 테스트

### 1.2 수리 코드 관리 컴포넌트 테스트
- [ ] 등급 목록 테이블 컴포넌트 테스트
- [ ] 등급 등록/수정 폼 컴포넌트 테스트
- [ ] 처리 내용 목록 테이블 컴포넌트 테스트
- [ ] 처리 내용 등록/수정 폼 컴포넌트 테스트
- [ ] 등급 연결 관리 컴포넌트 테스트
- [ ] 증상 목록 테이블 컴포넌트 테스트
- [ ] 증상 등록/수정 폼 컴포넌트 테스트
- [ ] 처리 내용 연결 관리 컴포넌트 테스트

### 1.3 수리비 및 구성품 관리 컴포넌트 테스트
- [ ] 수리비 목록 테이블 컴포넌트 테스트
- [ ] 수리비 등록/수정 폼 컴포넌트 테스트
- [ ] 구성품 목록 테이블 컴포넌트 테스트
- [ ] 구성품 등록/수정 폼 컴포넌트 테스트
- [ ] 구성품 카테고리 선택 컴포넌트 테스트
- [ ] 카테고리 목록 테이블 컴포넌트 테스트

### 1.4 수리/점검 실행 컴포넌트 테스트
- [ ] 제품 정보 입력 컴포넌트 테스트
- [ ] 증상 선택 컴포넌트 테스트
- [ ] 처리 내용 선택 컴포넌트 테스트
- [ ] 수리 등급 선택 컴포넌트 테스트
- [ ] 구성품 선택 컴포넌트 테스트
- [ ] 수리비 자동 산정/조정 컴포넌트 테스트
- [ ] 수리/점검 등록 폼 컴포넌트 테스트

## 2. Store/상태 관리 테스트
- [ ] 수리 코드 관리 Store 테스트 (등급, 처리 내용, 증상)
- [ ] 수리비 및 구성품 Store 테스트
- [ ] 수리/점검 실행 Store 테스트
- [ ] 로딩/에러/성공 상태 처리 테스트

## 3. 페이지 통합 테스트
### 3.1 수리 코드 관리 페이지 테스트
- [ ] 등급 관리 페이지 렌더링/동작 테스트
- [ ] 처리 내용 관리 페이지 렌더링/동작 테스트
- [ ] 증상 내용 관리 페이지 렌더링/동작 테스트

### 3.2 수리비 및 구성품 관리 페이지 테스트
- [ ] 수리비 관리 페이지 렌더링/동작 테스트
- [ ] 구성품 관리 페이지 렌더링/동작 테스트
- [ ] 구성품 카테고리 관리 페이지 렌더링/동작 테스트

### 3.3 수리/점검 실행 페이지 테스트
- [ ] 수리/점검 등록 페이지 렌더링/동작 테스트
- [ ] 제품 조회 페이지 렌더링/동작 테스트
- [ ] 구성품 조회 페이지 렌더링/동작 테스트

## 4. API 연동 통합 테스트
- [ ] 수리 코드 관리 API 연동 테스트
- [ ] 수리비 및 구성품 API 연동 테스트
- [ ] 수리/점검 실행 API 연동 테스트
- [ ] 에러 처리 및 재시도 로직 테스트

## 5. 사용자 인터랙션 테스트
- [ ] 폼 입력/검증 테스트
- [ ] 모달/다이얼로그 동작 테스트
- [ ] 테이블 정렬/필터/페이징 테스트
- [ ] 다중 선택 기능 테스트
- [ ] 파일 업로드/다운로드 테스트

## 6. 예외/에러/권한 테스트
- [ ] 입력값 유효성 검증 테스트
- [ ] 인증/권한 에러 처리 테스트
- [ ] 네트워크 에러 처리 테스트
- [ ] API 에러 메시지 표시 테스트
- [ ] 대용량 데이터 처리 테스트
- [ ] 보안 관련 예외 처리 테스트

## 7. 성능 테스트
- [ ] 컴포넌트 렌더링 성능 테스트
- [ ] 대용량 목록 처리 성능 테스트
- [ ] 메모리 누수 테스트
- [ ] 번들 크기 최적화 테스트 