# 수리/점검 관리 - 프론트엔드 페이지 구현

## 1. 수리 코드 관리 페이지 (Settings)
### 1.1 수리 등급 관리 페이지
- [ ] 등급 목록 페이지 레이아웃
- [ ] 등급 목록 테이블 구현 (검색, 필터, 정렬)
- [ ] 등급 등록/수정 모달 구현
- [ ] 등급 삭제 확인 다이얼로그
- [ ] 페이징 처리

### 1.2 처리 내용 관리 페이지
- [ ] 처리 내용 목록 페이지 레이아웃
- [ ] 처리 내용 목록 테이블 구현
- [ ] 처리 내용 등록/수정 모달 구현
- [ ] 등급 연결 관리 모달 구현 (다중 선택)
- [ ] 처리 내용 삭제 확인 다이얼로그

### 1.3 증상 내용 관리 페이지
- [ ] 증상 목록 페이지 레이아웃
- [ ] 증상 목록 테이블 구현
- [ ] 증상 등록/수정 모달 구현
- [ ] 처리 내용 연결 관리 모달 구현 (다중 선택)
- [ ] 증상 삭제 확인 다이얼로그

## 2. 수리비 및 구성품 관리 페이지 (Settings)
### 2.1 수리비 관리 페이지
- [ ] 수리비 목록 페이지 레이아웃
- [ ] 수리비 목록 테이블 구현
- [ ] 수리비 등록/수정 모달 구현
- [ ] 수리비 옵션 선택 기능
- [ ] 수리비 삭제 확인 다이얼로그

### 2.2 구성품 관리 페이지
- [ ] 구성품 목록 페이지 레이아웃
- [ ] 구성품 목록 테이블 구현 (카테고리별 필터)
- [ ] 구성품 등록/수정 모달 구현
- [ ] 구성품 카테고리 선택 기능
- [ ] 구성품 삭제 확인 다이얼로그

### 2.3 구성품 카테고리 관리 페이지
- [ ] 카테고리 목록 페이지 레이아웃
- [ ] 카테고리 목록 테이블 구현
- [ ] 카테고리 등록/수정 모달 구현
- [ ] 카테고리 삭제 확인 다이얼로그

## 3. 수리/점검 실행 페이지
### 3.1 수리/점검 등록 페이지
- [ ] 제품 정보 입력 영역 (QAID 검색)
- [ ] 증상 선택 영역 (계층적 선택)
- [ ] 처리 내용 선택 영역 (증상 기반)
- [ ] 수리 등급 선택 영역 (처리 내용 기반)
- [ ] 구성품 선택 영역 (카테고리별)
- [ ] 수리비 자동 산정/조정 영역
- [ ] 수리/점검 등록 폼 구현
- [ ] 유효성 검증 및 에러 처리

### 3.2 제품 조회 페이지
- [ ] QAID 검색 영역
- [ ] 제품 정보 표시 영역
- [ ] 제품 이력 조회 영역
- [ ] 수리/점검 이력 표시

### 3.3 구성품 조회 페이지
- [ ] 구성품 카테고리 선택 영역
- [ ] 구성품 목록 표시 영역
- [ ] 구성품 검색/필터 기능
- [ ] 구성품 재고 정보 표시

## 4. 대시보드 및 통계 페이지
- [ ] 수리/점검 통계 대시보드
- [ ] 수리비 통계 차트
- [ ] 구성품 사용량 통계
- [ ] 수리 등급별 통계
- [ ] QAID별 타임라인 조회
- [ ] 수리/점검 이력 상세 조회

## 5. 기타 페이지
- [ ] 라벨 프린트 설정 페이지
- [ ] 작업 로그 조회 페이지
- [ ] 수리/점검 완료 알림 페이지 