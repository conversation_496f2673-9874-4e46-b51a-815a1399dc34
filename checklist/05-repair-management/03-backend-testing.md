# 수리/점검 관리 - 백엔드 테스트 (Pest)

## 1. 모델 단위 테스트
### 1.1 수리비 관련 모델 테스트
- [ ] RepairCategory 모델 단위 테스트 (관계, 액세서/뮤테이터)
- [ ] RepairFeeRange 모델 단위 테스트
- [ ] RepairFee 모델 단위 테스트

### 1.2 구성품 관련 모델 테스트
- [ ] RepairParts 모델 단위 테스트
- [ ] RepairPartsCategory 모델 단위 테스트
- [ ] RepairPartsLog 모델 단위 테스트
- [ ] RepairPartsOrder 모델 단위 테스트

### 1.3 수리 코드 관련 모델 테스트
- [ ] RepairSymptom 모델 단위 테스트
- [ ] RepairProcess 모델 단위 테스트
- [ ] RepairGrade 모델 단위 테스트
- [ ] RepairProcessRepairSymptom 연결 모델 테스트
- [ ] RepairGradeRepairProcess 연결 모델 테스트

### 1.4 수리 제품 관련 모델 테스트
- [ ] RepairProduct 모델 단위 테스트
- [ ] RepairProductParts 모델 단위 테스트

## 2. Repository 단위 테스트
- [ ] GradeRepository 단위 테스트
- [ ] RepairProcessRepository 단위 테스트
- [ ] SymptomRepository 단위 테스트
- [ ] RepairFeeRepository 단위 테스트
- [ ] RepairPartsRepository 단위 테스트
- [ ] RepairPartsCategoryRepository 단위 테스트
- [ ] RepairRepository 단위 테스트

## 3. Service 단위 테스트
- [ ] GradeService 단위 테스트
- [ ] RepairProcessService 단위 테스트
- [ ] SymptomService 단위 테스트
- [ ] RepairFeeService 단위 테스트
- [ ] RepairPartsService 단위 테스트
- [ ] RepairPartsCategoryService 단위 테스트
- [ ] RepairService 단위 테스트

## 4. Feature 테스트
### 4.1 수리 코드 관리 API 테스트
- [ ] 수리 등급 CRUD API 정상/실패 케이스
- [ ] 처리 내용 CRUD API 정상/실패 케이스
- [ ] 증상 내용 CRUD API 정상/실패 케이스
- [ ] 관계 설정 API (증상-처리, 처리-등급) 테스트

### 4.2 수리비 및 구성품 설정 API 테스트
- [ ] 수리비 관리 API 정상/실패 케이스
- [ ] 구성품 관리 API 정상/실패 케이스
- [ ] 구성품 카테고리 관리 API 정상/실패 케이스

### 4.3 수리/점검 실행 API 테스트
- [ ] 수리/점검 기록 API 정상/실패 케이스
- [ ] 구성품 조회 API 테스트
- [ ] 제품 조회 API 테스트
- [ ] 코드 조회 API 테스트

## 5. 예외/에러/보안 테스트
- [ ] 인증/권한 에러 케이스
- [ ] 데이터 유효성 검증 에러 케이스
- [ ] 트랜잭션 롤백 케이스
- [ ] 대용량 데이터 처리 테스트
- [ ] SQL 인젝션 방지 테스트
- [ ] XSS 방지 테스트
- [ ] CSRF 보호 테스트

## 6. 통합 테스트
- [ ] 전체 수리/점검 플로우 테스트
- [ ] 수리비 자동 산정 로직 테스트
- [ ] 구성품 재고 관리 플로우 테스트
- [ ] 라벨 프린트 연동 테스트 