# 수리/점검 관리 - 백엔드 API 개발 (Service/Repository 패턴)

## 1. 수리 코드 관리 API (settings 라우터 하위)
### 1.1 수리 등급 관리 API
- [x] Route 설정: `GET/POST/PUT/DELETE /wms/settings/repair/grades`
- [x] GradeController 구현 (apiResource)
- [ ] Request Validation 클래스 생성 (StoreGradeRequest, UpdateGradeRequest)
- [ ] Service: GradeService::create(), update(), delete() 구현
- [ ] Repository: GradeRepository::store(), update(), delete() 구현
- [ ] 응답 데이터 포맷 정의 (GradeResource 활용)
- [ ] Feature 테스트 작성 (Pest)

### 1.2 처리 내용 관리 API
- [x] Route 설정: `GET/POST/PUT/DELETE /wms/settings/repair/processes`
- [x] Route 설정: `GET /wms/settings/repair/processes/{process}/grades`
- [x] Route 설정: `POST /wms/settings/repair/processes/{process}/sync-grades`
- [x] RepairProcessController 구현 (apiResource + 추가 메서드)
- [ ] Request Validation 클래스 생성
- [ ] Service: RepairProcessService::create(), update(), delete(), getGrades(), syncGrades() 구현
- [ ] Repository: RepairProcessRepository 구현
- [ ] Feature 테스트 작성

### 1.3 증상 내용 관리 API
- [x] Route 설정: `GET/POST/PUT/DELETE /wms/settings/repair/symptoms`
- [x] Route 설정: `GET /wms/settings/repair/symptoms/{symptom}/processes`
- [x] Route 설정: `POST /wms/settings/repair/symptoms/{symptom}/sync-processes`
- [x] SymptomController 구현 (apiResource + 추가 메서드)
- [ ] Request Validation 클래스 생성
- [ ] Service: SymptomService::create(), update(), delete(), getProcesses(), syncProcesses() 구현
- [ ] Repository: SymptomRepository 구현
- [ ] Feature 테스트 작성

## 2. 수리비 및 구성품 설정 API (settings 라우터 하위)
### 2.1 수리비 관리 API
- [x] Route 설정: `GET/POST/PUT/DELETE /wms/settings/repair/fees`
- [x] Route 설정: `GET /wms/settings/repair/fees/options`
- [x] RepairFeeController 구현
- [ ] Request Validation 클래스 생성
- [ ] Service: RepairFeeService::list(), store(), update(), destroy(), repairOptions() 구현
- [ ] Repository: RepairFeeRepository 구현
- [ ] Feature 테스트 작성

### 2.2 구성품 관리 API
- [x] Route 설정: `GET/POST/PUT/DELETE /wms/settings/repair/parts`
- [x] RepairPartsController 구현
- [ ] Request Validation 클래스 생성
- [ ] Service: RepairPartsService::partsList(), storeParts(), updateParts(), destroyParts() 구현
- [ ] Repository: RepairPartsRepository 구현
- [ ] Feature 테스트 작성

### 2.3 구성품 카테고리 관리 API
- [x] Route 설정: `GET/POST/PUT/DELETE /wms/settings/repair/parts-categories`
- [x] RepairPartsCategoryController 구현
- [ ] Request Validation 클래스 생성
- [ ] Service: RepairPartsCategoryService::index(), store(), update(), destroy() 구현
- [ ] Repository: RepairPartsCategoryRepository 구현
- [ ] Feature 테스트 작성

## 3. 수리/점검 실행 API
### 3.1 수리/점검 기록 API
- [x] Route 설정: `POST /wms/repairs/store`
- [ ] Request Validation 클래스 생성 (StoreRepairRequest)
- [ ] Service: RepairService::store() 구현
- [ ] Repository: RepairRepository::store() 구현
- [ ] 트랜잭션 처리 (Service 내부)
- [ ] 예외 및 에러 처리 (커스텀 Exception 활용)
- [ ] Feature 테스트 작성 (Pest)

### 3.2 구성품 관련 조회 API
- [x] Route 설정: `GET /wms/repairs/parts-categories`
- [x] Route 설정: `GET /wms/repairs/parts/{category_id?}`
- [ ] Service: RepairService::getPartsCategories(), getParts() 구현
- [ ] Repository: RepairRepository::getPartsCategories(), getParts() 구현
- [ ] Feature 테스트 작성

### 3.3 제품 조회 API
- [x] Route 설정: `GET /wms/repairs/check-product/{qaid}`
- [ ] Service: RepairService::checkProduct() 구현
- [ ] Repository: RepairRepository::checkProduct() 구현
- [ ] Feature 테스트 작성

### 3.4 코드 조회 API
- [x] Route 설정: `GET /wms/repairs/code/symptoms/{req_type}`
- [x] Route 설정: `GET /wms/repairs/code/processes/symptom/{id}`
- [x] Route 설정: `GET /wms/repairs/code/grade/process/{id}`
- [ ] Service: RepairService::getSymptom(), getProcess(), getGrade() 구현
- [ ] Repository: RepairRepository::getSymptom(), getProcess(), getGrade() 구현
- [ ] Feature 테스트 작성

## 4. 기타 기능
- [ ] 수리비 자동 산정/조정 로직 구현
- [ ] 라벨 프린트(Tauri 플러그인) 연동 API 구현
- [ ] 구성품 주문/로그 관리 API 구현 (주석 처리된 부분)
- [ ] 대용량/성능/보안 테스트
- [ ] API 문서화 (Swagger/OpenAPI) 