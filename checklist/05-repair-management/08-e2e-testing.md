# 수리/점검 관리 - E2E 테스트

## 1. 수리 코드 관리 E2E 테스트
### 1.1 수리 등급 관리 시나리오
- [ ] 등급 목록 조회 → 등급 등록 → 등급 수정 → 등급 삭제 플로우
- [ ] 등급 데이터 유효성 검증 (중복 이름, 필수 필드 등)
- [ ] 등급 삭제 시 연관 데이터 처리 확인

### 1.2 처리 내용 관리 시나리오
- [ ] 처리 내용 목록 조회 → 등록 → 수정 → 삭제 플로우
- [ ] 등급 연결 관리 (연결 → 해제 → 재연결)
- [ ] 처리 내용 삭제 시 등급 연결 해제 확인

### 1.3 증상 내용 관리 시나리오
- [ ] 증상 목록 조회 → 등록 → 수정 → 삭제 플로우
- [ ] 처리 내용 연결 관리 (연결 → 해제 → 재연결)
- [ ] 증상 삭제 시 처리 내용 연결 해제 확인

## 2. 수리비 및 구성품 관리 E2E 테스트
### 2.1 수리비 관리 시나리오
- [ ] 수리비 목록 조회 → 등록 → 수정 → 삭제 플로우
- [ ] 수리비 옵션 선택 및 적용
- [ ] 수리비 자동 산정 로직 검증

### 2.2 구성품 관리 시나리오
- [ ] 구성품 목록 조회 → 등록 → 수정 → 삭제 플로우
- [ ] 구성품 카테고리별 필터링 및 검색
- [ ] 구성품 재고 관리 연동

### 2.3 구성품 카테고리 관리 시나리오
- [ ] 카테고리 목록 조회 → 등록 → 수정 → 삭제 플로우
- [ ] 카테고리 삭제 시 구성품 처리 확인

## 3. 수리/점검 실행 E2E 테스트
### 3.1 수리/점검 등록 시나리오
- [ ] QAID 검색 → 제품 정보 확인 → 증상 선택 → 처리 내용 선택 → 등급 선택 → 구성품 선택 → 수리비 확인 → 등록 플로우
- [ ] 각 단계별 유효성 검증
- [ ] 수리비 자동 산정 및 조정 기능
- [ ] 구성품 재고 차감 확인

### 3.2 제품 조회 시나리오
- [ ] QAID 검색 → 제품 정보 표시 → 이력 조회 플로우
- [ ] 제품 정보 정확성 확인
- [ ] 수리/점검 이력 표시 확인

### 3.3 구성품 조회 시나리오
- [ ] 카테고리 선택 → 구성품 목록 조회 → 검색/필터 플로우
- [ ] 구성품 정보 정확성 확인
- [ ] 재고 정보 표시 확인

## 4. 권한별 접근/동작 시나리오
- [ ] 관리자 권한으로 모든 기능 접근/수정 테스트
- [ ] 일반 사용자 권한으로 제한된 기능 접근 테스트
- [ ] 권한 없는 기능 접근 시 에러 처리 확인

## 5. 크로스 브라우저/환경 테스트
- [ ] Chrome, Firefox, Safari, Edge 브라우저별 동작 확인
- [ ] Windows, macOS, Linux OS별 동작 확인
- [ ] 모바일 브라우저 반응형 동작 확인
- [ ] Tauri 데스크톱 앱 동작 확인

## 6. 성능/부하/보안 테스트
- [ ] 대량 데이터 처리 시나리오 (1000개 이상 등급/처리내용/증상)
- [ ] 동시 사용자 처리 시나리오 (10명 이상 동시 접근)
- [ ] 네트워크 지연/끊김 상황 처리
- [ ] SQL 인젝션, XSS 등 보안 취약점 테스트
- [ ] 인증/권한 우회 시도 테스트

## 7. 데이터 무결성 테스트
- [ ] 트랜잭션 롤백 시나리오
- [ ] 외래키 제약조건 위반 시나리오
- [ ] 데이터 동기화 시나리오 (등급-처리내용-증상 관계)
- [ ] 백업/복구 시나리오 