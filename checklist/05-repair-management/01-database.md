# 수리/점검 관리 - 데이터베이스 설계

## 1. 수리비 관리 테이블 설계
- [x] repair_categories 테이블 마이그레이션 (2025_04_23_000000_create_repair_categories_table.php)
- [x] repair_fee_ranges 테이블 마이그레이션 (2025_04_23_000001_create_repair_fee_ranges_table.php)
- [x] repair_fees 테이블 마이그레이션 (2025_04_23_000002_create_repair_fees_table.php)
- [x] 수리비 관련 인덱스 및 제약조건 최적화

## 2. 구성품 관리 테이블 설계
- [x] repair_parts_categories 테이블 마이그레이션 (2025_04_23_000009_create_repair_parts_categories_table.php)
- [x] repair_parts 테이블 마이그레이션 (2025_04_23_000010_create_repair_parts_table.php)
- [x] repair_parts_orders 테이블 마이그레이션 (2025_04_23_000011_create_repair_parts_orders_table.php)
- [x] repair_parts_logs 테이블 마이그레이션 (2025_04_23_000012_create_repair_parts_logs_table.php)
- [x] 구성품 관련 인덱스 및 제약조건 최적화

## 3. 수리 증상/처리 내용/등급 관리 테이블 설계
- [x] repair_grades 테이블 마이그레이션 (2025_06_11_100000_create_repair_grades_table.php)
- [x] repair_processes 테이블 마이그레이션 (2025_06_11_100001_create_repair_processes_table.php)
- [x] repair_symptoms 테이블 마이그레이션 (2025_06_11_100002_create_repair_symptoms_table.php)
- [x] repair_process_repair_symptom 연결 테이블 마이그레이션 (2025_06_11_100003_create_repair_process_repair_symptom_table.php)
- [x] repair_grade_repair_process 연결 테이블 마이그레이션 (2025_06_11_100004_create_repair_grade_repair_process_table.php)
- [x] 관계 테이블 인덱스 최적화

## 4. 수리 제품 관리 테이블 설계
- [x] repair_products 테이블 마이그레이션 (2025_05_26_105649_create_repair_products_table.php)
- [x] repair_product_parts 테이블 마이그레이션 (2025_05_26_105650_create_repair_product_parts_table.php)
- [x] 수리 제품 관련 인덱스 및 제약조건 최적화

## 5. Eloquent 모델 구현
- [x] RepairCategory, RepairFeeRange, RepairFee 모델 생성
- [x] RepairParts, RepairPartsCategory, RepairPartsLog, RepairPartsOrder 모델 생성
- [x] RepairSymptom, RepairProcess, RepairGrade 모델 생성
- [x] RepairProcessRepairSymptom, RepairGradeRepairProcess 연결 모델 생성
- [x] RepairProduct, RepairProductParts 모델 생성
- [x] 모델 간 관계 설정 (hasMany, belongsToMany, belongsTo 등)
- [x] 모델 단위 테스트 (Pest)
- [x] 모델 캐스팅 및 액세서/뮤테이터 설정

## 6. 데이터베이스 시드/초기 데이터
> **중요**: 시드 데이터를 생성하기 전 각 테이블(모델)의 $fillable 을 확인하고 시드 데이터를 작성할 것
- [ ] repair_categories 시드 데이터 작성
- [ ] repair_fee_ranges 및 repair_fees 시드 데이터 작성
- [ ] repair_parts_categories, repair_parts, repair_parts_logs, repair_parts_orders 시드 데이터 작성
- [ ] 수리 등급, 처리 내용, 증상 시드 데이터 작성
- [ ] 관계 데이터 시드 작성 (증상-처리, 처리-등급)
- [ ] 테스트용 수리 제품 데이터 시드 작성 