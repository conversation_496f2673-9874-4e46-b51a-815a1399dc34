# 코너스톤 프로젝트 WMS 체크리스트 가이드

## 📋 체크리스트 구조 개요

이 디렉토리는 코너스톤 프로젝트 WMS(창고관리시스템) 개발 작업의 모든 체크리스트를 관리합니다.

## 📂 디렉토리 구조

```
checklist/
├── README.md                    # 이 파일 (체크리스트 가이드)
├── checklist.md                 # 전체 프로젝트 요약 체크리스트 (379줄)
├── checklist-00.md              # 동적 WorkStatus 관리 시스템 (최우선 작업, 871줄)
├── checklist-03.md              # 입고 관리 관련 체크리스트 (265줄)
├── 01-project-setup/            # 프로젝트 초기 설정
├── 02-auth-management/          # 인증 시스템
├── 03-incoming-management/      # 입고 관리
├── 04-inspection-management/    # 검수 관리
├── 05-repair-management/        # 수리 관리
├── 06-pallet-management/        # 팔레트 관리
├── 07-shipment-management/      # 출고 관리
├── 08-carryout-management/      # 반출 관리
├── 09-board-management/         # 게시판 관리
├── 10-settings-management/      # 설정 관리
├── 11-qaid-management/          # QAID 관리
├── 12-testing/                  # 테스트
├── 13-deployment/               # 배포 및 운영
└── 14-documentation/            # 문서화
```

## 🎯 작업 우선순위

### 1단계: 최우선 작업
- **[checklist-00.md](./checklist-00.md)** - 동적 WorkStatus 관리 시스템 구현
  - 가장 중요한 핵심 기능으로 최우선 구현 필요
  - 871줄의 상세한 체크리스트 포함

### 2단계: 프로젝트 기반 구축
- **[01-project-setup/](./01-project-setup/)** - 프로젝트 초기 설정
- **[02-auth-management/](./02-auth-management/)** - 인증 시스템

### 3단계: 핵심 비즈니스 로직
- **[03-incoming-management/](./03-incoming-management/)** - 입고 관리
- **[04-inspection-management/](./04-inspection-management/)** - 검수 관리
- **[06-pallet-management/](./06-pallet-management/)** - 팔레트 관리

### 4단계: 운영 관리 기능
- **[05-repair-management/](./05-repair-management/)** - 수리/점검 관리
- **[07-shipment-management/](./07-shipment-management/)** - 출고 관리
- **[08-carryout-management/](./08-carryout-management/)** - 반출 관리

### 5단계: 시스템 관리
- **[09-board-management/](./09-board-management/)** - 게시판 관리
- **[10-settings-management/](./10-settings-management/)** - 설정 관리
- **[11-qaid-management/](./11-qaid-management/)** - QAID 관리

### 6단계: 품질 보증 및 배포
- **[12-testing/](./12-testing/)** - 테스트
- **[13-deployment/](./13-deployment/)** - 배포 및 운영
- **[14-documentation/](./14-documentation/)** - 문서화

## 📄 주요 체크리스트 파일

### 1. [checklist.md](./checklist.md) - 전체 요약
- 전체 프로젝트의 요약된 체크리스트
- 15개 주요 섹션으로 구성
- 각 기능별 백엔드/프론트엔드 작업 항목 포함

### 2. [checklist-00.md](./checklist-00.md) - 동적 WorkStatus 관리
- **최우선 구현 대상**
- 871줄의 상세한 체크리스트
- 동적 작업 상태 관리 시스템의 모든 세부사항 포함

### 3. [checklist-03.md](03-incoming-management/checklist-03.md) - 입고 관리
- 입고 관련 상세 체크리스트
- 265줄의 구체적인 작업 항목

## 🔧 기술 스택

### 백엔드
- **Laravel** - PHP 웹 프레임워크
- **Laravel Sanctum** - API 인증
- **Laravel Excel** - 엑셀 파일 처리
- **Laravel Scout (Meilisearch)** - 검색 기능
- **MariaDB** - 데이터베이스

### 프론트엔드
- **Tauri + SvelteKit** - 데스크톱 애플리케이션
- **Tailwind CSS** - 스타일링
- **daisyUI** - UI 컴포넌트 라이브러리
- **TypeScript** - 타입 안전성

### 인프라
- **Docker** - 컨테이너화
- **Docker Compose** - 멀티 컨테이너 관리

## 📝 작업 가이드라인

### 체크리스트 사용법
1. **우선순위 확인**: 현재 작업할 체크리스트 파일 확인
2. **세부 항목 검토**: 해당 디렉토리의 상세 체크리스트 참조
3. **진행 상황 체크**: 완료된 항목에 `[x]` 표시
4. **문서 업데이트**: 작업 완료 시 관련 문서 동기화

### 개발 원칙
- **TDD 방식**: 테스트 먼저 작성 후 코드 구현
- **백엔드 우선**: API 구현 후 프론트엔드 연동
- **에러 처리**: 모든 예외 상황에 대한 처리 구현
- **로깅**: 모든 작업에 대한 상세 로그 기록
- **보안**: 인증/권한/입력 검증 철저히 구현

### 품질 기준
- **테스트 커버리지**: 최소 80% 이상
- **코드 리뷰**: 모든 변경사항에 대한 리뷰 필수
- **문서화**: 코드와 함께 문서 업데이트
- **성능**: 대용량 데이터 처리 고려

## 🚀 빠른 시작

1. **최우선 작업 시작**: `checklist-00.md` 확인
2. **개발 환경 구축**: `01-project-setup/` 디렉토리 참조
3. **인증 시스템**: `02-auth-management/` 디렉토리 참조
4. **핵심 기능**: `03-incoming-management/` 부터 순차 진행

## 📞 지원

체크리스트 관련 문의사항이나 개선 제안이 있으시면 개발팀에 연락해주세요.

---

**마지막 업데이트**: 2025년 7월 9일  
**버전**: 1.0.0 