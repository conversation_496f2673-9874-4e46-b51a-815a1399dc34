# CNSProWMS 프론트엔드 개발 가이드

## 📋 프로젝트 개요

### 기술 스택
- **프론트엔드**: SvelteKit
- **스타일링**: Tailwind CSS + DaisyUI (향후 shadcn-svelte 마이그레이션 예정)
- **백엔드**: Laravel API
- **인증**: Laravel Sanctum
- **상태 관리**: Svelte Stores

### 프로젝트 구조
```
frontend/
├── src/
│   ├── components/        # 공통 컴포넌트
│   ├── pages/             # 페이지 컴포넌트
│   ├── stores/            # 상태 관리
│   ├── services/          # API 서비스
│   ├── utils/             # 유틸리티 함수
│   └── styles/            # 스타일 파일
├── static/                # 정적 파일
└── tests/                 # 테스트 파일
```

### 전체 라우터 구조
```
/wms/settings/dynamic-work-status/
├── categories/                    # 카테고리 관리
│   ├── GET    /                  # 목록 조회
│   ├── POST   /                  # 생성
│   ├── PUT    /{id}              # 수정
│   ├── DELETE /{id}              # 삭제
│   └── GET    /{code}/stats      # 통계 조회
├── actions/                      # 액션 관리
│   ├── GET    /{categoryId}      # 목록 조회
│   ├── POST   /                  # 생성
│   ├── PUT    /{id}              # 수정
│   └── DELETE /{id}              # 삭제
├── templates/                    # 템플릿 관리
│   ├── GET    /{categoryId}/{actionId}  # 목록 조회
│   ├── POST   /                  # 생성
│   ├── PUT    /{id}              # 수정
│   ├── DELETE /{id}              # 삭제
│   └── GET    /unused            # 미사용 템플릿 조회
├── generated-statuses/           # 생성된 상태 관리
│   ├── GET    /                  # 목록 조회
│   ├── GET    /stats             # 통계 조회
│   ├── GET    /recent            # 최근 생성된 상태
│   └── GET    /frequent          # 자주 사용되는 상태
└── system/                       # 시스템 관리
    ├── POST   /convert-legacy    # 레거시 변환
    └── POST   /clear-cache       # 캐시 초기화
```

### 프론트엔드 페이지별 필요 라우터
| 페이지 | 필요한 라우터 | 설명 |
|--------|---------------|------|
| 카테고리 관리 | `GET /categories`, `POST /categories`, `PUT /categories/{id}`, `DELETE /categories/{id}` | 카테고리 CRUD |
| 액션 관리 | `GET /actions/{categoryId}`, `POST /actions`, `PUT /actions/{id}`, `DELETE /actions/{id}` | 액션 CRUD |
| 템플릿 관리 | `GET /templates/{categoryId}/{actionId}`, `POST /templates`, `PUT /templates/{id}`, `DELETE /templates/{id}` | 템플릿 CRUD |
| 상태 모니터링 | `GET /generated-statuses`, `GET /generated-statuses/stats`, `GET /generated-statuses/recent`, `GET /generated-statuses/frequent` | 상태 조회 및 통계 |
| 시스템 관리 | `POST /system/convert-legacy`, `POST /system/clear-cache`, `GET /templates/unused` | 시스템 관리 |

### 데이터 흐름 예시
```javascript
// 1. 카테고리 선택 → 액션 목록 로드
const categoryId = selectedCategory.id;
const actions = await api.get(`/actions/${categoryId}`);

// 2. 액션 선택 → 템플릿 목록 로드
const actionId = selectedAction.id;
const templates = await api.get(`/templates/${categoryId}/${actionId}`);

// 3. 통계 데이터 조회
const categoryStats = await api.get(`/categories/${categoryCode}/stats`);
const generalStats = await api.get(`/generated-statuses/stats`);
```

## 🎯 우선순위 1: 동적 WorkStatus 관리 시스템 UI

### 1. 카테고리 관리 페이지
**경로**: `/wms/settings/dynamic-work-status/categories`

#### 기능 요구사항
- [ ] 카테고리 목록 조회 및 표시
- [ ] 카테고리 생성 폼 (모달 또는 별도 페이지)
- [ ] 카테고리 수정 기능
- [ ] 카테고리 삭제 기능 (확인 다이얼로그 포함)
- [ ] 카테고리별 통계 조회 및 시각화

#### API 엔드포인트
```javascript
// 카테고리 목록 조회
GET /api/wms/settings/dynamic-work-status/categories

// 카테고리 생성
POST /api/wms/settings/dynamic-work-status/categories
{
  "code": "QUALITY_CHECK",
  "name": "품질 검사",
  "description": "제품 품질 검사 관련 작업",
  "is_active": true
}

// 카테고리 수정
PUT /api/wms/settings/dynamic-work-status/categories/{id}
{
  "name": "품질 검사 (수정)",
  "description": "수정된 설명",
  "is_active": true
}

// 카테고리 삭제
DELETE /api/wms/settings/dynamic-work-status/categories/{id}

// 카테고리별 통계 조회
GET /api/wms/settings/dynamic-work-status/categories/{code}/stats
```

#### 컴포넌트 구조
```
CategoryManagement/
├── CategoryList.svelte          # 카테고리 목록 표시
├── CategoryForm.svelte          # 카테고리 생성/수정 폼
├── CategoryDeleteModal.svelte   # 삭제 확인 모달
├── CategoryStats.svelte         # 통계 시각화
└── CategoryTable.svelte         # 테이블 컴포넌트
```

#### 상태 관리 (Svelte Store)
```javascript
// stores/categoryStore.js
import { writable } from 'svelte/store';

export const categories = writable([]);
export const selectedCategory = writable(null);
export const categoryStats = writable({});
export const loading = writable(false);
export const error = writable(null);
```

### 2. 액션 관리 페이지
**경로**: `/wms/settings/dynamic-work-status/actions`

#### 기능 요구사항
- [x] 카테고리별 액션 목록 조회
- [x] 액션 생성 폼 (부모 액션 선택 지원)
- [x] 액션 수정 기능
- [x] 액션 삭제 기능
- [x] 계층 구조 시각화 (트리 뷰)
- [x] 액션 순서 변경 (드래그 앤 드롭)

#### API 엔드포인트
```javascript
// 카테고리별 액션 목록 조회
GET /api/wms/settings/dynamic-work-status/actions/{categoryId}

// 액션 생성
POST /api/wms/settings/dynamic-work-status/actions
{
  "category_id": 1,
  "code": "DEFECT_FOUND",
  "name": "결함 발견",
  "description": "제품에서 결함이 발견된 상태",
  "parent_id": null,
  "sort_order": 1,
  "is_active": true
}

// 액션 수정
PUT /api/wms/settings/dynamic-work-status/actions/{id}

// 액션 삭제
DELETE /api/wms/settings/dynamic-work-status/actions/{id}
```

#### 컴포넌트 구조
```
ActionManagement/
├── ActionList.svelte            # 액션 목록
├── ActionTreeView.svelte        # 계층 구조 트리
├── ActionForm.svelte            # 액션 생성/수정 폼
├── ActionDeleteModal.svelte     # 삭제 확인 모달
├── ActionSortable.svelte        # 드래그 앤 드롭 정렬
└── ParentSelector.svelte        # 부모 액션 선택기
```

### 3. 템플릿 관리 페이지
**경로**: `/wms/settings/dynamic-work-status/templates`

#### 기능 요구사항
- [x] 카테고리/액션별 템플릿 목록 조회
- [x] 템플릿 생성 폼 (조건부 생성 규칙 설정)
- [x] 템플릿 수정 기능
- [x] 템플릿 삭제 기능
- [x] 템플릿 미리보기 (생성될 상태 예시)
- [x] 조건부 생성 규칙 시각화
- [x] 사용되지 않는 템플릿 관리

#### API 엔드포인트
```javascript
// 카테고리/액션별 템플릿 목록 조회
GET /api/wms/settings/dynamic-work-status/templates/{categoryId}/{actionId}

// 템플릿 생성
POST /api/wms/settings/dynamic-work-status/templates
{
  "category_id": 1,
  "action_id": 1,
  "template_code": "DEFECT_TEMPLATE",
  "name": "결함 발견 - {{defect_type}}",
  "description": "{{defect_type}} 유형의 결함이 발견됨",
  "conditions": {
    "defect_type": "required",
    "severity": "optional"
  },
  "is_active": true
}

// 템플릿 수정
PUT /api/wms/settings/dynamic-work-status/templates/{id}

// 템플릿 삭제
DELETE /api/wms/settings/dynamic-work-status/templates/{id}

// 미사용 템플릿 조회
GET /api/wms/settings/dynamic-work-status/templates/unused
```

#### 컴포넌트 구조
```
TemplateManagement/
├── TemplateList.svelte          # 템플릿 목록
├── TemplateForm.svelte          # 템플릿 생성/수정 폼
├── ConditionBuilder.svelte      # 조건 규칙 빌더
├── TemplatePreview.svelte       # 템플릿 미리보기
├── UnusedTemplates.svelte       # 미사용 템플릿 관리
└── TemplateDeleteModal.svelte   # 삭제 확인 모달
```

#### 조건부 생성 규칙 JSON 구조
```javascript
// 조건부 생성 규칙 예시
const conditionExample = {
  "defect_type": {
    "type": "required",
    "validation": "string",
    "options": ["scratch", "dent", "crack"]
  },
  "severity": {
    "type": "optional",
    "validation": "integer",
    "min": 1,
    "max": 5,
    "default": 3
  },
  "location": {
    "type": "conditional",
    "condition": "defect_type === 'scratch'",
    "validation": "string"
  }
};
```

### 4. 생성된 상태 모니터링 페이지
**경로**: `/wms/settings/dynamic-work-status/generated-statuses`

#### 기능 요구사항
- [x] 자동 생성된 상태 목록 조회
- [x] 상태 검색 및 필터링 (카테고리, 액션, 템플릿별)
- [x] 사용 통계 시각화
- [x] 최근 생성된 상태 조회
- [x] 자주 사용되는 상태 조회
- [x] 상태 사용 히스토리 조회

#### API 엔드포인트
```javascript
// 생성된 상태 목록 조회
GET /api/wms/settings/dynamic-work-status/generated-statuses
?category_id=1&action_id=1&template_id=1&page=1&limit=20

// 통계 정보 조회
GET /api/wms/settings/dynamic-work-status/generated-statuses/stats

// 최근 생성된 상태 조회
GET /api/wms/settings/dynamic-work-status/generated-statuses/recent?limit=10

// 자주 사용되는 상태 조회
GET /api/wms/settings/dynamic-work-status/generated-statuses/frequent?limit=10
```

#### 컴포넌트 구조
```
GeneratedStatusMonitoring/
├── StatusList.svelte            # 상태 목록
├── StatusFilters.svelte         # 필터링 컴포넌트
├── StatusStats.svelte           # 통계 시각화
├── RecentStatuses.svelte        # 최근 생성 상태
├── FrequentStatuses.svelte      # 자주 사용 상태
└── StatusUsageChart.svelte      # 사용량 차트
```

### 5. 시스템 관리 페이지
**경로**: `/wms/settings/dynamic-work-status/system`

#### 기능 요구사항
- [x] 레거시 상수 변환 기능
- [x] 캐시 초기화 기능
- [x] 시스템 상태 모니터링
- [x] 데이터 무결성 검증
- [x] 마이그레이션 상태 확인

#### API 엔드포인트
```javascript
// 레거시 상수 변환
POST /api/wms/settings/dynamic-work-status/system/convert-legacy

// 캐시 초기화
POST /api/wms/settings/dynamic-work-status/system/clear-cache

// 시스템 상태 조회
GET /api/wms/settings/dynamic-work-status/system/status

// 데이터 무결성 검증
POST /api/wms/settings/dynamic-work-status/system/validate-data
```

#### 컴포넌트 구조
```
SystemManagement/
├── LegacyConverter.svelte       # 레거시 변환 도구
├── CacheManager.svelte          # 캐시 관리 도구
├── SystemStatus.svelte          # 시스템 상태 모니터링
├── DataValidator.svelte         # 데이터 무결성 검증
└── MigrationStatus.svelte       # 마이그레이션 상태
```

### 6. 기존 화면 업데이트

#### 동적 상태 선택 컴포넌트
```javascript
// DynamicStatusSelector.svelte
<script>
  import { onMount } from 'svelte';
  import { dynamicStatusStore } from '../stores/dynamicStatusStore';
  
  export let categoryCode;
  export let actionCode;
  export let context = {};
  export let selectedStatusId = null;
  
  let availableStatuses = [];
  let loading = false;
  
  $: loadStatuses(categoryCode, actionCode, context);
  
  async function loadStatuses(category, action, ctx) {
    if (!category || !action) return;
    
    loading = true;
    try {
      const response = await fetch(`/api/dynamic-work-status/get-or-create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${$authStore.token}`
        },
        body: JSON.stringify({
          category_code: category,
          action_code: action,
          context: ctx
        })
      });
      
      const data = await response.json();
      availableStatuses = data.statuses || [];
    } catch (error) {
      console.error('상태 조회 실패:', error);
    } finally {
      loading = false;
    }
  }
</script>

<div class="status-selector">
  <label class="label">
    <span class="label-text">상태 선택</span>
  </label>
  
  {#if loading}
    <div class="loading loading-spinner loading-sm"></div>
  {:else}
    <select 
      class="select select-bordered w-full"
      bind:value={selectedStatusId}
    >
      <option value="">상태를 선택하세요</option>
      {#each availableStatuses as status}
        <option value={status.id}>{status.name}</option>
      {/each}
    </select>
  {/if}
</div>
```

## 🎯 우선순위 2: 제품 이력 추적 시스템 UI

### 1. 제품 이력 타임라인 컴포넌트
**경로**: `/products/{id}/history`

#### 기능 요구사항
- [x] 시간순 이력 표시 (타임라인 형태)
- [x] 상태별 색상 구분
- [x] 상세 정보 툴팁
- [x] 이력 필터링 (날짜, 상태, 직원별)
- [x] 무한 스크롤 또는 페이지네이션

#### 컴포넌트 구조
```
ProductHistory/
├── ProductTimeline.svelte       # 메인 타임라인
├── TimelineItem.svelte          # 개별 이력 항목
├── HistoryFilter.svelte         # 필터링 컴포넌트
├── HistoryTooltip.svelte        # 상세 정보 툴팁
└── HistoryPagination.svelte     # 페이지네이션
```

### 2. 이력 분석 대시보드
**경로**: `/analytics/product-history`

#### 기능 요구사항
- [x] 차트 및 그래프 시각화
- [x] 필터링 및 검색 기능
- [x] 실시간 데이터 업데이트
- [x] 대시보드 레이아웃 커스터마이징

#### 컴포넌트 구조
```
HistoryAnalytics/
├── AnalyticsDashboard.svelte    # 메인 대시보드
├── ProductFlowChart.svelte      # 제품 흐름 차트
├── ProcessingTimeChart.svelte   # 처리 시간 차트
├── EmployeeProductivity.svelte  # 직원 생산성 차트
└── RealTimeUpdates.svelte       # 실시간 업데이트
```

### 3. 통계 리포트 페이지
**경로**: `/reports/statistics`

#### 기능 요구사항
- [x] 직원별 생산성 리포트
- [x] 제품별 처리 시간 분석
- [x] 기간별 통계 비교
- [x] 리포트 내보내기 (PDF, Excel)

#### 컴포넌트 구조
```
StatisticsReport/
├── ReportDashboard.svelte       # 리포트 대시보드
├── EmployeeReport.svelte        # 직원 리포트
├── ProductReport.svelte         # 제품 리포트
├── ComparisonReport.svelte      # 비교 리포트
└── ReportExporter.svelte        # 리포트 내보내기
```

## 🎯 우선순위 3: 사용자 인터페이스 개선

### 1. 반응형 디자인 적용
- [x] 모바일 최적화 (768px 이하)
- [x] 태블릿 지원 (768px - 1024px)
- [x] 데스크톱 최적화 (1024px 이상)

#### Tailwind CSS 브레이크포인트
```css
/* 모바일 */
@media (max-width: 768px) {
  .mobile-optimized {
    @apply flex-col space-y-2;
  }
}

/* 태블릿 */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-optimized {
    @apply grid grid-cols-2 gap-4;
  }
}

/* 데스크톱 */
@media (min-width: 1024px) {
  .desktop-optimized {
    @apply grid grid-cols-3 gap-6;
  }
}
```

### 2. 사용자 경험 개선
- [x] 로딩 상태 표시
- [x] 에러 메시지 개선
- [x] 키보드 단축키 지원
- [x] 접근성 향상

#### 로딩 상태 컴포넌트
```svelte
<!-- LoadingSpinner.svelte -->
<script>
  export let size = 'md';
  export let message = '로딩 중...';
</script>

<div class="flex items-center justify-center p-4">
  <div class="loading loading-spinner loading-{size}"></div>
  {#if message}
    <span class="ml-2 text-sm text-gray-600">{message}</span>
  {/if}
</div>
```

#### 에러 메시지 컴포넌트
```svelte
<!-- ErrorMessage.svelte -->
<script>
  export let error;
  export let retry = null;
</script>

<div class="alert alert-error">
  <svg class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
  <span>{error.message || '알 수 없는 오류가 발생했습니다.'}</span>
  {#if retry}
    <button class="btn btn-sm btn-outline" on:click={retry}>다시 시도</button>
  {/if}
</div>
```

## 📊 데이터 구조 및 API 통신

### 1. API 서비스 구조
```javascript
// services/api.js
class ApiService {
  constructor(baseURL = '/api') {
    this.baseURL = baseURL;
    this.token = null;
  }

  setToken(token) {
    this.token = token;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`API 요청 실패: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API 요청 오류:', error);
      throw error;
    }
  }

  // CRUD 메서드
  get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`${endpoint}${queryString ? `?${queryString}` : ''}`);
  }

  post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE',
    });
  }
}

export default new ApiService();
```

### 2. 동적 WorkStatus API 서비스
```javascript
// services/dynamicWorkStatusService.js
import api from './api';

class DynamicWorkStatusService {
  // 카테고리 관련
  async getCategories() {
    return api.get('/wms/settings/dynamic-work-status/categories');
  }

  async createCategory(data) {
    return api.post('/wms/settings/dynamic-work-status/categories', data);
  }

  async updateCategory(id, data) {
    return api.put(`/wms/settings/dynamic-work-status/categories/${id}`, data);
  }

  async deleteCategory(id) {
    return api.delete(`/wms/settings/dynamic-work-status/categories/${id}`);
  }

  async getCategoryStats(code) {
    return api.get(`/wms/settings/dynamic-work-status/categories/${code}/stats`);
  }

  // 액션 관련
  async getActions(categoryId) {
    return api.get(`/wms/settings/dynamic-work-status/actions/${categoryId}`);
  }

  async createAction(data) {
    return api.post('/wms/settings/dynamic-work-status/actions', data);
  }

  async updateAction(id, data) {
    return api.put(`/wms/settings/dynamic-work-status/actions/${id}`, data);
  }

  async deleteAction(id) {
    return api.delete(`/wms/settings/dynamic-work-status/actions/${id}`);
  }

  // 템플릿 관련
  async getTemplates(categoryId, actionId) {
    return api.get(`/wms/settings/dynamic-work-status/templates/${categoryId}/${actionId}`);
  }

  async createTemplate(data) {
    return api.post('/wms/settings/dynamic-work-status/templates', data);
  }

  async updateTemplate(id, data) {
    return api.put(`/wms/settings/dynamic-work-status/templates/${id}`, data);
  }

  async deleteTemplate(id) {
    return api.delete(`/wms/settings/dynamic-work-status/templates/${id}`);
  }

  async getUnusedTemplates() {
    return api.get('/wms/settings/dynamic-work-status/templates/unused');
  }

  // 생성된 상태 관련
  async getGeneratedStatuses(params = {}) {
    return api.get('/wms/settings/dynamic-work-status/generated-statuses', params);
  }

  async getGeneratedStatusStats() {
    return api.get('/wms/settings/dynamic-work-status/generated-statuses/stats');
  }

  async getRecentStatuses(limit = 10) {
    return api.get('/wms/settings/dynamic-work-status/generated-statuses/recent', { limit });
  }

  async getFrequentStatuses(limit = 10) {
    return api.get('/wms/settings/dynamic-work-status/generated-statuses/frequent', { limit });
  }

  // 시스템 관리
  async convertLegacy() {
    return api.post('/wms/settings/dynamic-work-status/system/convert-legacy');
  }

  async clearCache() {
    return api.post('/wms/settings/dynamic-work-status/system/clear-cache');
  }

  // 동적 상태 생성/조회
  async getOrCreateStatus(categoryCode, actionCode, context = {}) {
    return api.post('/wms/settings/dynamic-work-status/get-or-create', {
      category_code: categoryCode,
      action_code: actionCode,
      context
    });
  }
}

export default new DynamicWorkStatusService();
```

### 3. 상태 관리 (Svelte Stores)
```javascript
// stores/dynamicWorkStatusStore.js
import { writable, derived } from 'svelte/store';
import dynamicWorkStatusService from '../services/dynamicWorkStatusService';

// 기본 상태들
export const categories = writable([]);
export const actions = writable([]);
export const templates = writable([]);
export const generatedStatuses = writable([]);

// 선택된 항목들
export const selectedCategory = writable(null);
export const selectedAction = writable(null);
export const selectedTemplate = writable(null);

// UI 상태
export const loading = writable(false);
export const error = writable(null);

// 필터링 상태
export const filters = writable({
  categoryId: null,
  actionId: null,
  templateId: null,
  dateRange: null
});

// Derived stores
export const filteredActions = derived(
  [actions, selectedCategory],
  ([$actions, $selectedCategory]) => {
    if (!$selectedCategory) return [];
    return $actions.filter(action => action.category_id === $selectedCategory.id);
  }
);

export const filteredTemplates = derived(
  [templates, selectedCategory, selectedAction],
  ([$templates, $selectedCategory, $selectedAction]) => {
    if (!$selectedCategory || !$selectedAction) return [];
    return $templates.filter(template => 
      template.category_id === $selectedCategory.id && 
      template.action_id === $selectedAction.id
    );
  }
);

// 액션 함수들
export const categoryActions = {
  async load() {
    loading.set(true);
    try {
      const response = await dynamicWorkStatusService.getCategories();
      categories.set(response.data);
    } catch (err) {
      error.set(err.message);
    } finally {
      loading.set(false);
    }
  },

  async create(data) {
    try {
      const response = await dynamicWorkStatusService.createCategory(data);
      categories.update(list => [...list, response.data]);
      return response.data;
    } catch (err) {
      error.set(err.message);
      throw err;
    }
  },

  async update(id, data) {
    try {
      const response = await dynamicWorkStatusService.updateCategory(id, data);
      categories.update(list => 
        list.map(item => item.id === id ? response.data : item)
      );
      return response.data;
    } catch (err) {
      error.set(err.message);
      throw err;
    }
  },

  async delete(id) {
    try {
      await dynamicWorkStatusService.deleteCategory(id);
      categories.update(list => list.filter(item => item.id !== id));
    } catch (err) {
      error.set(err.message);
      throw err;
    }
  }
};
```

## 🎨 스타일 가이드

### 1. DaisyUI 컴포넌트 사용
```svelte
<!-- 버튼 스타일 -->
<button class="btn btn-primary">Primary Button</button>
<button class="btn btn-secondary">Secondary Button</button>
<button class="btn btn-success">Success Button</button>
<button class="btn btn-error">Error Button</button>

<!-- 카드 스타일 -->
<div class="card w-96 bg-base-100 shadow-xl">
  <div class="card-body">
    <h2 class="card-title">Card Title</h2>
    <p>Card content</p>
    <div class="card-actions justify-end">
      <button class="btn btn-primary">Action</button>
    </div>
  </div>
</div>

<!-- 테이블 스타일 -->
<div class="overflow-x-auto">
  <table class="table table-zebra">
    <thead>
      <tr>
        <th>Name</th>
        <th>Job</th>
        <th>Company</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>Cy Ganderton</td>
        <td>Quality Control Specialist</td>
        <td>Littel, Schaden and Vandervort</td>
      </tr>
    </tbody>
  </table>
</div>
```

### 2. 반응형 그리드 시스템
```css
/* 반응형 그리드 */
.responsive-grid {
  @apply grid gap-4;
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

/* 반응형 플렉스 */
.responsive-flex {
  @apply flex flex-col md:flex-row;
  @apply space-y-4 md:space-y-0 md:space-x-4;
}

/* 반응형 패딩 */
.responsive-padding {
  @apply p-4 md:p-6 lg:p-8;
}
```

### 3. 컴포넌트 스타일링 가이드
```svelte
<script>
  // 컴포넌트 props
  export let size = 'md';
  export let variant = 'primary';
  export let disabled = false;
  
  // 동적 클래스 생성
  $: buttonClass = `btn btn-${variant} btn-${size} ${disabled ? 'btn-disabled' : ''}`;
</script>

<button 
  class={buttonClass}
  {disabled}
  on:click
>
  <slot></slot>
</button>

<style>
  /* 컴포넌트 특화 스타일 */
  .btn-custom {
    @apply transition-all duration-200 ease-in-out;
    @apply hover:scale-105 active:scale-95;
  }
</style>
```

## 🧪 테스트 가이드

### 테스트 환경 설정
```javascript
// vitest.config.js
import { defineConfig } from 'vitest/config';
import { svelte } from '@sveltejs/vite-plugin-svelte';

export default defineConfig({
  plugins: [svelte({ hot: !process.env.VITEST })],
  test: {
    environment: 'jsdom',
    setupFiles: ['./tests/setup.js'],
    globals: true,
  },
});
```

```javascript
// tests/setup.js
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// 전역 모킹
global.fetch = vi.fn();
global.localStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
```

### 1. 단위 테스트 (Vitest)
```javascript
// tests/components/CategoryForm.test.js
import { render, fireEvent, waitFor } from '@testing-library/svelte';
import { vi } from 'vitest';
import CategoryForm from '../src/components/CategoryForm.svelte';

// 모킹 설정
const mockSubmit = vi.fn();

describe('CategoryForm', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('폼 제출 시 올바른 데이터 전송', async () => {
    const { getByLabelText, getByText } = render(CategoryForm, {
      props: { onSubmit: mockSubmit }
    });
    
    const codeInput = getByLabelText('코드');
    const nameInput = getByLabelText('이름');
    const descriptionInput = getByLabelText('설명');
    const submitButton = getByText('생성');
    
    await fireEvent.input(codeInput, { target: { value: 'TEST_CODE' } });
    await fireEvent.input(nameInput, { target: { value: '테스트 카테고리' } });
    await fireEvent.input(descriptionInput, { target: { value: '테스트 설명' } });
    await fireEvent.click(submitButton);
    
    // 검증 로직
    expect(mockSubmit).toHaveBeenCalledWith({
      code: 'TEST_CODE',
      name: '테스트 카테고리',
      description: '테스트 설명',
      is_active: true
    });
  });

  test('필수 필드 유효성 검사', async () => {
    const { getByText, getByLabelText } = render(CategoryForm, {
      props: { onSubmit: mockSubmit }
    });
    
    const submitButton = getByText('생성');
    await fireEvent.click(submitButton);
    
    // 에러 메시지 확인
    await waitFor(() => {
      expect(getByText('코드는 필수입니다')).toBeInTheDocument();
      expect(getByText('이름은 필수입니다')).toBeInTheDocument();
    });
    
    expect(mockSubmit).not.toHaveBeenCalled();
  });
});
```

### 2. Store 테스트
```javascript
// tests/stores/dynamicWorkStatusStore.test.js
import { get } from 'svelte/store';
import { vi } from 'vitest';
import { 
  categories, 
  selectedCategory, 
  categoryActions 
} from '../src/stores/dynamicWorkStatusStore';
import dynamicWorkStatusService from '../src/services/dynamicWorkStatusService';

// 서비스 모킹
vi.mock('../src/services/dynamicWorkStatusService');

describe('dynamicWorkStatusStore', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // 스토어 초기화
    categories.set([]);
    selectedCategory.set(null);
  });

  test('카테고리 로드 성공', async () => {
    const mockCategories = [
      { id: 1, code: 'REPAIR', name: '수리' },
      { id: 2, code: 'PALLET', name: '팔레트' }
    ];

    dynamicWorkStatusService.getCategories.mockResolvedValue({
      data: mockCategories
    });

    await categoryActions.load();

    expect(get(categories)).toEqual(mockCategories);
  });

  test('카테고리 생성 성공', async () => {
    const newCategory = { id: 3, code: 'NEW', name: '새 카테고리' };
    
    dynamicWorkStatusService.createCategory.mockResolvedValue({
      data: newCategory
    });

    await categoryActions.create({ code: 'NEW', name: '새 카테고리' });

    const currentCategories = get(categories);
    expect(currentCategories).toContain(newCategory);
  });

  test('카테고리 삭제 성공', async () => {
    const mockCategories = [
      { id: 1, code: 'REPAIR', name: '수리' },
      { id: 2, code: 'PALLET', name: '팔레트' }
    ];
    
    categories.set(mockCategories);
    
    dynamicWorkStatusService.deleteCategory.mockResolvedValue({});

    await categoryActions.delete(1);

    const currentCategories = get(categories);
    expect(currentCategories).not.toContain(mockCategories[0]);
    expect(currentCategories).toHaveLength(1);
  });
});
```

### 3. 통합 테스트
```javascript
// tests/integration/dynamicWorkStatus.test.js
import { render, fireEvent, waitFor } from '@testing-library/svelte';
import DynamicWorkStatusPage from '../src/pages/DynamicWorkStatusPage.svelte';
import { categoryActions } from '../src/stores/dynamicWorkStatusStore';
import { vi } from 'vitest';

// 모킹 설정
vi.mock('../src/services/dynamicWorkStatusService');

describe('DynamicWorkStatus Integration', () => {
  test('카테고리 생성부터 템플릿 생성까지 전체 플로우', async () => {
    const { getByText, getByLabelText, getByTestId } = render(DynamicWorkStatusPage);
    
    // 1. 카테고리 생성 버튼 클릭
    const createCategoryBtn = getByText('카테고리 생성');
    await fireEvent.click(createCategoryBtn);
    
    // 2. 카테고리 폼 작성
    const categoryCodeInput = getByLabelText('카테고리 코드');
    const categoryNameInput = getByLabelText('카테고리 이름');
    
    await fireEvent.input(categoryCodeInput, { target: { value: 'TEST_CATEGORY' } });
    await fireEvent.input(categoryNameInput, { target: { value: '테스트 카테고리' } });
    
    // 3. 카테고리 저장
    const saveCategoryBtn = getByText('저장');
    await fireEvent.click(saveCategoryBtn);
    
    // 4. 카테고리 생성 확인
    await waitFor(() => {
      expect(getByText('테스트 카테고리')).toBeInTheDocument();
    });
    
    // 5. 액션 생성
    const createActionBtn = getByText('액션 생성');
    await fireEvent.click(createActionBtn);
    
    // 6. 액션 폼 작성
    const actionCodeInput = getByLabelText('액션 코드');
    const actionNameInput = getByLabelText('액션 이름');
    
    await fireEvent.input(actionCodeInput, { target: { value: 'TEST_ACTION' } });
    await fireEvent.input(actionNameInput, { target: { value: '테스트 액션' } });
    
    // 7. 액션 저장
    const saveActionBtn = getByText('저장');
    await fireEvent.click(saveActionBtn);
    
    // 8. 템플릿 생성
    const createTemplateBtn = getByText('템플릿 생성');
    await fireEvent.click(createTemplateBtn);
    
    // 9. 템플릿 폼 작성
    const templateCodeInput = getByLabelText('템플릿 코드');
    const templateNameInput = getByLabelText('템플릿 이름');
    
    await fireEvent.input(templateCodeInput, { target: { value: 'TEST_TEMPLATE' } });
    await fireEvent.input(templateNameInput, { target: { value: '테스트 템플릿' } });
    
    // 10. 템플릿 저장
    const saveTemplateBtn = getByText('저장');
    await fireEvent.click(saveTemplateBtn);
    
    // 11. 상태 생성 확인
    await waitFor(() => {
      expect(getByText('테스트 템플릿')).toBeInTheDocument();
    });
  });
  
  test('상태 필터링 및 검색 기능', async () => {
    const { getByPlaceholderText, getByText } = render(DynamicWorkStatusPage);
    
    // 검색 기능 테스트
    const searchInput = getByPlaceholderText('상태 검색...');
    await fireEvent.input(searchInput, { target: { value: '테스트' } });
    
    // 필터 적용 확인
    await waitFor(() => {
      expect(getByText('테스트 템플릿')).toBeInTheDocument();
         });
   });
 });
 ```

### 4. E2E 테스트 (Playwright)
```javascript
// tests/e2e/dynamic-work-status.spec.js
import { test, expect } from '@playwright/test';

test.describe('동적 WorkStatus 관리 시스템', () => {
  test.beforeEach(async ({ page }) => {
    // 로그인 처리
    await page.goto('/login');
    await page.fill('[data-testid="username"]', 'admin');
    await page.fill('[data-testid="password"]', 'password');
    await page.click('[data-testid="login-button"]');
    
    // 동적 WorkStatus 페이지로 이동
    await page.goto('/wms/settings/dynamic-work-status');
  });

  test('카테고리 CRUD 전체 플로우', async ({ page }) => {
    // 카테고리 생성
    await page.click('[data-testid="create-category-btn"]');
    await page.fill('[data-testid="category-code"]', 'E2E_TEST');
    await page.fill('[data-testid="category-name"]', 'E2E 테스트 카테고리');
    await page.fill('[data-testid="category-description"]', 'E2E 테스트용 카테고리입니다');
    await page.click('[data-testid="save-category-btn"]');
    
    // 카테고리 생성 확인
    await expect(page.locator('text=E2E 테스트 카테고리')).toBeVisible();
    
    // 카테고리 수정
    await page.click('[data-testid="edit-category-btn"]');
    await page.fill('[data-testid="category-name"]', 'E2E 테스트 카테고리 (수정됨)');
    await page.click('[data-testid="save-category-btn"]');
    
    // 카테고리 수정 확인
    await expect(page.locator('text=E2E 테스트 카테고리 (수정됨)')).toBeVisible();
    
    // 카테고리 삭제
    await page.click('[data-testid="delete-category-btn"]');
    await page.click('[data-testid="confirm-delete-btn"]');
    
    // 카테고리 삭제 확인
    await expect(page.locator('text=E2E 테스트 카테고리 (수정됨)')).not.toBeVisible();
  });

  test('액션 생성 및 계층 구조', async ({ page }) => {
    // 카테고리 선택
    await page.click('[data-testid="category-select"]');
    await page.click('[data-testid="category-option-repair"]');
    
    // 부모 액션 생성
    await page.click('[data-testid="create-action-btn"]');
    await page.fill('[data-testid="action-code"]', 'PARENT_ACTION');
    await page.fill('[data-testid="action-name"]', '부모 액션');
    await page.click('[data-testid="save-action-btn"]');
    
    // 자식 액션 생성
    await page.click('[data-testid="create-action-btn"]');
    await page.fill('[data-testid="action-code"]', 'CHILD_ACTION');
    await page.fill('[data-testid="action-name"]', '자식 액션');
    await page.click('[data-testid="parent-action-select"]');
    await page.click('[data-testid="parent-option-parent-action"]');
    await page.click('[data-testid="save-action-btn"]');
    
    // 계층 구조 확인
    await expect(page.locator('[data-testid="action-tree"]')).toContainText('부모 액션');
    await expect(page.locator('[data-testid="action-tree"]')).toContainText('자식 액션');
  });

  test('템플릿 조건부 생성 규칙', async ({ page }) => {
    // 카테고리와 액션 선택
    await page.click('[data-testid="category-select"]');
    await page.click('[data-testid="category-option-repair"]');
    await page.click('[data-testid="action-select"]');
    await page.click('[data-testid="action-option-symptom"]');
    
    // 템플릿 생성
    await page.click('[data-testid="create-template-btn"]');
    await page.fill('[data-testid="template-code"]', 'CONDITIONAL_TEMPLATE');
    await page.fill('[data-testid="template-name"]', '조건부 템플릿 - {{symptom_type}}');
    
    // 조건부 규칙 추가
    await page.click('[data-testid="add-condition-btn"]');
    await page.fill('[data-testid="condition-key"]', 'symptom_type');
    await page.select('[data-testid="condition-type"]', 'required');
    await page.click('[data-testid="save-template-btn"]');
    
    // 템플릿 미리보기 확인
    await page.click('[data-testid="preview-template-btn"]');
    await expect(page.locator('[data-testid="template-preview"]')).toContainText('조건부 템플릿 - {{symptom_type}}');
  });

  test('생성된 상태 모니터링', async ({ page }) => {
    // 생성된 상태 페이지로 이동
    await page.click('[data-testid="generated-statuses-tab"]');
    
    // 필터 적용
    await page.click('[data-testid="category-filter"]');
    await page.click('[data-testid="filter-option-repair"]');
    
    // 검색 기능
    await page.fill('[data-testid="search-input"]', '수리');
    await page.press('[data-testid="search-input"]', 'Enter');
    
    // 결과 확인
    await expect(page.locator('[data-testid="status-list"]')).toContainText('수리');
    
    // 통계 확인
    await expect(page.locator('[data-testid="stats-card"]')).toBeVisible();
  });
});
```

### 5. 시각적 회귀 테스트
```javascript
// tests/e2e/visual-regression.spec.js
import { test, expect } from '@playwright/test';

test.describe('시각적 회귀 테스트', () => {
  test('카테고리 관리 페이지 스크린샷', async ({ page }) => {
    await page.goto('/wms/settings/dynamic-work-status/categories');
    await expect(page).toHaveScreenshot('category-management.png');
  });

  test('액션 관리 페이지 스크린샷', async ({ page }) => {
    await page.goto('/wms/settings/dynamic-work-status/actions');
    await expect(page).toHaveScreenshot('action-management.png');
  });

  test('템플릿 관리 페이지 스크린샷', async ({ page }) => {
    await page.goto('/wms/settings/dynamic-work-status/templates');
    await expect(page).toHaveScreenshot('template-management.png');
  });

  test('반응형 디자인 테스트', async ({ page }) => {
    // 모바일 뷰포트
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/wms/settings/dynamic-work-status');
    await expect(page).toHaveScreenshot('mobile-view.png');
    
    // 태블릿 뷰포트
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page).toHaveScreenshot('tablet-view.png');
    
    // 데스크톱 뷰포트
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page).toHaveScreenshot('desktop-view.png');
  });
});
```

## 🚀 배포 및 최적화

### 1. 빌드 최적화
```javascript
// vite.config.js
import { defineConfig } from 'vite';
import { svelte } from '@sveltejs/vite-plugin-svelte';

export default defineConfig({
  plugins: [svelte()],
  build: {
    target: 'es2020',
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['svelte', 'svelte/store'],
          ui: ['@tailwindcss/forms', 'daisyui'],
        },
      },
    },
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
    },
  },
});
```

### 2. 성능 최적화
```javascript
// 지연 로딩 컴포넌트
const CategoryManagement = () => import('./components/CategoryManagement.svelte');
const ActionManagement = () => import('./components/ActionManagement.svelte');

// 이미지 최적화
<img 
  src={imageSrc} 
  alt={imageAlt}
  loading="lazy"
  class="w-full h-auto"
/>

// 가상 스크롤링
import { VirtualList } from '@tanstack/svelte-virtual';
```

## 📱 모바일 최적화

### 1. 터치 제스처 지원
```javascript
// 터치 제스처 핸들러
let touchStartX = 0;
let touchEndX = 0;

function handleTouchStart(e) {
  touchStartX = e.changedTouches[0].screenX;
}

function handleTouchEnd(e) {
  touchEndX = e.changedTouches[0].screenX;
  handleSwipe();
}

function handleSwipe() {
  if (touchEndX < touchStartX - 50) {
    // 왼쪽 스와이프
    nextPage();
  }
  if (touchEndX > touchStartX + 50) {
    // 오른쪽 스와이프
    prevPage();
  }
}
```

### 2. 모바일 네비게이션
```svelte
<!-- 모바일 네비게이션 -->
<div class="btm-nav md:hidden">
  <button class="active">
    <svg>...</svg>
    <span class="btm-nav-label">홈</span>
  </button>
  <button>
    <svg>...</svg>
    <span class="btm-nav-label">설정</span>
  </button>
  <button>
    <svg>...</svg>
    <span class="btm-nav-label">통계</span>
  </button>
</div>
```

## 🔧 개발 도구 설정

### 1. ESLint 설정
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:svelte/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint'],
  rules: {
    'no-unused-vars': 'warn',
    'no-console': 'warn',
    'svelte/no-unused-svelte-ignore': 'warn',
  },
};
```

### 2. Prettier 설정
```javascript
// .prettierrc
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "plugins": ["prettier-plugin-svelte"],
  "overrides": [
    {
      "files": "*.svelte",
      "options": {
        "parser": "svelte"
      }
    }
  ]
}
```

## 📚 참고 자료

### 1. 공식 문서
- [SvelteKit 공식 문서](https://kit.svelte.dev/)
- [Tailwind CSS 공식 문서](https://tailwindcss.com/)
- [DaisyUI 공식 문서](https://daisyui.com/)

### 2. 유용한 라이브러리
- [@tanstack/svelte-virtual](https://github.com/TanStack/virtual) - 가상 스크롤링
- [svelte-French-toast](https://github.com/kbrgl/svelte-french-toast) - 토스트 알림
- [svelte-loading-spinners](https://github.com/Schum123/svelte-loading-spinners) - 로딩 스피너
- [lucide-svelte](https://github.com/lucide-icons/lucide) - 아이콘 라이브러리
- [@testing-library/svelte](https://github.com/testing-library/svelte-testing-library) - Svelte 테스트 도구
- [@testing-library/jest-dom](https://github.com/testing-library/jest-dom) - DOM 매처
- [vitest](https://github.com/vitest-dev/vitest) - 빠른 테스트 런너
- [@playwright/test](https://github.com/microsoft/playwright) - E2E 테스트 프레임워크

### 3. 테스트 실행 스크립트
```json
{
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest run --coverage",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:e2e:debug": "playwright test --debug",
    "test:visual": "playwright test --project=chromium --grep='시각적 회귀 테스트'"
  },
  "devDependencies": {
    "@playwright/test": "^1.40.0",
    "@testing-library/jest-dom": "^6.1.4",
    "@testing-library/svelte": "^4.0.5",
    "@vitest/coverage-v8": "^0.34.6",
    "@vitest/ui": "^0.34.6",
    "jsdom": "^23.0.1",
    "vitest": "^0.34.6"
  }
}
```

### 4. 개발 가이드라인
- 모든 컴포넌트는 TypeScript로 작성
- props 타입 정의 필수
- 에러 핸들링 및 로딩 상태 관리 필수
- 접근성 고려 (ARIA 속성, 키보드 네비게이션)
- 반응형 디자인 적용
- 성능 최적화 (지연 로딩, 가상 스크롤링 등)

---

이 가이드를 참고하여 단계별로 구현해주세요. 각 단계에서 구현이 완료되면 다음 단계로 넘어가시면 됩니다. 