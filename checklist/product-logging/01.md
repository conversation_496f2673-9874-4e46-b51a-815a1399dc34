# 동적 WorkStatus 관리 시스템 및 제품 이력 추적 시스템 구현

## 🎯 우선순위 1: 동적 WorkStatus 관리 시스템 구현 (최우선)

### 🎯 목표
- 클라이언트(작업 관리자)가 직접 새로운 작업 유형을 등록할 수 있는 시스템
- 개발자는 코드 수정 없이 새로운 상태를 바로 사용할 수 있는 시스템
- 기존 하드코딩된 상수 시스템을 완전히 대체

### 데이터베이스 구조 설계
- [x] work_categories 테이블 생성
  ```sql
  -- 작업 카테고리 테이블
  CREATE TABLE work_categories (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '카테고리 코드 (예: REPAIR, PALLET, CARRYOUT)',
  name VARCHAR(100) NOT NULL COMMENT '카테고리 이름',
  description TEXT COMMENT '카테고리 설명',
  is_active BOOLEAN DEFAULT TRUE COMMENT '활성 상태',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  );
  ```
  - 작업 카테고리 코드, 이름, 설명, 활성 상태 필드
  - 기본 카테고리 데이터 시딩 (REPAIR, PALLET, CARRYOUT, AUTH 등)
- [x] work_actions 테이블 생성
  ```sql
  -- 작업 액션 테이블
  CREATE TABLE work_actions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  category_id BIGINT NOT NULL,
  code VARCHAR(50) NOT NULL COMMENT '액션 코드 (예: WAITING, COMPLETE, SYMPTOM)',
  name VARCHAR(100) NOT NULL COMMENT '액션 이름',
  description TEXT COMMENT '액션 설명',
  parent_id BIGINT NULL COMMENT '부모 액션 ID (계층 구조)',
  sort_order INT DEFAULT 0 COMMENT '정렬 순서',
  is_active BOOLEAN DEFAULT TRUE COMMENT '활성 상태',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES work_categories(id),
  FOREIGN KEY (parent_id) REFERENCES work_actions(id),
  UNIQUE KEY unique_category_action (category_id, code)
  );
  ```
  - 카테고리별 액션 관리 (WAITING, COMPLETE, SYMPTOM 등)
  - 계층 구조 지원 (parent_id)
  - 기본 액션 데이터 시딩 (각 카테고리별 기본 액션)
- [x] work_status_templates 테이블 생성
  ```sql
  -- 작업 상태 템플릿 테이블 (클라이언트가 등록하는 템플릿)
  CREATE TABLE work_status_templates (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  category_id BIGINT NOT NULL,
  action_id BIGINT NOT NULL,
  template_code VARCHAR(100) NOT NULL COMMENT '템플릿 코드',
  name VARCHAR(200) NOT NULL COMMENT '상태 이름',
  description TEXT COMMENT '상태 설명',
  conditions JSON COMMENT '조건부 생성 규칙',
  is_active BOOLEAN DEFAULT TRUE COMMENT '활성 상태',
  created_by BIGINT COMMENT '생성자',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES work_categories(id),
  FOREIGN KEY (action_id) REFERENCES work_actions(id),
  FOREIGN KEY (created_by) REFERENCES users(id),
  UNIQUE KEY unique_template (category_id, action_id, template_code)
  );
  ```
  - 클라이언트가 등록하는 상태 템플릿
  - 조건부 생성 규칙 지원
- [x] 기존 work_statuses 테이블 확장
  ```sql
  ALTER TABLE work_statuses 
  ADD COLUMN category_id BIGINT NULL AFTER id,
  ADD COLUMN action_id BIGINT NULL AFTER category_id,
  ADD COLUMN template_id BIGINT NULL AFTER action_id,
  ADD COLUMN auto_generated BOOLEAN DEFAULT FALSE COMMENT '자동 생성 여부',
  ADD COLUMN generation_context JSON COMMENT '생성 컨텍스트',
  ADD FOREIGN KEY (category_id) REFERENCES work_categories(id),
  ADD FOREIGN KEY (action_id) REFERENCES work_actions(id),
  ADD FOREIGN KEY (template_id) REFERENCES work_status_templates(id);
  ```
  - 기존 테이블에 category_id, action_id, template_id 추가
  - 자동 생성 여부 및 컨텍스트 정보 추가(auto_generated, generation_context 필드 추가)

### 백엔드 구현 (Service/Repository 패턴)
- [x] WorkCategory 모델 생성
  - 카테고리와 액션 관계 정의
  - 유효성 검증 로직
- [x] WorkAction 모델 생성
  - 카테고리별 액션 관리
  - 계층 구조 지원 메서드
- [x] WorkStatusTemplate 모델 생성
  - 템플릿 기반 상태 생성 로직
  - 조건부 생성 규칙 처리
- [x] DynamicWorkStatusService 구현
  - 컨텍스트 기반 상태 자동 생성 및 조회
  - 기존 하드코딩 상수 대체(템플릿 매칭 로직)
  - 캐싱 전략 구현
  - Repository 패턴 적용 완료
- [x] WorkStatusManagementController 구현
  - 클라이언트용 관리 UI API
  - 카테고리/액션/템플릿 CRUD 기능 제공
  - Service 패턴 적용 완료
- [x] FormRequest Validator 생성
  - WorkCategoryRequest, WorkActionRequest, WorkStatusTemplateRequest 생성
  - 모델의 validation 메서드 제거 및 FormRequest로 대체
  - 컨트롤러에서 FormRequest 사용하도록 수정
  - Service에서 모델 validation 제거
- [x] Repository 인터페이스 및 구현체 생성
  - WorkCategoryRepository, WorkActionRepository, WorkStatusTemplateRepository, WorkStatusRepository
  - 모든 Repository에 Service/Repository 패턴 적용 완료
- [x] Service 클래스 생성
  - WorkCategoryService, WorkActionService, WorkStatusTemplateService, WorkStatusService
  - 비즈니스 로직 캡슐화 및 Repository 패턴 적용 완료
- [x] ServiceProvider 등록
  - Repository 인터페이스와 구현체 바인딩 완료
  - 의존성 주입 설정 완료
- [x] 기존 서비스 리팩토링
  - RepairService 수정 완료 (DynamicWorkStatus Facade 적용)
  - WorkStatusManagementController 수정 완료 (DynamicWorkStatus Facade 적용)
  - DynamicWorkStatusService Facade 생성 및 연결 완료
  - 하드코딩된 상수 제거(주석 처리. 나중에 수동으로 삭제)
  - 동적 상태 생성 로직 적용

### 라우터 생성
- [x] 동적 WorkStatus 관리 시스템 라우터 구현
  - 카테고리 관리 라우터 (CRUD)
  - 액션 관리 라우터 (CRUD)
  - 템플릿 관리 라우터 (CRUD)
  - 생성된 상태 관리 라우터 (조회, 통계)
  - 시스템 관리 라우터 (레거시 변환, 캐시 초기화)
- [x] 라우터 네임스페이스 및 미들웨어 설정
  - `wms.setting.dynamic.*` 네임스페이스 적용
  - `auth:sanctum` 미들웨어 적용
  - 정규표현식 패턴 검증 적용
- [x] 컨트롤러 임포트 및 바인딩
  - WorkStatusManagementController 임포트 완료
  - 라우터와 컨트롤러 메서드 바인딩 완료

### 마이그레이션 및 호환성
- [ ] 기존 하드코딩 상수 매핑
  - 기존 상수들을 새로운 시스템으로 마이그레이션
- [ ] 점진적 전환 전략
  - 기존 코드 호환성 유지
  - 단계별 서비스 전환
- [ ] 데이터 무결성 보장
  - 기존 product_logs 데이터 보존
  - 새로운 시스템과의 연결

