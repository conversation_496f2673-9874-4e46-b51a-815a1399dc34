### 프론트엔드 구현
- [ ] **카테고리 관리 페이지** (`/wms/settings/dynamic-work-status/categories`)
  - 카테고리 목록 조회 (`GET /categories`)
  - 카테고리 생성 (`POST /categories`)
  - 카테고리 수정 (`PUT /categories/{id}`)
  - 카테고리 삭제 (`DELETE /categories/{id}`)
  - 카테고리별 통계 조회 (`GET /categories/{code}/stats`)

- [ ] **액션 관리 페이지** (`/wms/settings/dynamic-work-status/actions`)
  - 카테고리별 액션 목록 조회 (`GET /actions/{categoryId}`)
  - 액션 생성 (`POST /actions`)
  - 액션 수정 (`PUT /actions/{id}`)
  - 액션 삭제 (`DELETE /actions/{id}`)
  - 계층 구조 관리 (parent_id 설정)

- [ ] **템플릿 관리 페이지** (`/wms/settings/dynamic-work-status/templates`)
  - 카테고리/액션별 템플릿 목록 조회 (`GET /templates/{categoryId}/{actionId}`)
  - 템플릿 생성 (`POST /templates`)
  - 템플릿 수정 (`PUT /templates/{id}`)
  - 템플릿 삭제 (`DELETE /templates/{id}`)
  - 조건부 생성 규칙 설정 (conditions JSON 필드)
  - 사용되지 않는 템플릿 관리 (`GET /templates/unused`)

- [ ] **생성된 상태 모니터링 페이지** (`/wms/settings/dynamic-work-status/generated-statuses`)
  - 자동 생성된 상태 조회 (`GET /generated-statuses`)
  - 상태 검색 및 필터링 (category_id, action_id, template_id)
  - 사용 통계 및 분석 (`GET /generated-statuses/stats`)
  - 최근 생성된 상태 (`GET /generated-statuses/recent`)
  - 자주 사용되는 상태 (`GET /generated-statuses/frequent`)

- [ ] **시스템 관리 페이지** (`/wms/settings/dynamic-work-status/system`)
  - 레거시 상수 변환 (`POST /system/convert-legacy`)
  - 캐시 초기화 (`POST /system/clear-cache`)
  - 시스템 상태 모니터링

- [ ] **기존 화면 업데이트**
  - 동적 상태 선택 드롭다운 컴포넌트
  - 상태 변경 시 자동 갱신
  - 실시간 상태 생성 모니터링

- 참고 소스 3: 개선된 서비스 사용법
```php
<?php

namespace App\Services;

use App\Services\DynamicWorkStatusService;

class RepairService
{
    private DynamicWorkStatusService $dynamicWorkStatusService;
    
    public function __construct(DynamicWorkStatusService $dynamicWorkStatusService)
    {
        $this->dynamicWorkStatusService = $dynamicWorkStatusService;
    }
    
    /**
     * 간단하고 직관적인 상태 ID 조회
     */
    private function getStatusIds(array $data): array
    {
        $statusIds = [];
        
        // 기본 수리 상태들
        $statusIds['WAITING'] = $this->dynamicWorkStatusService
            ->getOrCreateStatus('REPAIR', 'WAITING')->id;
            
        $statusIds['COMPLETE'] = $this->dynamicWorkStatusService
            ->getOrCreateStatus('REPAIR', 'COMPLETE')->id;
        
        // 증상별 상태
        $statusIds['SYMPTOM'] = $this->dynamicWorkStatusService
            ->getOrCreateStatus('REPAIR', 'SYMPTOM', [
                'symptom_code' => $data['symptom_code'],
                'grade_code' => $data['grade_code']
            ])->id;
        
        // 처리별 상태
        $statusIds['PROCESS'] = $this->dynamicWorkStatusService
            ->getOrCreateStatus('REPAIR', 'PROCESS', [
                'process_code' => $data['process_code']
            ])->id;
        
        // 조건부 상태들
        if ($data['os_reinstall']) {
            $statusIds['OS_REINSTALL'] = $this->dynamicWorkStatusService
                ->getOrCreateStatus('REPAIR', 'OS_REINSTALL')->id;
        }
        
        if ($data['add_fee']) {
            $statusIds['ADD_FEE'] = $this->dynamicWorkStatusService
                ->getOrCreateStatus('REPAIR', 'ADD_FEE')->id;
        }
        
        if ($data['add_parts']) {
            $statusIds['ADD_PARTS'] = $this->dynamicWorkStatusService
                ->getOrCreateStatus('REPAIR', 'ADD_PARTS')->id;
        }
        
        return $statusIds;
    }
    
    /**
     * 사용 예시 - 기존 코드와 거의 동일
     */
    public function waiting(array $data, Product $product): void
    {
        $statusIds = $this->getStatusIds($data);
        
        // 기존과 동일한 방식으로 사용
        $this->logger->addLog(
            $product, 
            'App\Models\Product', 
            $product->id, 
            $statusIds['WAITING'], 
            auth()->id(), 
            "수리/점검 대기"
        );
        
        // 증상 로그
        $this->logger->addLog(
            $product, 
            'App\Models\Product', 
            $product->id, 
            $statusIds['SYMPTOM'], 
            auth()->id(), 
            "증상: {$data['symptom_code']}"
        );
        
        // 조건부 로그들
        if (isset($statusIds['OS_REINSTALL'])) {
            $this->logger->addLog(
                $product, 
                'App\Models\Product', 
                $product->id, 
                $statusIds['OS_REINSTALL'], 
                auth()->id(), 
                "OS 재설치 처리"
            );
        }
    }
}
```
- 참고 소스 4: 클라이언트 UI 사용 시나리오 (실제 라우터 사용)
```javascript
// 프론트엔드에서 새로운 작업 유형 등록
const createNewWorkType = async () => {
    // 1. 카테고리 생성 (필요시)
    const categoryResponse = await api.post('/wms/settings/dynamic-work-status/categories', {
        code: 'QUALITY_CHECK',
        name: '품질 검사',
        description: '제품 품질 검사 관련 작업'
    });
    
    const categoryId = categoryResponse.data.category.id;
    
    // 2. 액션 생성
    const actionResponse = await api.post('/wms/settings/dynamic-work-status/actions', {
        category_id: categoryId,
        code: 'DEFECT_FOUND',
        name: '결함 발견',
        description: '제품에서 결함이 발견된 상태'
    });
    
    const actionId = actionResponse.data.action.id;
    
    // 3. 상태 템플릿 생성
    await api.post('/wms/settings/dynamic-work-status/templates', {
        category_id: categoryId,
        action_id: actionId,
        template_code: 'DEFECT_TEMPLATE',
        name: '결함 발견 - {{defect_type}}',
        description: '{{defect_type}} 유형의 결함이 발견됨',
        conditions: {
            defect_type: 'required'
        }
    });
};

// 관리자 페이지에서 데이터 조회
const loadAdminData = async () => {
    // 카테고리 목록 조회
    const categories = await api.get('/wms/settings/dynamic-work-status/categories');
    
    // 특정 카테고리의 액션 조회
    const actions = await api.get(`/wms/settings/dynamic-work-status/actions/${categoryId}`);
    
    // 특정 카테고리와 액션의 템플릿 조회
    const templates = await api.get(`/wms/settings/dynamic-work-status/templates/${categoryId}/${actionId}`);
    
    // 생성된 상태 조회
    const generatedStatuses = await api.get('/wms/settings/dynamic-work-status/generated-statuses');
    
    // 통계 정보 조회
    const stats = await api.get('/wms/settings/dynamic-work-status/generated-statuses/stats');
    
    // 최근 생성된 상태 조회
    const recentStatuses = await api.get('/wms/settings/dynamic-work-status/generated-statuses/recent?limit=10');
    
    // 자주 사용되는 상태 조회
    const frequentStatuses = await api.get('/wms/settings/dynamic-work-status/generated-statuses/frequent?limit=10');
};

// 시스템 관리 기능
const systemManagement = {
    // 레거시 상수 변환
    convertLegacy: async () => {
        await api.post('/wms/settings/dynamic-work-status/system/convert-legacy');
    },
    
    // 캐시 초기화
    clearCache: async () => {
        await api.post('/wms/settings/dynamic-work-status/system/clear-cache');
    },
    
    // 사용되지 않는 템플릿 조회
    getUnusedTemplates: async () => {
        return await api.get('/wms/settings/dynamic-work-status/templates/unused');
    }
};
```
- 참고 소스 5: API에서 즉시 사용
```php
// 새로 등록된 작업 유형을 바로 사용
class QualityCheckService
{
    public function recordDefect(Product $product, string $defectType): void
    {
        // 클라이언트가 등록한 템플릿을 자동으로 사용
        $statusId = $this->dynamicWorkStatusService
            ->getOrCreateStatus('QUALITY_CHECK', 'DEFECT_FOUND', [
                'defect_type' => $defectType
            ])->id;
        
        $this->logger->addLog(
            $product, 
            'App\Models\Product', 
            $product->id, 
            $statusId, 
            auth()->id(), 
            "결함 발견: {$defectType}"
        );
    }
}
```
- 참고 소스 6: 마이그레이션 및 호환성
```php
// 기존 하드코딩된 상수를 새로운 시스템으로 마이그레이션
class WorkStatusMigration
{
    public function run(): void
    {
        $oldStatuses = [
            'REPAIR_WAITING' => 'REPAIR_WAITING',
            'REPAIR_COMPLETE' => 'REPAIR_COMPLETE',
            // ...
        ];
        
        foreach ($oldStatuses as $oldCode => $newCode) {
            $newStatus = WorkStatus::where('code', $newCode)->first();
            
            if (!$newStatus) {
                // 새로운 시스템에 상태가 없는 경우 생성
                $newStatus = WorkStatus::create([
                    'code' => $newCode,
                    'name' => $newCode,
                    'description' => "마이그레이션된 상태: {$newCode}",
                    'link_code' => $newCode,
                ]);
            }
            
            // product_logs 업데이트
            DB::table('product_logs')
                ->where('work_status_id', $oldCode)
                ->update(['work_status_id' => $newStatus->id]);
        }
    }
}
```
- 참고 소스 7: 점진적 전환 전략
```php
// 점진적 전환을 위한 설정 플래그
class Config
{
    public static function useDynamicWorkStatus(): bool
    {
        return env('USE_DYNAMIC_WORK_STATUS', false);
    }
}

// 서비스에서 전환 전략 적용
class RepairService
{
    public function waiting(array $data, Product $product): void
    {
        if (Config::useDynamicWorkStatus()) {
            // 새로운 방식 사용
        } else {
            // 기존 방식 사용
        }
    }
}
```
- 참고 소스 8: 데이터 무결성 보장
```php
// product_logs 테이블에 새로운 컬럼 추가
Schema::table('product_logs', function (Blueprint $table) {
    $table->boolean('is_migrated')->default(false);
});

// 마이그레이션된 데이터 표시
class ProductLog
{
    public function scopeMigrated($query)
    {
        return $query->where('is_migrated', true);
    }
    
    public function scopeNotMigrated($query)
    {
        return $query->where('is_migrated', false);
    }
}
```
- 참고 소스 9: 데이터 무결성 검증
```php
// 데이터 무결성 검증 테스트
class DataIntegrityTest extends TestCase
{
    public function testProductLogsMigration(): void
    {
        $migratedCount = ProductLog::migrated()->count();
        $notMigratedCount = ProductLog::notMigrated()->count();
        
        $this->assertEquals(0, $notMigratedCount, "모든 로그가 마이그레이션되지 않았습니다.");
    }
}
```
- 참고 소스 10: 마이그레이션 가이드
```bash
php artisan migrate
php artisan db:seed --class=WorkStatusSeeder
php artisan migrate:refresh --seed
```