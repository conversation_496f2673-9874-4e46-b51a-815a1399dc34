# 프로젝트 초기 설정 - 이슈 트래킹

### 이슈 #001: composer 미설치로 로컬 설치 불가
- **발견일**: 2025-07-07
- **상태**: 해결
- **담당자**: 
- **설명**: 로컬 환경에 composer가 없어 라라벨 설치 불가
- **해결방안**: Docker 기반 개발환경으로 전환, 컨테이너 내부에서 composer 사용
- **검증**: 컨테이너에서 composer 정상 동작 확인

### 이슈 #002: MariaDB 컨테이너 미사용, 외부 인스턴스 연결
- **발견일**: 2025-07-07
- **상태**: 해결
- **담당자**: 
- **설명**: docker-compose에서 DB를 관리하지 않고, WSL 외부 인스턴스 사용
- **해결방안**: DB_HOST=host.docker.internal로 연결, .env 및 compose 환경변수 반영
- **검증**: 컨테이너에서 DB 연결 정상 확인

### 이슈 #003: supervisor 설정 경로 및 artisan schedule:work 관리
- **발견일**: 2025-07-07
- **상태**: 참고
- **담당자**: 
- **설명**: supervisor 설정 경로, artisan 스케줄러 관리 주의 필요
- **해결방안**: supervisord.conf를 backend 디렉토리에 두고 Dockerfile에서 복사, schedule:work supervisor로 관리
- **검증**: 컨테이너에서 php-fpm, schedule:work 모두 정상 동작

## ⚠️ Medium Priority
### 이슈 #003: README 미작성
- **발견일**: 
- **상태**: 대기중
- **담당자**: 
- **설명**: 개발환경/실행법/기여 가이드 등 미작성
- **해결방안**: README 작성 및 최신화

## ✅ 해결된 이슈
### 이슈 #000: 초기 DB 마이그레이션 오류
- **해결일**: 
- **해결방안**: 마이그레이션 파일 수정 및 재실행
- **검증**: 정상 동작 확인 