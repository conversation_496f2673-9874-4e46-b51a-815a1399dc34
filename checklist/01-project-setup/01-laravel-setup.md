# 라라벨 프로젝트 생성 및 환경설정

## 1. 라라벨 프로젝트 설치
- [x] 라라벨 프로젝트 설치 (Docker 컨테이너 내부에서 composer로 설치)
- [x] 기본 폴더 구조 정리 (app, config, routes 등)
- [x] .env 파일에서 DB 연결 정보 설정 (MariaDB는 WSL 외부 인스턴스 사용)
- [x] 의존성 패키지 설치 (composer)
    - 필수 패키지
        - [ ] laravel/sanctum
        - [ ] maatwebsite/excel
        - [ ] laravel/scout
        - [ ] irazasyed/telegram-bot-sdk
        - [ ] laravel/slack-notification-channel
        - [ ] meilisearch/meilisearch-php
        - [ ] predis/predis
        - [ ] pusher/pusher-php-server
        - [ ] spatie/laravel-activitylog
    - [ ] devDependencies
        - [ ] Pest
        - [ ] PHPStan
- [x] APP_KEY 생성 및 기타 환경설정

### 진행 요약 및 참고
- PHP 8.4, 멀티스테이지 Docker<PERSON>le, supervisor 기반 개발환경 구축
- MariaDB는 docker-compose가 아닌 WSL 외부 인스턴스 사용
- docker-compose up --build -d로 컨테이너 실행
- 컨테이너 내부에서 composer create-project laravel/laravel . 명령어로 설치
- supervisor로 php-fpm, artisan schedule:work 동시 관리
- docker-compose.yml, .dockerignore, README.md 등 문서화

### 주요 명령어
```bash
docker-compose up --build -d
docker-compose exec backend bash
composer create-project laravel/laravel .
```

### 트러블슈팅/참고
- composer 미설치로 로컬 설치 불가 → Docker 환경으로 전환
- MariaDB 컨테이너 미사용, 외부 인스턴스 연결(DB_HOST=host.docker.internal)
- supervisor 설정 경로 및 artisan schedule:work 관리 주의
- 기존 Dockerfile 참고, 멀티스테이지 빌드로 최적화

---

## 1. .env.example 관리
- [ ] .env.example 파일 작성 (백엔드)
- [ ] .env.example 파일 작성 (프론트엔드)
- [ ] 주요 환경 변수 주석 및 설명 추가

## 2. 환경별 분리
- [ ] 개발/테스트/운영 환경 변수 분리
- [ ] 환경별 .env 파일 자동 생성 스크립트 작성

## 3. 보안
- [ ] 민감 정보(비밀번호, API 키 등) 노출 방지
- [ ] .env 파일 gitignore 적용

## 4. 자동화
- [ ] 환경 변수 로딩 자동화 (Docker, CI 등)
- [ ] 환경 변수 변경 시 문서화 자동화 

## 5. Lint/Format
- [ ] PHP CS Fixer, Pint 등 린터/포매터 설정
- [ ] pre-commit hook 설정 (husky 등)

## 6. 기본 설정
- [ ] config 파일 점검 및 기본값 설정
- [ ] .gitignore 최신화
- [ ] README에 백엔드 개발 환경 문서화 

---

### [참고] 배포/운영용 Dockerfile 및 Compose 설정은 13-deployment/01-docker.md 참고
- 개발환경 구축과 배포/운영 환경은 목적과 최적화 포인트가 다르므로, 공통 내용은 별도 문서로 분리 권장 