# 프로젝트 초기 설정 - 프론트엔드 세팅

## 1. 패키지 매니저
- [ ] .nvmrc 파일 작성 및 Node.js 버전 고정
- [ ] pnpm 설치 및 lockfile 고정

## 2. 프로젝트 생성
- [ ] <PERSON><PERSON> + SvelteKit 프로젝트 생성
- [ ] 기본 폴더 구조 정리 (src, routes, lib 등)

## 3. 패키지 설치
- [ ] 필수 패키지 설치 (Tailwind, daisyUI 등)
- [ ] devDependencies 설치 (Vitest, ESLint 등)

## 4. Lint/Format
- [ ] E<PERSON><PERSON>, Prettier 등 린터/포매터 설정
- [ ] pre-commit hook 설정 (husky 등)

## 5. 기본 설정
- [ ] 환경 변수 연동
- [ ] .gitignore 최신화
- [ ] README에 프론트엔드 개발 환경 문서화 