# 테스트 - 백엔드 단위 테스트 (Pest/Laravel)

## 1. 모델 단위 테스트
- [ ] User 모델 단위 테스트
- [ ] Product 모델 단위 테스트
- [ ] Pallet 모델 단위 테스트
- [ ] Repair 모델 단위 테스트
- [ ] Carryout 모델 단위 테스트
- [ ] Board 모델 단위 테스트
- [ ] 기타 주요 모델 단위 테스트

## 2. 서비스/리포지토리 단위 테스트
- [ ] AuthService 단위 테스트
- [ ] ProductService 단위 테스트
- [ ] PalletService 단위 테스트
- [ ] RepairService 단위 테스트
- [ ] CarryoutService 단위 테스트
- [ ] BoardService 단위 테스트
- [ ] 기타 서비스/리포지토리 단위 테스트

## 3. 미들웨어/권한/에러 단위 테스트
- [ ] 인증 미들웨어 단위 테스트
- [ ] 권한 미들웨어 단위 테스트
- [ ] 에러/예외 처리 단위 테스트

## 4. 성능/보안 단위 테스트
- [ ] 대용량 데이터 처리 성능 테스트
- [ ] 입력값 검증/보안 취약점 단위 테스트 