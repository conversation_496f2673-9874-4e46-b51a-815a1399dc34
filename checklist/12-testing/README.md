# 테스트 관리(12-testing)

## 📊 전체 진행 상황
- **백엔드 단위 테스트**: 0% 완료
- **백엔드 Feature/API 테스트**: 0% 완료
- **프론트엔드 단위 테스트**: 0% 완료
- **프론트엔드 통합(API 연동) 테스트**: 0% 완료
- **E2E/시나리오/성능/환경 테스트**: 0% 완료

## 📁 세부 체크리스트
- [01-backend-unit.md](./01-backend-unit.md) - 백엔드 단위 테스트(모델/서비스/미들웨어/권한/에러/성능/보안)
- [02-backend-feature.md](./02-backend-feature.md) - 백엔드 Feature/API 테스트(정상/실패/권한/에러/롤백/성능/보안)
- [03-frontend-unit.md](./03-frontend-unit.md) - 프론트엔드 단위 테스트(Svelte/컴포넌트/스토어/상태/유효성/에러)
- [04-frontend-integration.md](./04-frontend-integration.md) - 프론트엔드 통합(API 연동) 테스트(API 연동/상태/에러/권한/UX)
- [05-e2e.md](./05-e2e.md) - E2E/시나리오/성능/환경 테스트(전체 플로우/권한/환경/성능/부하/크로스브라우저)

## 🎯 테스트 목표
- 모든 핵심 기능에 대해 TDD 기반의 단위/통합/E2E 테스트 작성
- 권한/보안/성능/예외/에러/UX 등 실무 시나리오 기반 테스트 강화
- 테스트 커버리지 80% 이상 유지
- CI/CD 파이프라인에서 자동 테스트 통과 필수

## 📝 작성 가이드
- 각 파일별로 실제 업무/개발/테스트 흐름에 맞는 구체적이고 실행 가능한 체크리스트만 작성
- 체크리스트는 최대한 세분화(단위/Feature/API/통합/E2E/성능/보안/UX/예외 등)
- 테스트 실패/이슈 발생 시 [이슈 트래킹](../09-issue.md)에도 기록

## 💡 참고
- 각 도메인별(입고, 검수, 수리 등) 테스트 분할이 필요하면 동일한 구조로 디렉토리/파일 생성
- 테스트 자동화/커버리지/성능/보안 등 비기능 요구도 반드시 포함 