# QAID 관리 - 백엔드 API 개발

## 1. 이력/통계/검색 API
- [ ] QAID별 이력 조회 API(Route/Controller/Service/Repository)
- [ ] QAID별 타임라인/상세 이력 API
- [ ] 증상/처리내용/등급/부품/담당자 등 코드 기반 검색/통계 API
- [ ] 코드 기반 LIKE 금지, 정확 일치/IN 검색만 허용
- [ ] 대용량 환경 성능 최적화(Eager Loading, 인덱스 등)

## 2. 코드 관리 API
- [ ] 증상/처리내용/등급/부품 등 코드 테이블 CRUD API
- [ ] 코드별 이력/통계/검색 API

## 3. 예외/권한/보안
- [ ] 권한별 접근 제어(관리자/담당자 등)
- [ ] 에러/예외 응답 및 상세 로깅
- [ ] 개인정보/이력 데이터 보호(암호화 등)

## 4. 테스트/문서화
- [ ] API 단위/통합 테스트
- [ ] API 명세/문서화 