# QAID 관리 기능

## 📊 전체 진행 상황
- **DB/모델**: 0% 완료
- **API**: 0% 완료
- **백엔드 테스트**: 0% 완료
- **프론트엔드**: 0% 완료
- **테스트**: 0% 완료
- **전체**: 0% 완료

## 📁 세부 체크리스트
- [01-database.md](./01-database.md) - DB 설계 및 모델 구현
- [02-api.md](./02-api.md) - API 개발(Service, Repository 패턴 등)
- [03-backend-testing.md](./03-backend-testing.md) - 백엔드 테스트
- [04-frontend-ui.md](./04-frontend-ui.md) - UI 컴포넌트
- [05-frontend-pages.md](./05-frontend-pages.md) - 페이지(컴포넌트) 개발
- [06-frontend-integration.md](./06-frontend-integration.md) - API 연동
- [07-frontend-testing.md](./07-frontend-testing.md) - 프론트엔드 테스트
- [08-e2e-testing.md](./08-e2e-testing.md) - 통합 테스트
- [09-issue.md](./09-issue.md) - 이슈 트래킹

## 🎯 이번 주 목표
- QAID 이력 테이블/코드 테이블 설계 및 마이그레이션
- QAID별 이력/통계/검색 API 1차 구현

## 💥 주요 이슈
- 대용량 이력 성능 최적화 방안 검토
- LIKE 검색 금지, 코드 기반 정규화 설계 