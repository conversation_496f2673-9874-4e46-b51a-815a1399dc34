# QAID 관리 - 데이터베이스 설계

## 1. 테이블/마이그레이션 설계
- [ ] product_histories 테이블 설계 및 마이그레이션 생성
- [ ] product_history_parts(N:M) 테이블 설계 및 마이그레이션 생성
- [ ] symptoms, processes, grades, parts 등 코드 테이블 설계 및 마이그레이션 생성
- [ ] 각 테이블 인덱스/외래키/정합성 제약조건 설계
- [ ] 파티셔닝/아카이빙 정책 설계 및 적용

## 2. Eloquent 모델/관계
- [ ] ProductHistory, ProductHistoryPart 등 모델 생성
- [ ] 코드 테이블 모델 및 관계 설정
- [ ] N:M 관계(부품 이력) 모델 관계 설정

## 3. 성능/보안/정합성
- [ ] 대용량 데이터 인덱스/쿼리 성능 검증
- [ ] 데이터 정합성/무결성 테스트
- [ ] 개인정보/이력 데이터 암호화 적용(필요시) 