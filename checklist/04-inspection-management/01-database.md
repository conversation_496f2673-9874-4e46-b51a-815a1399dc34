# 입고 검수 관리 - 데이터베이스 설계 예제

## 1. inspections 테이블 마이그레이션 (<PERSON><PERSON>)
```php
// database/migrations/2024_xx_xx_create_inspections_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('inspections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->cascadeOnUpdate();
            $table->unsignedTinyInteger('status')->default(10); // 10: 대기, 20: 완료, 30: 미입고, 40: 중복
            $table->foreignId('user_id')->constrained('users')->cascadeOnUpdate();
            $table->dateTime('inspected_at')->nullable();
            $table->string('memo')->nullable();
            $table->timestamps();
            $table->index(['product_id', 'status', 'user_id', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('inspections');
    }
};
```

## 2. 실제 작업 항목 (작성/수정)
- [ ] inspections 테이블 구조 설계 및 마이그레이션 작성
- [ ] Eloquent 모델(Inspection) 생성 및 관계 설정
- [ ] 모델 단위 테스트 작성 (Pest)
- [ ] 상태별 인덱스/쿼리 최적화 