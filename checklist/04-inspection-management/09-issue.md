# 입고 검수 관리 - 이슈 트래킹

## 🚨 High Priority
### 이슈 #001: 검수 상태 처리 로직 불안정
- **발견일**: 2024-xx-xx
- **상태**: 진행중
- **담당자**: 김개발
- **설명**: 검수 상태(입고/미입고/중복) 전환 시 예외 발생
- **해결방안**: 상태 전환 로직 리팩토링 및 테스트 강화
- **예상 완료일**: 2024-xx-xx

### 이슈 #002: 바코드 스캔 예외 처리 미흡
- **발견일**: 2024-xx-xx
- **상태**: 대기중
- **담당자**: 이개발
- **설명**: 바코드 스캔 실패 시 UX 미흡
- **해결방안**: 수동 입력 UX 개선 및 에러 메시지 추가
- **예상 완료일**: 2024-xx-xx

## ⚠️ Medium Priority
### 이슈 #003: 대량 데이터 성능 저하
- **발견일**: 2024-xx-xx
- **상태**: 조사중
- **담당자**: 박개발
- **설명**: 검수 목록 대량 조회 시 응답 지연
- **해결방안**: 인덱스 최적화 및 페이징 개선

## ✅ 해결된 이슈
### 이슈 #000: 초기 API 응답 포맷 오류
- **해결일**: 2024-xx-xx
- **해결방안**: Resource 포맷 일관성 적용
- **검증**: Postman/프론트엔드 정상 동작 확인 