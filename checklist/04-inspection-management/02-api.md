# 입고 검수 관리 - 백엔드 API 개발 (Service/Repository 패턴)

## 🚩 진행 상황
- **완료**: 0/20 항목 (0%)
- **예상 완료일**: 미정

## 🚨 즉시 수정 필요
- [ ] 검수 상태별(입고/미입고/중복) 처리 로직 설계
- [ ] Request Validation 클래스 생성

## 📚 상세 작업 항목

## 1. 검수 상품 목록 조회 API
- [ ] Route 설정: `GET /wms/inspections`
- [ ] Request Validation 클래스 생성 (ListInspectionRequest)
- [ ] Service: InspectionService::getList() 구현
- [ ] Repository: InspectionRepository::fetchList() 구현
- [ ] 필터/페이징/정렬 기능 구현 (Repository)
- [ ] Eager Loading, 쿼리 최적화
- [ ] 응답 데이터 포맷 정의 (Resource 활용)
- [ ] Feature 테스트 작성 (Pest)

## 2. 개별/일괄 검수 완료 API
- [ ] Route 설정: `PATCH /wms/inspections/pass`
- [ ] Request Validation 클래스 생성 (PassInspectionRequest)
- [ ] Service: InspectionService::pass() 구현
- [ ] Repository: InspectionRepository::pass() 구현
- [ ] 상태 변경/로그 기록/알림 처리
- [ ] 예외 및 에러 처리 (Custom Exception 활용)
- [ ] Feature 테스트 작성

## 3. 검수 상태 변경/미입고/중복 처리 API
- [ ] Route 설정: `PATCH /wms/inspections/status`
- [ ] Request Validation 클래스 생성 (UpdateInspectionStatusRequest)
- [ ] Service/Repository 구현
- [ ] 관리자 알림/로그 기록
- [ ] Feature 테스트 작성

## 4. 기타
- [ ] 바코드 스캔/수동 입력 예외 처리
- [ ] 검수 결과 엑셀 다운로드 API
- [ ] 작업 로그/이력 기록 API

## 🔗 의존성
- **사용자 관리**: 작업자 정보 필요
- **제품 관리**: product_id 연동 필요

## 📈 성능/보안 지표
- API 응답시간: 평균 200ms (목표: 500ms 이하)
- 동시 처리량: 50 TPS (목표: 100 TPS) 