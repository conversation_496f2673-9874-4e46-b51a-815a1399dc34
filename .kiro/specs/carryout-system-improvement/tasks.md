# 구현 계획

- [x] 1. 범용 예외 처리 시스템 구축(한글로 답변)
  - WMS 전체에서 사용할 수 있는 예외 클래스 계층 구조 생성
  - 예외 헬퍼 클래스와 메시지 헬퍼 클래스 구현
  - _요구사항: 1.4, 2.4, 3.4, 6.2_

- [x] 2. FormRequest 클래스 생성(한글로 답변)
  - 외주 관련 모든 API 엔드포인트에 대한 FormRequest 클래스 구현
  - 입력 검증 규칙과 오류 메시지 정의
  - _요구사항: 3.1, 3.2, 3.3, 3.4_

- [x] 3. CarryoutService의 null safety 문제 수정(한글로 답변)
  - updateProductExport 메서드의 null pointer exception 위험 제거
  - Product 조회 시 안전한 검증 로직 구현
  - _요구사항: 1.1, 1.2, 1.3, 1.4_

- [ ] 4. CarryoutService의 상태 업데이트 로직 수정(한글로 답변)
  - updateProductImport에서 누락된 CarryoutProduct 상태 업데이트 추가
  - 상태 업데이트 공통 메서드 구현
  - _요구사항: 2.1, 2.2, 2.3, 2.4_

- [ ] 5. 컨트롤러 입력 검증 개선(한글로 답변)
  - 기존 컨트롤러 메서드들을 FormRequest 사용하도록 수정
  - 필수 필드 검증 로직 추가
  - _요구사항: 3.1, 3.2, 3.3, 3.4_

- [ ] 6. 일괄 처리와 개별 처리 로직 통일(한글로 답변)
  - 동일한 작업에 대해 일관된 비즈니스 로직 적용
  - 중복 코드 제거 및 공통 메서드 추출
  - _요구사항: 4.1, 4.2, 4.3, 4.4, 6.1, 6.3_

- [ ] 7. 트랜잭션 처리 개선(한글로 답변)
  - 부분 실패 시 롤백 시나리오 보완
  - 공통 트랜잭션 래퍼 메서드 구현
  - _요구사항: 5.1, 5.2, 5.3, 5.4_

- [ ] 8. 오류 처리 표준화(한글로 답변)
  - 일관된 오류 메시지 형식 적용
  - 로그 생성 패턴 통일
  - _요구사항: 6.2, 6.4_

- [ ] 9. 단위 테스트 작성(한글로 답변)
  - 수정된 메서드들에 대한 단위 테스트 구현
  - 예외 시나리오 테스트 케이스 작성
  - _요구사항: 모든 요구사항 검증_

- [ ] 10. 통합 테스트 및 검증(한글로 답변)
  - 전체 외주 프로세스 통합 테스트 실행
  - 기존 기능 회귀 테스트 수행
  - _요구사항: 모든 요구사항 최종 검증_