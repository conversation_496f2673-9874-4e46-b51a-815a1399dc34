# 요구사항 문서

## 소개

현재 carryout(외주 반출/반입) 시스템에서 발견된 버그와 누락된 로직들을 수정하여 시스템의 안정성과 데이터 무결성을 보장하는 개선 작업입니다. 기존 기능의 문제점을 분석하고 수정하는 것에 초점을 맞춥니다.

## 요구사항

### 요구사항 1

**사용자 스토리:** 시스템 관리자로서, 외주 반출 시 발생하는 null pointer exception을 방지하고 싶습니다. 그래야 시스템이 안정적으로 동작합니다.

#### 승인 기준

1. WHEN updateProductExport에서 존재하지 않는 QAID를 조회할 때 THEN 시스템은 null 체크 없이 first()->id를 호출하여 오류가 발생하지 않아야 합니다
2. WHEN Product::where('qaid', $qaid)->first()가 null을 반환할 때 THEN 시스템은 적절한 예외 처리를 해야 합니다
3. WHEN CarryoutProduct 중복 체크 시 THEN 시스템은 Product가 존재하지 않는 경우를 먼저 검증해야 합니다
4. WHEN 데이터베이스 조회 결과가 null일 때 THEN 시스템은 명확한 오류 메시지를 제공해야 합니다

### 요구사항 2

**사용자 스토리:** 시스템 관리자로서, 외주 반입 처리 시 누락된 CarryoutProduct 상태 업데이트가 수정되기를 원합니다. 그래야 데이터 일관성이 보장됩니다.

#### 승인 기준

1. WHEN updateProductImport가 실행될 때 THEN 시스템은 CarryoutProduct의 상태를 적절히 업데이트해야 합니다
2. WHEN 외주 반입이 완료될 때 THEN 시스템은 CarryoutProduct.status를 STATUS_RENOVATED 또는 적절한 상태로 변경해야 합니다
3. WHEN CarryoutProduct를 업데이트할 때 THEN 시스템은 carryin_at과 carryin_user_id만이 아닌 필요한 모든 필드를 업데이트해야 합니다
4. WHEN 반입 처리가 완료될 때 THEN 시스템은 관련된 모든 테이블의 상태를 일관되게 유지해야 합니다

### 요구사항 3

**사용자 스토리:** 시스템 관리자로서, 컨트롤러에서 입력 검증이 누락된 부분이 수정되기를 원합니다. 그래야 잘못된 데이터로 인한 오류를 방지할 수 있습니다.

#### 승인 기준

1. WHEN 컨트롤러에서 request 데이터를 받을 때 THEN 시스템은 필수 필드의 존재 여부를 검증해야 합니다
2. WHEN QAID 입력을 받을 때 THEN 시스템은 빈 문자열이나 null 값에 대한 검증을 해야 합니다
3. WHEN carryoutId를 받을 때 THEN 시스템은 유효한 정수인지 검증해야 합니다
4. WHEN 잘못된 입력이 들어올 때 THEN 시스템은 서비스 레이어에서 오류가 발생하기 전에 컨트롤러에서 검증해야 합니다

### 요구사항 4

**사용자 스토리:** 시스템 관리자로서, 일괄 처리와 개별 처리 간의 로직 불일치가 수정되기를 원합니다. 그래야 동일한 작업에 대해 일관된 결과를 얻을 수 있습니다.

#### 승인 기준

1. WHEN 일괄 반출(updateExport)과 개별 반출(updateProductExport)을 비교할 때 THEN 시스템은 동일한 비즈니스 로직을 적용해야 합니다
2. WHEN 일괄 반입(updateImport)과 개별 반입(updateProductImport)을 비교할 때 THEN 시스템은 동일한 상태 변경 로직을 적용해야 합니다
3. WHEN 카운터 업데이트 로직을 비교할 때 THEN 시스템은 일괄 처리와 개별 처리에서 동일한 방식으로 처리해야 합니다
4. WHEN ProductLog 생성 로직을 비교할 때 THEN 시스템은 일관된 로그 생성 패턴을 따라야 합니다

### 요구사항 5

**사용자 스토리:** 시스템 관리자로서, 트랜잭션 처리에서 누락된 롤백 시나리오가 수정되기를 원합니다. 그래야 데이터 무결성이 보장됩니다.

#### 승인 기준

1. WHEN 외주 처리 중 부분적으로 실패할 때 THEN 시스템은 모든 변경사항을 롤백해야 합니다
2. WHEN ProductLog 삽입이 실패할 때 THEN 시스템은 이전에 변경된 Product 상태도 롤백해야 합니다
3. WHEN 카운터 업데이트가 실패할 때 THEN 시스템은 관련된 모든 변경사항을 롤백해야 합니다
4. WHEN 예외가 발생할 때 THEN 시스템은 적절한 로그를 남기고 트랜잭션을 롤백해야 합니다

### 요구사항 6

**사용자 스토리:** 시스템 관리자로서, 메서드 간 중복 코드와 일관성 없는 오류 처리가 수정되기를 원합니다. 그래야 코드 유지보수가 용이합니다.

#### 승인 기준

1. WHEN 동일한 로직이 여러 메서드에서 반복될 때 THEN 시스템은 공통 메서드로 추출해야 합니다
2. WHEN 오류 메시지를 생성할 때 THEN 시스템은 일관된 형식과 내용을 사용해야 합니다
3. WHEN 상태 검증 로직을 수행할 때 THEN 시스템은 동일한 검증 규칙을 적용해야 합니다
4. WHEN 로그 생성 시 THEN 시스템은 일관된 로그 레벨과 메시지 형식을 사용해야 합니다