# 설계 문서

## 개요

현재 carryout 시스템에서 발견된 주요 문제점들을 분석하고 수정하기 위한 설계입니다. 기존 기능의 버그 수정과 누락된 로직 보완에 초점을 맞춥니다.

## 아키텍처

### 현재 구조
```
Controller Layer (CarryoutController, CarryoutProductController)
    ↓
Service Layer (CarryoutService)
    ↓
Model Layer (Carryout, CarryoutProduct, Product)
```

### 문제점 분석
1. **Null Pointer Exception 위험**: `updateProductExport`에서 Product 조회 시 null 체크 없이 `first()->id` 호출
2. **상태 업데이트 누락**: `updateProductImport`에서 CarryoutProduct 상태 업데이트 누락
3. **입력 검증 부족**: 컨트롤러에서 필수 필드 검증 누락
4. **로직 불일치**: 일괄 처리와 개별 처리 간 비즈니스 로직 차이
5. **트랜잭션 처리 미흡**: 부분 실패 시 롤백 시나리오 누락

## 컴포넌트 및 인터페이스

### 1. 입력 검증 개선 (FormRequest 사용)

#### FormRequest 클래스 생성
```php
// StoreCarryoutRequest.php
class StoreCarryoutRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // 권한 검증은 별도 미들웨어에서 처리
    }

    public function rules(): array
    {
        return [
            'id' => 'nullable|integer|exists:carryouts,id',
            'carryout_at' => 'required|date',
            'status' => 'nullable|integer|in:10,30,90',
            'memo' => 'nullable|string|max:1000',
        ];
    }

    public function messages(): array
    {
        return [
            'carryout_at.required' => '반출 날짜는 필수 입력 항목입니다.',
            'carryout_at.date' => '올바른 날짜 형식을 입력해주세요.',
            'status.in' => '올바른 상태 값을 선택해주세요.',
        ];
    }
}

// ExportCarryoutRequest.php
class ExportCarryoutRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'carryoutId' => 'required|integer|exists:carryouts,id',
        ];
    }
}

// ExportProductRequest.php
class ExportProductRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'carryoutId' => 'required|integer|exists:carryouts,id',
            'qaid' => 'required|string|max:50',
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'qaid' => mb_strtoupper(trim($this->qaid ?? '')),
        ]);
    }

    public function messages(): array
    {
        return [
            'carryoutId.required' => '외주 반출 ID는 필수 입력 항목입니다.',
            'carryoutId.exists' => '존재하지 않는 외주 반출 정보입니다.',
            'qaid.required' => 'QAID는 필수 입력 항목입니다.',
        ];
    }
}

// ImportProductRequest.php
class ImportProductRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'qaid' => 'required|string|max:50',
            'repair_symptom_id' => 'nullable|integer|exists:repair_symptoms,id',
            'repair_process_id' => 'nullable|integer|exists:repair_processes,id',
            'repair_grade_id' => 'nullable|integer|exists:repair_grades,id',
            'memo' => 'nullable|string|max:1000',
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'qaid' => mb_strtoupper(trim($this->qaid ?? '')),
        ]);
    }
}
```

#### 컨트롤러 수정사항
```php
// CarryoutController.php
public function store(StoreCarryoutRequest $request): JsonResponse
{
    try {
        $data = $request->validated();
        $user = Auth::user();
        $this->carryoutService->store($data, $user);
        
        return $this->successResponse([
            'message' => '외주반출 정보등록 성공'
        ]);
    } catch (Exception $e) {
        return $this->errorResponse([
            'message' => "외주 반출 정보 등록에 실패하였습니다",
        ], $e, 'carryout', 500);
    }
}

public function export(ExportCarryoutRequest $request): JsonResponse
{
    try {
        $data = $request->validated();
        $user = Auth::user();
        $this->carryoutService->export($data, $user);
        
        return $this->successResponse([
            'message' => 'RP상품 외주 반출처리 성공'
        ]);
    } catch (Exception $e) {
        return $this->errorResponse([
            'message' => "외주 반출 처리에 실패하였습니다.",
        ], $e, 'carryout', 500);
    }
}

// CarryoutProductController.php
public function export(ExportProductRequest $request): JsonResponse
{
    try {
        $data = $request->validated();
        $user = Auth::user();
        $this->carryoutService->exportProduct($data, $user);
        
        return $this->successResponse([
            'message' => '외부 점검을 위한 외주 반출 성공'
        ]);
    } catch (Exception $e) {
        return $this->errorResponse([
            'message' => $e->getMessage(),
        ], $e, 'carryout', $e->getCode());
    }
}

public function import(ImportProductRequest $request): JsonResponse
{
    try {
        $data = $request->validated();
        $user = Auth::user();
        $this->carryoutService->importProduct($data, $user);
        
        return $this->successResponse([
            'message' => '외주 반출 상품에 대한 반입 처리 성공'
        ]);
    } catch (Exception $e) {
        return $this->errorResponse([
            'message' => "외주 반출 상품에 반입 처리에 실패하였습니다.",
        ], $e, 'carryout', 500);
    }
}
```

### 2. CarryoutService 개선

#### Null Safety 개선 (범용 예외 사용)
```php
// 기존 문제점
$isCarryoutProduct = CarryoutProduct::where('product_id', 
    Product::where('qaid', mb_strtoupper($data['qaid']))->first()->id
)->exists();

// 개선안
private function findProduct(string $qaid): Product
{
    $product = Product::where('qaid', mb_strtoupper($qaid))->first();
    if ($product === null) {
        ExceptionHelper::throwNotFound('상품', $qaid);
    }
    return $product;
}

private function checkDuplicate(Product $product): void
{
    $exists = CarryoutProduct::where('product_id', $product->id)->exists();
    if ($exists) {
        ExceptionHelper::throwDuplicate('외주 반출 상품', $product->qaid);
    }
}
```

#### 상태 업데이트 로직 통일
```php
// CarryoutProduct 상태 업데이트 공통 메서드
private function updateStatus(CarryoutProduct $carryoutProduct, int $status, User $user): void
{
    $now = now();
    $updateData = ['status' => $status];
    
    switch ($status) {
        case CarryoutProduct::STATUS_ONBOARD:
            $updateData['checked_user_id'] = $user->id;
            $updateData['checked_at'] = $now;
            break;
        case CarryoutProduct::STATUS_RENOVATED:
            $updateData['renovator_id'] = $user->id;
            $updateData['renovate_at'] = $now;
            break;
    }
    
    $carryoutProduct->update($updateData);
}
```

### 3. 트랜잭션 처리 개선

#### 공통 트랜잭션 래퍼
```php
private function transaction(callable $operation, string $name): void
{
    try {
        DB::beginTransaction();
        $operation();
        DB::commit();
        
        SimpleLogService::info('carryout', "{$name} 성공");
    } catch (Exception $e) {
        DB::rollBack();
        SimpleLogService::error('carryout', "{$name} 실패", [], $e);
        throw $e;
    }
}
```

## 데이터 모델

### CarryoutProduct 상태 관리 개선

#### 현재 상태 정의
```php
const STATUS_ONBOARD = 10;     // 수리대기
const STATUS_RENOVATED = 20;   // 수리완료
const STATUS_CANCELED = 90;    // 취소
```

#### 상태 전이 규칙
```
반출 시: Product.status = CARRIED_OUT, CarryoutProduct.status = ONBOARD
반입 시: Product.status = REGISTERED, CarryoutProduct.status = RENOVATED (수리 완료된 경우)
취소 시: Product.status = REGISTERED, CarryoutProduct 삭제
```

### 필드 매핑 정리

#### CarryoutProduct 테이블 필드 활용
```php
// 현재 미사용 필드들의 활용 방안
'repair_symptom_id'  => '수리 증상 정보 저장'
'repair_process_id'  => '수리 과정 정보 저장' 
'repair_grade_id'    => '수리 등급 정보 저장'
'renovator_id'       => '수리 담당자 정보 저장'
'renovate_at'        => '수리 완료 시간 저장'
'memo'               => '수리 관련 메모 저장'
```

## 오류 처리

### 표준화된 오류 메시지 (범용 설계)
```php
class ErrorMessages
{
    // 공통 메시지
    const RESOURCE_NOT_FOUND = "해당 %s [%s]이 존재하지 않습니다.";
    const RESOURCE_ALREADY_PROCESSED = "해당 %s [%s]은 이미 처리된 항목입니다.";
    const REQUIRED_FIELD_MISSING = "필수 필드가 누락되었습니다: %s";
    const INVALID_STATUS = "올바르지 않은 상태입니다: %s";
    const PERMISSION_DENIED = "해당 작업에 대한 권한이 없습니다.";
    
    // 상품 관련
    const PRODUCT_NOT_FOUND = "해당 상품 [%s]이 존재하지 않습니다.";
    const PRODUCT_INVALID_STATUS = "해당 상품 [%s]의 상태가 올바르지 않습니다.";
    
    // 외주 관련
    const CARRYOUT_NOT_FOUND = "해당 외주 반출 정보 [%s]가 존재하지 않습니다.";
    const PRODUCT_ALREADY_CARRIED_OUT = "해당 상품 [%s]은 이미 외주 반출 처리된 상품입니다.";
    const PRODUCT_NOT_CARRIED_OUT = "해당 상품 [%s]은 외주 반출 상태가 아닙니다.";
    const PRODUCT_ALREADY_IMPORTED = "해당 상품 [%s]은 이미 반입 완료된 상품입니다.";
    
    // 창고 관련 (확장 예시)
    const WAREHOUSE_NOT_FOUND = "해당 창고 [%s]가 존재하지 않습니다.";
    const WAREHOUSE_FULL = "해당 창고 [%s]의 용량이 부족합니다.";
    
    // 출고 관련 (확장 예시)
    const SHIPPING_NOT_FOUND = "해당 출고 정보 [%s]가 존재하지 않습니다.";
    const SHIPPING_ALREADY_COMPLETED = "해당 출고 [%s]는 이미 완료된 상태입니다.";
}

// 메시지 헬퍼 클래스
class MessageHelper
{
    public static function format(string $template, ...$args): string
    {
        return sprintf($template, ...$args);
    }
    
    public static function productNotFound(string $qaid): string
    {
        return self::format(ErrorMessages::PRODUCT_NOT_FOUND, $qaid);
    }
    
    public static function resourceNotFound(string $resource, string $identifier): string
    {
        return self::format(ErrorMessages::RESOURCE_NOT_FOUND, $resource, $identifier);
    }
    
    public static function alreadyProcessed(string $resource, string $identifier): string
    {
        return self::format(ErrorMessages::RESOURCE_ALREADY_PROCESSED, $resource, $identifier);
    }
}
```

### 예외 처리 계층화 (범용 설계)
```php
// 기본 WMS 예외 클래스
abstract class WmsException extends Exception
{
    protected string $module;
    protected array $context = [];

    public function __construct(string $message, int $code = 0, ?Throwable $previous = null, array $context = [])
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    public function getModule(): string
    {
        return $this->module;
    }

    public function getContext(): array
    {
        return $this->context;
    }
}

// 비즈니스 로직 예외 (범용)
class BusinessException extends WmsException
{
    protected string $module = 'business';
}

// 데이터 검증 예외 (범용)
class ValidationException extends WmsException
{
    protected string $module = 'validation';
}

// 데이터베이스 관련 예외 (범용)
class DataException extends WmsException
{
    protected string $module = 'data';
}

// 권한 관련 예외 (범용)
class AuthorizationException extends WmsException
{
    protected string $module = 'auth';
}

// 리소스 없음 예외 (범용)
class ResourceNotFoundException extends WmsException
{
    protected string $module = 'resource';
}

// 모듈별 특화 예외 (필요시 확장)
class CarryoutException extends BusinessException
{
    protected string $module = 'carryout';
}

class WarehouseException extends BusinessException
{
    protected string $module = 'warehouse';
}

class ShippingException extends BusinessException
{
    protected string $module = 'shipping';
}
```

### 예외 처리 헬퍼 클래스
```php
class ExceptionHelper
{
    public static function throwNotFound(string $resource, string $identifier): void
    {
        throw new ResourceNotFoundException(
            "해당 {$resource} [{$identifier}]이 존재하지 않습니다.",
            404,
            null,
            ['resource' => $resource, 'identifier' => $identifier]
        );
    }

    public static function throwValidation(string $field, string $message): void
    {
        throw new ValidationException(
            $message,
            422,
            null,
            ['field' => $field]
        );
    }

    public static function throwBusiness(string $message, string $module = 'general'): void
    {
        throw new BusinessException(
            $message,
            400,
            null,
            ['module' => $module]
        );
    }

    public static function throwDuplicate(string $resource, string $identifier): void
    {
        throw new BusinessException(
            "해당 {$resource} [{$identifier}]은 이미 처리된 항목입니다.",
            409,
            null,
            ['resource' => $resource, 'identifier' => $identifier, 'type' => 'duplicate']
        );
    }
}
```

## 테스트 전략

### 단위 테스트 대상
1. **입력 검증 로직**
   - 필수 필드 누락 시 예외 발생 확인
   - QAID 형식 검증 확인
   
2. **비즈니스 로직**
   - 상품 존재 여부 검증
   - 중복 처리 방지 로직
   - 상태 전이 규칙 준수
   
3. **트랜잭션 처리**
   - 부분 실패 시 롤백 확인
   - 로그 생성 확인

### 통합 테스트 시나리오
1. **정상 플로우**
   - 반출 → 반입 → 완료
   - 일괄 처리 vs 개별 처리 결과 일치성
   
2. **예외 시나리오**
   - 존재하지 않는 상품 처리
   - 중복 처리 시도
   - 잘못된 상태에서의 처리 시도

## 성능 고려사항

### 데이터베이스 쿼리 최적화
```php
// 기존: N+1 쿼리 문제 가능성
$carryoutProducts = CarryoutProduct::where('carryout_id', $id)->get();
foreach ($carryoutProducts as $item) {
    $product = $item->product; // 추가 쿼리 발생
}

// 개선: Eager Loading 사용
$carryoutProducts = CarryoutProduct::with('product')->where('carryout_id', $id)->get();
```

### 대량 데이터 처리
```php
// 일괄 처리 시 청크 단위로 처리
private function processBulkOperation(array $ids, callable $processor): void
{
    collect($ids)->chunk(100)->each(function ($chunk) use ($processor) {
        $processor($chunk->toArray());
    });
}
```

## 보안 고려사항

### 권한 검증 강화
```php
// 사용자 권한 확인
private function validateUserPermission(User $user, string $operation): void
{
    if (!$user->hasPermission("carryout.{$operation}")) {
        throw new UnauthorizedException("해당 작업에 대한 권한이 없습니다.");
    }
}
```

### 입력 데이터 검증
```php
// SQL Injection 방지를 위한 파라미터 바인딩 사용
// XSS 방지를 위한 입력 데이터 이스케이프 처리
private function sanitizeInput(string $input): string
{
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}
```