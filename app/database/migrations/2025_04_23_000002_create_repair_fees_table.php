<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('repair_fees', function (Blueprint $table) {
            $table->id();
            $table->foreignId('repair_fee_range_id')->comment('범위 인덱스');
            $table->enum('repair_type', ['CHECK', 'REPAIR', 'OSinstall', 'COMPLETE', 'DOA', 'BEST', 'GOOD', 'NORMAL'])->default('CHECK')->comment('청구 유형 (CK: 점검, RP: 수리, OS: OS재설치)');
            $table->unsignedBigInteger('amount')->default(0)->comment('수리 비용');
            $table->timestamps();

            $table->foreign('repair_fee_range_id')
                ->on('repair_fee_ranges')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // 복합 인덱스 추가 (자주 함께 조회되는 컬럼들)
            $table->index(['repair_fee_range_id', 'repair_type'], 'repair_fees_range_type_index');
            $table->index(['repair_type', 'amount'], 'repair_fees_type_amount_index');
            
            // 기존 단일 인덱스들
            $table->index('repair_fee_range_id');
            $table->index('repair_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_fees');
    }
};
