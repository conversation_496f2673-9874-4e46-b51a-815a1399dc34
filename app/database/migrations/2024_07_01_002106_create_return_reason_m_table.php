<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('return_reason_m', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique()->comment('선택 이유 (예: 구성품 누락, 단순 변심)');

            // 인덱스 추가
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('return_reason_m');
    }
};
