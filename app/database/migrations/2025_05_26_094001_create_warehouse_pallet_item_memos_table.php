<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('warehouse_pallet_item_memos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('warehouse_pallet_item_id')->comment('팔레트 아이템 인덱스');
            $table->enum('status', ['STORED','INTERNAL_USE','INTERNAL_PARTS','RETURNED','DISCARDED'])
                ->default('STORED')
                ->comment('메모 상태(STORED, INTERNAL_USE, INTERNAL_PARTS, RETURNED, DISCARDED)');
            $table->foreignId('created_by')->comment('작성자 인덱스');
            $table->text('memo')->comment('메모 내용');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('warehouse_pallet_item_id')
                ->on('warehouse_pallet_items')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('created_by')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');
        });
    }
};
