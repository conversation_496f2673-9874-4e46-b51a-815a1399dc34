<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('areas', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment("지역명");
            $table->string('code')->comment("지역 코드");
            $table->text('description')->nullable()->comment("지역 설명");
            $table->boolean('is_active')->default(true)->comment("활성 여부");
            $table->timestamps();
            $table->softDeletes();

            $table->unique('code');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('areas');
    }
};
