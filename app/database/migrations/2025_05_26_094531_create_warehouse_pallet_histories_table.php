<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('warehouse_pallet_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('warehouse_pallet_item_id')->comment('창고 팔레트 아이템 인덱스');
            $table->foreignId('position_id')->comment('위치 인덱스');
            $table->enum('action_type', ['created', 'moved', 'updated', 'deleted', 'restored'])->default('created')->comment('행동 유형(created, moved, updated, deleted, restored)');
            $table->text('action_details')->nullable()->comment('행동 세부사항');
            $table->foreignId('performed_by')->comment('수행자 인덱스');
            $table->timestamps();

            $table->foreign('warehouse_pallet_item_id')
                ->on('warehouse_pallet_items')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('position_id')
                ->on('positions')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('performed_by')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_pallet_histories');
    }
};
