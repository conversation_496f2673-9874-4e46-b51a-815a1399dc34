<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('repair_parts_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('repair_parts_id')->comment('수리 구성품 인덱스');
            $table->unsignedBigInteger('price')->default(0)->comment('구입 단가');
            $table->unsignedMediumInteger('quantity')->default(0)->comment('구입 개수');
            $table->date('purchase_date')->nullable()->comment('구입일');
            $table->text('memo')->comment('메모');
            $table->timestamps();

            $table->foreign('repair_parts_id')
                ->on('repair_parts')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // 복합 인덱스 추가 (자주 함께 조회되는 컬럼들)
            $table->index(['repair_parts_id', 'purchase_date'], 'repair_parts_orders_parts_date_index');
            $table->index(['purchase_date', 'price'], 'repair_parts_orders_date_price_index');
            
            // 기존 단일 인덱스들
            $table->index('repair_parts_id');
            $table->index('purchase_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_parts_orders');
    }
};
