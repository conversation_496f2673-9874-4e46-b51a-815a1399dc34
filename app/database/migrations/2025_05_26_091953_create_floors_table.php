<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('floors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('zone_id')->comment('존 인덱스');
            $table->string('name')->comment('층 명');
            $table->string('code')->comment('층 코드');
            $table->unsignedTinyInteger('level')->comment('층 레벨');
            $table->text('description')->nullable()->comment('층 설명');
            $table->boolean('is_active')->default(true)->comment('활성 여부');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('zone_id')
                ->on('zones')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unique('code');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('floors');
    }
};
