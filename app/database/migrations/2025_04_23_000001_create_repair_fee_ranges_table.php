<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('repair_fee_ranges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('repair_category_id')->comment('수리 카테고리 인덱스');
            $table->enum('type', ['general', 'monitor', 'apple'])->default('general')->comment('수리비 분류');
            $table->enum('model', ['brand', '맥북', '아이맥', 'etc'])->default('etc')->comment('모델별 분류');
            $table->enum('fee_type', ['size', 'price', 'none'])->default('none')->comment('가격 결정 기준 (size: 크기, price: 판매가, none: 공통)');
            $table->enum('fee_unit', ['won', 'cm', 'inch'])->default('won')->comment('가격 결정 기준 단위 (cm: cm, inch: 인치, won: 금액)');
            $table->decimal('min_value', 15, 2)->default(0)->comment('구간 최소값');
            $table->decimal('max_value', 15, 2)->default(0)->comment('구간 최대값');
            $table->string('memo')->nullable()->comment('메모');

            $table->foreign('repair_category_id')
                ->on('repair_categories')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // 복합 인덱스 추가 (자주 함께 조회되는 컬럼들)
            $table->index(['repair_category_id', 'type', 'model'], 'repair_fee_ranges_category_type_model_index');
            $table->index(['repair_category_id', 'fee_type'], 'repair_fee_ranges_category_fee_type_index');
            $table->index(['min_value', 'max_value'], 'repair_fee_ranges_value_range_index');
            
            // 기존 단일 인덱스들
            $table->index('repair_category_id');
            $table->index('type');
            $table->index('model');
            $table->index('fee_type');
            $table->index('fee_unit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_fee_ranges');
    }
};
