<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('return_reason_b', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique()->comment('B 카테고리명 (예: 고객 변심, 배송 불만, 상품 불량)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('return_reason_b');
    }
};
