<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('repair_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->comment('상품 인덱스');
            $table->unsignedTinyInteger('status')->default(30)->comment('상태(30: 완료(창고), 80: 대기(구성품 신청), 90: 삭제)');
            $table->unsignedBigInteger('waiting_user_id')->nullable()->comment('구성품 신청 직원 인덱스');
            $table->dateTime('waiting_at')->nullable()->comment('구성품 신청 일시');
            $table->unsignedBigInteger('completed_user_id')->nullable()->comment('마지막으로 수리/점검한 직원 인덱스(구성품 대기로 인한)');
            $table->dateTime('completed_at')->nullable()->comment('마지막으로 수리/점검 일시');
            $table->unsignedInteger('amount')->default(0)->comment('판매 단가(상품 등록값과 동일)');
            $table->foreignId('repair_symptom_id')->nullable()->comment('진단내용(상품상태)번호');;
            $table->foreignId('repair_process_id')->nullable()->comment('처리내용(수리내역)번호');
            $table->foreignId('repair_grade_id')->nullable()->comment('분류등급(상품등급)번호');
            $table->unsignedInteger('invoice1')->default(0)->comment('청구금액1(기본)');
            $table->unsignedInteger('invoice2')->default(0)->comment('청구금액2(추가)');
            $table->unsignedInteger('invoice3')->default(0)->comment('청구금액3(추가)');
            $table->unsignedTinyInteger('is_os_install')->default(0)->comment('OS재설치 여부(0: 미설치 , 1: 재설치)');
            $table->string('fee_type')->nullable()->comment('수리비 타입(size, price, none)');
            $table->string('fee_unit')->nullable()->comment('수리비 단위(cm, inch, won)');
            $table->enum('is_default_fee', ['Y', 'N'])->default('N')->comment('기본 수리비 여부(Y: 기본, N: 사용자 지정)');
            $table->foreignId('repair_fee_range_id')->nullable()->comment('수리비 범위 인덱스');
            $table->Integer('default_fee_range_id')->nullable()->comment('수리비(기본) 범위 인덱스');
            $table->text('memo')->nullable()->comment('메모');
            $table->timestamps();
            $table->softDeletes()->comment('삭제 일시');

            $table->foreign('product_id')
                ->on('products')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('waiting_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('completed_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_symptom_id')
                ->on('repair_symptoms')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_process_id')
                ->on('repair_processes')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_grade_id')
                ->on('repair_grades')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_fee_range_id')
                ->on('repair_fee_ranges')
                ->references('id')
                ->onUpdate('cascade');

            // 복합 인덱스 추가 (자주 함께 조회되는 컬럼들)
            $table->index(['status', 'waiting_at'], 'repair_products_status_waiting_index');
            $table->index(['status', 'completed_at'], 'repair_products_status_completed_index');
            $table->index(['repair_symptom_id', 'repair_process_id'], 'repair_products_symptom_process_index');
            $table->index(['repair_grade_id', 'status'], 'repair_products_grade_status_index');
            $table->index(['waiting_user_id', 'waiting_at'], 'repair_products_waiting_user_date_index');
            $table->index(['completed_user_id', 'completed_at'], 'repair_products_completed_user_date_index');

            // 기존 단일 인덱스들
            $table->index('is_default_fee');
            $table->index('status');
            $table->index('updated_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('repair_products');
    }
};
