<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 작업 통계를 내기 위한 작업 상태 저장
        Schema::create('work_statuses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->nullable()->comment('카테고리 인덱스');
            $table->foreignId('action_id')->nullable()->comment('액션 인덱스');
            $table->foreignId('template_id')->nullable()->comment('템플릿 인덱스');
            $table->boolean('auto_generated')->default(false)->comment('자동 생성 여부');
            $table->json('generation_context')->nullable()->comment('생성 컨텍스트');
            $table->string('code')->comment('상태 코드');
            $table->string('name')->comment('상태 이름');
            $table->string('description')->nullable();
            $table->string('link_code')->nullable()->comment('연결될 프로그램 코드(프로그래머가 소스에 직접 연결해 줘야 함)');
            $table->timestamps();

            $table->foreign('category_id')
                ->references('id')
                ->on('work_categories')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('action_id')
                ->references('id')
                ->on('work_actions')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('template_id')
                ->references('id')
                ->on('work_status_templates')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unique('code');
            $table->unique('name');
            $table->unique('link_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_statuses');
    }
};
