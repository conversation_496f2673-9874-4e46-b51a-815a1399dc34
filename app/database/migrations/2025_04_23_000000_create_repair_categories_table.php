<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cate4_id')->comment('4차 카테고리');
            $table->foreignId('cate5_id')->nullable()->comment('5차 카테고리');

            $table->foreign('cate4_id')
                ->on('cate4')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('cate5_id')
                ->on('cate5')
                ->references('id')
                ->onUpdate('cascade');

            // 복합 유니크 제약조건 추가 (동일한 카테고리 조합 방지)
            $table->unique(['cate4_id', 'cate5_id'], 'repair_categories_cate4_cate5_unique');
            
            // 기존 인덱스들
            $table->index(['cate4_id', 'cate5_id']);
            $table->index('cate4_id');
            $table->index('cate5_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_categories');
    }
};
