<?php

namespace Database\Factories;

use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\RepairCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairCategory>
 */
class RepairCategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cate4_id' => Cate4::factory(),
            'cate5_id' => Cate5::factory(),
        ];
    }

    /**
     * 5차 카테고리가 없는 상태로 생성
     */
    public function withoutCate5(): static
    {
        return $this->state(fn (array $attributes) => [
            'cate5_id' => null,
        ]);
    }
} 