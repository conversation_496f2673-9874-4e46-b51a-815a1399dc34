<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Req;
use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\ProductLot;
use App\Models\ProductVendor;
use App\Models\ProductLink;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'req_id' => Req::factory(),
            'qaid' => $this->faker->unique()->uuid(),
            'barcode' => $this->faker->unique()->ean13(),
            'name' => $this->faker->word(),
            'cate4_id' => Cate4::factory(),
            'cate5_id' => Cate5::factory(),
            'quantity' => $this->faker->numberBetween(1, 10),
            'amount' => $this->faker->numberBetween(1000, 100000),
            'user_id' => null,
            'status' => $this->faker->randomElement([10,19,20,30,50,70,80,90]),
            'rg' => $this->faker->randomElement(['Y','N']),
            'duplicated' => $this->faker->randomElement(['Y','N']),
            'checked_at' => null,
            'checked_status' => $this->faker->randomElement([10,20]),
            'checked_user_id' => null,
            'product_lot_id' => null,
            'product_vendor_id' => null,
            'product_link_id' => null,
            'memo' => $this->faker->optional()->sentence(),
        ];
    }
}
