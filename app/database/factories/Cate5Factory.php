<?php

namespace Database\Factories;

use App\Models\Cate4;
use App\Models\Cate5;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Cate5>
 */
class Cate5Factory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $names = [
            '삼성 노트북', 'LG 노트북', '애플 맥북', 'HP 노트북', '델 노트북',
            '삼성 데스크톱', 'LG 데스크톱', '애플 아이맥', 'HP 데스크톱', '델 데스크톱',
            '삼성 모니터', 'LG 모니터', '애플 디스플레이', 'HP 모니터', '델 모니터'
        ];

        return [
            'cate5_no' => $this->faker->unique()->numberBetween(1, 999),
            'cate4_id' => Cate4::factory(),
            'name' => $names[array_rand($names)],
        ];
    }
} 