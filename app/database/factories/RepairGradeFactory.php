<?php

namespace Database\Factories;

use App\Models\RepairGrade;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairGrade>
 */
class RepairGradeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->text(20),
            'code' => $this->faker->unique()->regexify('[A-Z]{2}[0-9]{3}'),
        ];
    }
} 