<?php

namespace Database\Factories;

use App\Models\RepairSymptom;
use App\Models\RepairProcess;
use App\Models\RepairGrade;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairSymptom>
 */
class RepairSymptomFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type' => $this->faker->randomElement(['hardware', 'software', 'network']),
            'name' => $this->faker->text(30),
            'code' => $this->faker->unique()->regexify('[A-Z]{2}[0-9]{4}'),
            'default_repair_process_id' => RepairProcess::factory(),
            'default_repair_grade_id' => RepairGrade::factory(),
        ];
    }
} 