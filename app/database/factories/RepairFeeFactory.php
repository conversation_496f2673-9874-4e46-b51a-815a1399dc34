<?php

namespace Database\Factories;

use App\Models\RepairFee;
use App\Models\RepairFeeRange;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairFee>
 */
class RepairFeeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'repair_fee_range_id' => RepairFeeRange::factory(),
            'repair_type' => $this->faker->randomElement(['CHECK', 'REPAIR', 'OSinstall', 'COMPLETE', 'DOA', 'BEST', 'GOOD', 'NORMAL']),
            'amount' => $this->faker->numberBetween(1000, 100000),
        ];
    }

    /**
     * 점검 타입으로 생성
     */
    public function check(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => 'CHECK',
            'amount' => $this->faker->numberBetween(5000, 10000),
        ]);
    }

    /**
     * 수리 타입으로 생성
     */
    public function repair(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => 'REPAIR',
            'amount' => $this->faker->numberBetween(10000, 50000),
        ]);
    }

    /**
     * OS 재설치 타입으로 생성
     */
    public function osInstall(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => 'OSinstall',
            'amount' => $this->faker->numberBetween(15000, 30000),
        ]);
    }
} 