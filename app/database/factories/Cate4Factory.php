<?php

namespace Database\Factories;

use App\Models\Cate4;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Cate4>
 */
class Cate4Factory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $names = [
            '노트북', '데스크톱', '모니터', '프린터', '스캐너', 
            '키보드', '마우스', '스피커', '헤드폰', '웹캠',
            '마이크', '태블릿', '스마트폰', '서버', '네트워크'
        ];

        return [
            'cate4_no' => $this->faker->unique()->numberBetween(1, 999),
            'name' => $names[array_rand($names)],
        ];
    }
} 