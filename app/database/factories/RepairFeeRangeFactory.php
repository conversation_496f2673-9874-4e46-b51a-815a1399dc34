<?php

namespace Database\Factories;

use App\Models\RepairCategory;
use App\Models\RepairFeeRange;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairFeeRange>
 */
class RepairFeeRangeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'repair_category_id' => RepairCategory::factory(),
            'type' => $this->faker->randomElement(['general', 'monitor', 'apple']),
            'model' => $this->faker->randomElement(['brand', '맥북', '아이맥', 'etc']),
            'fee_type' => $this->faker->randomElement(['size', 'price', 'none']),
            'fee_unit' => $this->faker->randomElement(['won', 'cm', 'inch']),
            'min_value' => $this->faker->randomFloat(2, 0, 1000000),
            'max_value' => $this->faker->randomFloat(2, 1000000, 5000000),
            'memo' => $this->faker->optional()->sentence(),
        ];
    }

    /**
     * 일반 타입으로 생성
     */
    public function general(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'general',
            'model' => 'etc',
        ]);
    }

    /**
     * 모니터 타입으로 생성
     */
    public function monitor(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'monitor',
            'model' => 'brand',
            'fee_type' => 'size',
            'fee_unit' => 'inch',
        ]);
    }

    /**
     * 애플 타입으로 생성
     */
    public function apple(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'apple',
            'model' => $this->faker->randomElement(['맥북', '아이맥']),
        ]);
    }
} 