<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\RepairProduct;
use App\Models\RepairSymptom;
use App\Models\RepairProcess;
use App\Models\RepairGrade;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairProduct>
 */
class RepairProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'status' => RepairProduct::STATUS_WAITING,
            'waiting_user_id' => User::factory(),
            'completed_user_id' => null,
            'amount' => $this->faker->numberBetween(100000, 1000000),
            'repair_symptom_id' => RepairSymptom::factory(),
            'repair_process_id' => RepairProcess::factory(),
            'repair_grade_id' => RepairGrade::factory(),
            'invoice1' => $this->faker->numberBetween(10000, 100000),
            'invoice2' => $this->faker->numberBetween(5000, 50000),
            'invoice3' => $this->faker->numberBetween(1000, 10000),
            'waiting_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'completed_at' => null,
        ];
    }

    /**
     * 완료 상태로 생성
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => RepairProduct::STATUS_REPAIRED,
            'completed_user_id' => User::factory(),
            'completed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * 삭제 상태로 생성
     */
    public function deleted(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => RepairProduct::STATUS_DELETED,
        ]);
    }
} 