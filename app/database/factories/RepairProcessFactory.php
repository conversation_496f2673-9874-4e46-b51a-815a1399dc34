<?php

namespace Database\Factories;

use App\Models\RepairProcess;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairProcess>
 */
class RepairProcessFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->text(30),
            'code' => $this->faker->unique()->regexify('[A-Z]{2}[0-9]{4}'),
        ];
    }
} 