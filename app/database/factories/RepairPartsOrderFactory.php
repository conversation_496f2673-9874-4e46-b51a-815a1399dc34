<?php

namespace Database\Factories;

use App\Models\RepairParts;
use App\Models\RepairPartsOrder;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairPartsOrder>
 */
class RepairPartsOrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'repair_parts_id' => RepairParts::factory(),
            'quantity' => $this->faker->numberBetween(1, 20),
            'price' => $this->faker->numberBetween(1000, 100000),
            'status' => $this->faker->randomElement(['pending', 'ordered', 'received', 'cancelled']),
            'order_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'expected_date' => $this->faker->dateTimeBetween('now', '+1 month'),
            'received_date' => null,
            'memo' => $this->faker->optional()->text(50),
        ];
    }

    /**
     * 주문 완료 상태로 생성
     */
    public function ordered(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'ordered',
        ]);
    }

    /**
     * 입고 완료 상태로 생성
     */
    public function received(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'received',
            'received_date' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }
} 