<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Req>
 */
class ReqFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'req_at' => $this->faker->date(),
            'req_type' => $this->faker->randomElement([1,2,9]),
            'status' => $this->faker->randomElement([10,90]),
            'user_id' => null,
            'checked_user_id' => null,
            'checked_at' => null,
            'memo' => $this->faker->optional()->sentence(),
            'total_count' => $this->faker->numberBetween(0, 100),
        ];
    }
}
