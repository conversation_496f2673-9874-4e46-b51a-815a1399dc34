<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'telegram' => [
        'bot_token' => env('TELEGRAM_BOT_TOKEN'),
        'chat_id' => env('TELEGRAM_CHAT_ID'),
    ],
    'slack' => [
        'notify' => env('LOG_NOTIFY_SLACK_WEBHOOK_URL'),
        'req' => env('LOG_REQ_SLACK_WEBHOOK_URL'),
        'inspection' => env('LOG_INSPECTION_SLACK_WEBHOOK_URL'),
        'product' => env('LOG_PRODUCT_SLACK_WEBHOOK_URL'),
        'repair' => env('LOG_REPAIR_SLACK_WEBHOOK_URL'),
        'pallet' => env('LOG_PALLET_SLACK_WEBHOOK_URL'),
        'carryout' => env('LOG_CARRYOUT_SLACK_WEBHOOK_URL'),
        'setting' => env('LOG_SETTING_SLACK_WEBHOOK_URL'),
    ],
];
