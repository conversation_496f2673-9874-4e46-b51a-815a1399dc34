<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Excel Header Mappings
    |--------------------------------------------------------------------------
    |
    | 엑셀 파일의 헤더를 내부 필드명으로 매핑하는 설정입니다.
    | 원청에서 제공하는 엑셀 파일의 헤더가 자주 변경되므로,
    | 다양한 헤더명을 지원하도록 설정합니다.
    |
    */

    'mappings' => [
        // 필수 필드들 (반드시 있어야 하는 필드)
        'required' => [
            'qaid' => [
                'QAID', 'qaid', 'Qaid', 'qa_id', 'QA_ID'
            ],
            'cate4' => [
                'CATE4', 'Cate4', 'cate4', '카테고리4', '카테4', 'CATEGORY4', 'Category4'
            ],
            'cate5' => [
                'CATE5', 'Cate5', 'cate5', '카테고리5', '카테5', 'CATEGORY5', 'Category5'
            ],
            'lot_full_name' => [
                'LOT_FULL_NAME', 'LOT FULL NAME', 'lot_full_name', '로트명', 'LOT명', 'Lot Name'
            ],
            'wms_sku_id' => [
                'wmsSkuId', 'wms_sku_id', 'WMS_SKU_ID', 'WMS SKU ID', 'wmsSkuld', 'WMS_SKULD' // 오타 포함
            ],
            'external_sku_id' => [
                'externalSkuId', 'external_sku_id', 'EXTERNAL_SKU_ID', 'externalSkuld', 'EXTERNAL_SKULD' // 오타 포함
            ],
            'barcode' => [
                'BARCODE', 'barcode', '바코드', 'Barcode', 'BARCODEDESCRIPTIONDOR_NAQ' // 오타 포함
            ],
            'description' => [
                'DESCRIPTION', 'description', '상품명', '상품설명', 'PRODUCT_NAME', 'Product Name'
            ],
            'vendor_name' => [
                'VENDOR_NAME', 'vendor_name', 'VENDOR NAME', '공급업체명', '벤더명', 'Vendor Name'
            ],
            'quantity' => [
                'QUANTITY', 'quantity', '수량', 'QTY', 'Quantity'
            ],
            'amount' => [
                'AMOUNT', 'amount', '금액', '가격', 'Amount', 'PRICE'
            ],
            'vendor_item_id' => [
                'vendoritemid', 'vendor_item_id', 'VENDOR_ITEM_ID', '공급업체상품ID', 'Vendor Item ID'
            ],
        ],

        // 선택적 필드들 (있으면 좋고 없어도 되는 필드)
        'optional' => [
            'product_id' => [
                'productid', 'product_id', 'PRODUCT_ID', '상품ID', 'Product ID'
            ],
            'item_id' => [
                'itemid', 'item_id', 'ITEM_ID', '아이템ID', 'Item ID'
            ],
            'rg' => [
                '구분', 'RG', 'rg', 'RG구분', 'RG_구분', 'RG_DIVISION', 'Division'
            ],
            'center' => [
                '센터', 'CENTER', 'center', '센터명', 'CENTER_NAME', 'Center Name'
            ],
            'b_cate' => [
                'bCate', 'b_cate', 'B_CATE', 'bCate', 'BCATE', 'B_CATE'
            ],
            'm_cate' => [
                'mCate', 'm_cate', 'M_CATE', 'mCate', 'MCATE', 'M_CATE'
            ],
            'detail_reason' => [
                'DetailReason', 'detail_reason', 'DETAIL_REASON', '상세사유', 'Detail Reason'
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Header Validation Rules
    |--------------------------------------------------------------------------
    |
    | 헤더 검증 규칙을 설정합니다.
    |
    */

    'validation' => [
        // 최소 필수 헤더 수
        'min_required_headers' => 5,

        // 허용되는 최대 헤더 수
        'max_total_headers' => 20,

        // 헤더명 최대 길이
        'max_header_length' => 50,
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Messages
    |--------------------------------------------------------------------------
    |
    | 헤더 매핑 실패 시 표시할 에러 메시지입니다.
    |
    */

    'error_messages' => [
        'missing_required' => '필수 헤더가 누락되었습니다: :headers',
        'invalid_header_format' => '헤더 형식이 올바르지 않습니다: :header',
        'too_many_headers' => '헤더 수가 너무 많습니다. 최대 :max 개까지 허용됩니다.',
        'too_few_headers' => '헤더 수가 너무 적습니다. 최소 :min 개가 필요합니다.',
    ],
];
