<?php

namespace Tests\Feature;

use App\Imports\VidImport;
use App\Models\ProductLink;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Tests\TestCase;

class VidImportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('local');
    }

    /**
     * VidImport 기본 동작 테스트
     */
    public function test_vid_import_basic_functionality(): void
    {
        $import = new VidImport(2);

        // 테스트 데이터 생성
        $testData = [
            ['EXT-001', 'VENDOR-001', 'PROD-001', 'ITEM-001'],
            ['EXT-002', 'VENDOR-002', 'PROD-002', 'ITEM-002'],
            ['EXT-003', 'VENDOR-003', '', ''], // product_id, item_id가 빈 값
        ];

        $collection = collect($testData)->map(function ($row) {
            return collect($row);
        });

        // 임포트 실행
        $import->collection($collection);

        // 결과 검증
        $this->assertEquals(3, $import->totalProcessed);
        $this->assertEquals(3, $import->totalCreated);
        $this->assertEquals(0, $import->totalSkipped);

        // 데이터베이스 검증
        $this->assertDatabaseHas('product_links', [
            'external_wms_sku_id' => 'EXT-001',
            'vendor_item_id' => 'VENDOR-001',
            'product_id' => 'PROD-001',
            'item_id' => 'ITEM-001',
        ]);

        $this->assertDatabaseHas('product_links', [
            'external_wms_sku_id' => 'EXT-002',
            'vendor_item_id' => 'VENDOR-002',
            'product_id' => 'PROD-002',
            'item_id' => 'ITEM-002',
        ]);

        $this->assertDatabaseHas('product_links', [
            'external_wms_sku_id' => 'EXT-003',
            'vendor_item_id' => 'VENDOR-003',
            'product_id' => null,
            'item_id' => null,
        ]);
    }

    /**
     * 중복 데이터 처리 테스트
     */
    public function test_vid_import_duplicate_handling(): void
    {
        // 기존 데이터 생성
        ProductLink::create([
            'external_wms_sku_id' => 'EXT-001',
            'vendor_item_id' => 'VENDOR-001',
            'product_id' => 'PROD-001',
            'item_id' => 'ITEM-001',
        ]);

        $import = new VidImport(2);

        // 중복 데이터 포함한 테스트 데이터
        $testData = [
            ['EXT-001', 'VENDOR-001', 'PROD-002', 'ITEM-002'], // 중복 (건너뛰기)
            ['EXT-002', 'VENDOR-002', 'PROD-003', 'ITEM-003'], // 새로운 데이터
        ];

        $collection = collect($testData)->map(function ($row) {
            return collect($row);
        });

        // 임포트 실행
        $import->collection($collection);

        // 결과 검증
        $this->assertEquals(2, $import->totalProcessed);
        $this->assertEquals(1, $import->totalCreated);
        $this->assertEquals(1, $import->totalSkipped);

        // 기존 데이터가 변경되지 않았는지 확인
        $existingLink = ProductLink::where([
            'external_wms_sku_id' => 'EXT-001',
            'vendor_item_id' => 'VENDOR-001',
        ])->first();

        $this->assertEquals('PROD-001', $existingLink->product_id); // 원래 값 유지
        $this->assertEquals('ITEM-001', $existingLink->item_id);    // 원래 값 유지
    }

    /**
     * 빈 행 처리 테스트
     */
    public function test_vid_import_empty_row_handling(): void
    {
        $import = new VidImport(2);

        // 빈 행 포함한 테스트 데이터
        $testData = [
            ['EXT-001', 'VENDOR-001', 'PROD-001', 'ITEM-001'],
            ['', '', '', ''], // 완전히 빈 행
            ['EXT-002', '', 'PROD-002', 'ITEM-002'], // 필수 필드 누락
        ];

        $collection = collect($testData)->map(function ($row) {
            return collect($row);
        });

        // 임포트 실행
        $import->collection($collection);

        // 결과 검증
        $this->assertEquals(3, $import->totalProcessed);
        $this->assertEquals(1, $import->totalCreated); // 성공한 것만
        $this->assertEquals(2, $import->totalSkipped); // 실패한 것들

        // 성공한 데이터만 저장되었는지 확인
        $this->assertDatabaseHas('product_links', [
            'external_wms_sku_id' => 'EXT-001',
            'vendor_item_id' => 'VENDOR-001',
        ]);

        $this->assertDatabaseMissing('product_links', [
            'external_wms_sku_id' => 'EXT-002',
            'vendor_item_id' => '',
        ]);
    }

    /**
     * 캐리지 리턴 처리 테스트
     */
    public function test_vid_import_carriage_return_handling(): void
    {
        $import = new VidImport(2);

        // 캐리지 리턴이 포함된 테스트 데이터
        $testData = [
            ["EXT-001\r\n", "VENDOR-001\n", "PROD-001\r", "ITEM-001"],
        ];

        $collection = collect($testData)->map(function ($row) {
            return collect($row);
        });

        // 임포트 실행
        $import->collection($collection);

        // 결과 검증
        $this->assertEquals(1, $import->totalProcessed);
        $this->assertEquals(1, $import->totalCreated);

        // 캐리지 리턴이 제거되었는지 확인
        $this->assertDatabaseHas('product_links', [
            'external_wms_sku_id' => 'EXT-001',
            'vendor_item_id' => 'VENDOR-001',
            'product_id' => 'PROD-001',
            'item_id' => 'ITEM-001',
        ]);
    }
} 