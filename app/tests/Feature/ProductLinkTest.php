<?php

namespace Tests\Feature;

use App\Models\ProductLink;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductLinkTest extends TestCase
{
    use RefreshDatabase;

    /**
     * findByExternalAndVendor 메서드 테스트
     */
    public function test_find_by_external_and_vendor(): void
    {
        // 테스트 데이터 생성
        $productLink = ProductLink::create([
            'external_wms_sku_id' => 'EXT-001',
            'vendor_item_id' => 'VENDOR-001',
            'product_id' => 'PROD-001',
            'item_id' => 'ITEM-001',
        ]);

        // 정상 조회 테스트
        $found = ProductLink::findByExternalAndVendor('EXT-001', 'VENDOR-001');
        $this->assertNotNull($found);
        $this->assertEquals($productLink->id, $found->id);

        // 존재하지 않는 조합 테스트
        $notFound = ProductLink::findByExternalAndVendor('EXT-002', 'VENDOR-001');
        $this->assertNull($notFound);

        // null 값 테스트
        $nullResult = ProductLink::findByExternalAndVendor(null, 'VENDOR-001');
        $this->assertNull($nullResult);

        $nullResult2 = ProductLink::findByExternalAndVendor('EXT-001', null);
        $this->assertNull($nullResult2);
    }

    /**
     * 문자열 표현 테스트
     */
    public function test_string_representation(): void
    {
        $productLink = ProductLink::create([
            'external_wms_sku_id' => 'EXT-001',
            'vendor_item_id' => 'VENDOR-001',
            'product_id' => 'PROD-001',
            'item_id' => 'ITEM-001',
        ]);

        $expected = 'External: EXT-001, Vendor: VENDOR-001, Product: PROD-001, Item: ITEM-001';
        $this->assertEquals($expected, (string)$productLink);
    }

    /**
     * null 값 문자열 표현 테스트
     */
    public function test_string_representation_with_null_values(): void
    {
        $productLink = ProductLink::create([
            'external_wms_sku_id' => null,
            'vendor_item_id' => null,
            'product_id' => null,
            'item_id' => null,
        ]);

        $expected = 'External: , Vendor: , Product: , Item: ';
        $this->assertEquals($expected, (string)$productLink);
    }

    /**
     * products 관계 테스트
     */
    public function test_products_relationship(): void
    {
        $productLink = ProductLink::create([
            'external_wms_sku_id' => 'EXT-001',
            'vendor_item_id' => 'VENDOR-001',
            'product_id' => 'PROD-001',
            'item_id' => 'ITEM-001',
        ]);

        // ProductLink는 products와 1:N 관계를 가짐
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $productLink->products);
        $this->assertCount(0, $productLink->products);
    }
} 