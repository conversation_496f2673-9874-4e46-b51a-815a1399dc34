<?php

use App\Models\User;
use App\Models\Company;
use App\Models\UserAttendance;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

it('사용자 생성 및 회사/근태 관계 확인', function () {
    $company = Company::factory()->create();
    $user = User::factory()->create(['company_id' => $company->id]);
    $attendance = UserAttendance::factory()->create(['user_id' => $user->id]);
    expect($user->company->id)->toBe($company->id);
    expect($user->attendances)->toHaveCount(1);
    expect($user->attendances->first()->id)->toBe($attendance->id);
}); 