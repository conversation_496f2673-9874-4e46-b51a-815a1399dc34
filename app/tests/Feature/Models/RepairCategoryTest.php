<?php

use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\RepairCategory;
use App\Models\RepairFeeRange;

beforeEach(function () {
    // 테스트용 카테고리 데이터 생성
    $this->cate4 = Cate4::factory()->create();
    $this->cate5 = Cate5::factory()->create();
});

describe('RepairCategory 모델 테스트', function () {
    it('수리 카테고리를 생성할 수 있다', function () {
        $repairCategory = RepairCategory::factory()->create([
            'cate4_id' => $this->cate4->id,
            'cate5_id' => $this->cate5->id,
        ]);

        expect($repairCategory)->toBeInstanceOf(RepairCategory::class);
        expect($repairCategory->cate4_id)->toBe($this->cate4->id);
        expect($repairCategory->cate5_id)->toBe($this->cate5->id);
    });

    it('5차 카테고리 없이 수리 카테고리를 생성할 수 있다', function () {
        $repairCategory = RepairCategory::factory()->withoutCate5()->create([
            'cate4_id' => $this->cate4->id,
        ]);

        expect($repairCategory->cate4_id)->toBe($this->cate4->id);
        expect($repairCategory->cate5_id)->toBeNull();
    });

    it('4차 카테고리와의 관계가 올바르게 작동한다', function () {
        $repairCategory = RepairCategory::factory()->create([
            'cate4_id' => $this->cate4->id,
        ]);

        expect($repairCategory->cate4)->toBeInstanceOf(Cate4::class);
        expect($repairCategory->cate4->id)->toBe($this->cate4->id);
    });

    it('5차 카테고리와의 관계가 올바르게 작동한다', function () {
        $repairCategory = RepairCategory::factory()->create([
            'cate5_id' => $this->cate5->id,
        ]);

        expect($repairCategory->cate5)->toBeInstanceOf(Cate5::class);
        expect($repairCategory->cate5->id)->toBe($this->cate5->id);
    });

    it('수리비 범위들과의 관계가 올바르게 작동한다', function () {
        $repairCategory = RepairCategory::factory()->create();
        
        // 수리비 범위 생성
        $feeRanges = RepairFeeRange::factory()->count(3)->create([
            'repair_category_id' => $repairCategory->id,
        ]);

        expect($repairCategory->ranges)->toHaveCount(3);
        expect($repairCategory->ranges->first())->toBeInstanceOf(RepairFeeRange::class);
    });

    it('동일한 카테고리 조합으로 중복 생성이 불가능하다', function () {
        $repairCategory = RepairCategory::factory()->create([
            'cate4_id' => $this->cate4->id,
            'cate5_id' => $this->cate5->id,
        ]);

        // 동일한 조합으로 다시 생성 시도
        expect(function () {
            RepairCategory::factory()->create([
                'cate4_id' => $this->cate4->id,
                'cate5_id' => $this->cate5->id,
            ]);
        })->toThrow(Exception::class);
    });
}); 