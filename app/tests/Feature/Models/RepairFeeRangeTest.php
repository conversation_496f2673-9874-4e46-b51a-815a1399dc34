<?php

use App\Models\RepairCategory;
use App\Models\RepairFeeRange;
use App\Models\RepairFee;

beforeEach(function () {
    $this->repairCategory = RepairCategory::factory()->create();
});

describe('RepairFeeRange 모델 테스트', function () {
    it('수리비 범위를 생성할 수 있다', function () {
        $feeRange = RepairFeeRange::factory()->create([
            'repair_category_id' => $this->repairCategory->id,
            'type' => 'general',
            'model' => 'etc',
            'fee_type' => 'none',
            'fee_unit' => 'won',
            'min_value' => 100000,
            'max_value' => 500000,
        ]);

        expect($feeRange)->toBeInstanceOf(RepairFeeRange::class);
        expect($feeRange->repair_category_id)->toBe($this->repairCategory->id);
        expect($feeRange->type)->toBe('general');
        expect($feeRange->min_value)->toBe(100000.0);
        expect($feeRange->max_value)->toBe(500000.0);
    });

    it('카테고리와의 관계가 올바르게 작동한다', function () {
        $feeRange = RepairFeeRange::factory()->create([
            'repair_category_id' => $this->repairCategory->id,
        ]);

        expect($feeRange->category)->toBeInstanceOf(RepairCategory::class);
        expect($feeRange->category->id)->toBe($this->repairCategory->id);
    });

    it('수리비들과의 관계가 올바르게 작동한다', function () {
        $feeRange = RepairFeeRange::factory()->create([
            'repair_category_id' => $this->repairCategory->id,
        ]);

        // 수리비 생성
        $fees = RepairFee::factory()->count(3)->create([
            'repair_fee_range_id' => $feeRange->id,
        ]);

        expect($feeRange->fees)->toHaveCount(3);
        expect($feeRange->fees->first())->toBeInstanceOf(RepairFee::class);
    });

    it('액세서가 올바르게 작동한다', function () {
        $feeRange = RepairFeeRange::factory()->create([
            'type' => 'general',
            'model' => 'etc',
            'fee_type' => 'none',
            'fee_unit' => 'won',
            'min_value' => 100000,
            'max_value' => 500000,
        ]);

        // 타입 이름 액세서
        expect($feeRange->type_name)->toBe('일반');
        
        // 모델 이름 액세서
        expect($feeRange->model_name)->toBe('그 외 모든 상품');
        
        // 수수료 타입 이름 액세서
        expect($feeRange->fee_type_name)->toBe('공통');
        
        // 수수료 단위 이름 액세서
        expect($feeRange->fee_unit_name)->toBe('원');
        
        // 값 범위 액세서
        expect($feeRange->value_range)->toBe('100,000 ~ 500,000원');
    });

    it('동일한 최소값과 최대값일 때 범위 표시가 올바르다', function () {
        $feeRange = RepairFeeRange::factory()->create([
            'min_value' => 100000,
            'max_value' => 100000,
            'fee_unit' => 'won',
        ]);

        expect($feeRange->value_range)->toBe('100,000원');
    });

    it('팩토리 상태가 올바르게 작동한다', function () {
        // 일반 타입
        $generalRange = RepairFeeRange::factory()->general()->create([
            'repair_category_id' => $this->repairCategory->id,
        ]);
        expect($generalRange->type)->toBe('general');
        expect($generalRange->model)->toBe('etc');

        // 모니터 타입
        $monitorRange = RepairFeeRange::factory()->monitor()->create([
            'repair_category_id' => $this->repairCategory->id,
        ]);
        expect($monitorRange->type)->toBe('monitor');
        expect($monitorRange->model)->toBe('brand');
        expect($monitorRange->fee_type)->toBe('size');
        expect($monitorRange->fee_unit)->toBe('inch');

        // 애플 타입
        $appleRange = RepairFeeRange::factory()->apple()->create([
            'repair_category_id' => $this->repairCategory->id,
        ]);
        expect($appleRange->type)->toBe('apple');
        expect(in_array($appleRange->model, ['맥북', '아이맥']))->toBeTrue();
    });

    it('최소값이 최대값보다 클 수 없다', function () {
        expect(function () {
            RepairFeeRange::factory()->create([
                'repair_category_id' => $this->repairCategory->id,
                'min_value' => 500000,
                'max_value' => 100000,
            ]);
        })->toThrow(Exception::class);
    });

    it('캐스팅이 올바르게 작동한다', function () {
        $feeRange = RepairFeeRange::factory()->create([
            'min_value' => '100000.50',
            'max_value' => '500000.75',
        ]);

        expect($feeRange->min_value)->toBe(100000.50);
        expect($feeRange->max_value)->toBe(500000.75);
        expect(is_float($feeRange->min_value))->toBeTrue();
        expect(is_float($feeRange->max_value))->toBeTrue();
    });
}); 