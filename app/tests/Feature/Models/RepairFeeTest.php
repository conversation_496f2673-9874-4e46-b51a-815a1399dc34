<?php

use App\Models\RepairFee;
use App\Models\RepairFeeRange;

beforeEach(function () {
    $this->feeRange = RepairFeeRange::factory()->create();
});

describe('RepairFee 모델 테스트', function () {
    it('수리비를 생성할 수 있다', function () {
        $fee = RepairFee::factory()->create([
            'repair_fee_range_id' => $this->feeRange->id,
            'repair_type' => 'CHECK',
            'amount' => 5000,
        ]);

        expect($fee)->toBeInstanceOf(RepairFee::class);
        expect($fee->repair_fee_range_id)->toBe($this->feeRange->id);
        expect($fee->repair_type)->toBe('CHECK');
        expect($fee->amount)->toBe(5000);
    });

    it('범위와의 관계가 올바르게 작동한다', function () {
        $fee = RepairFee::factory()->create([
            'repair_fee_range_id' => $this->feeRange->id,
        ]);

        expect($fee->range)->toBeInstanceOf(RepairFeeRange::class);
        expect($fee->range->id)->toBe($this->feeRange->id);
    });

    it('액세서가 올바르게 작동한다', function () {
        $fee = RepairFee::factory()->create([
            'repair_type' => 'CHECK',
            'amount' => 5000,
        ]);

        // 수리 타입 이름 액세서
        expect($fee->repair_type_name)->toBe('점검');
        
        // 금액 포맷팅 액세서
        expect($fee->formatted_amount)->toBe('5,000원');
        
        // 타입 확인 액세서들
        expect($fee->is_check)->toBeTrue();
        expect($fee->is_repair)->toBeFalse();
        expect($fee->is_os_install)->toBeFalse();
    });

    it('수리 타입별 액세서가 올바르게 작동한다', function () {
        // 점검 타입
        $checkFee = RepairFee::factory()->check()->create();
        expect($checkFee->is_check)->toBeTrue();
        expect($checkFee->is_repair)->toBeFalse();
        expect($checkFee->is_os_install)->toBeFalse();

        // 수리 타입
        $repairFee = RepairFee::factory()->repair()->create();
        expect($repairFee->is_check)->toBeFalse();
        expect($repairFee->is_repair)->toBeTrue();
        expect($repairFee->is_os_install)->toBeFalse();

        // OS 재설치 타입
        $osFee = RepairFee::factory()->osInstall()->create();
        expect($osFee->is_check)->toBeFalse();
        expect($osFee->is_repair)->toBeFalse();
        expect($osFee->is_os_install)->toBeTrue();
    });

    it('팩토리 상태가 올바르게 작동한다', function () {
        // 점검 타입
        $checkFee = RepairFee::factory()->check()->create();
        expect($checkFee->repair_type)->toBe('CHECK');
        expect($checkFee->amount)->toBeGreaterThanOrEqual(5000);
        expect($checkFee->amount)->toBeLessThanOrEqual(10000);

        // 수리 타입
        $repairFee = RepairFee::factory()->repair()->create();
        expect($repairFee->repair_type)->toBe('REPAIR');
        expect($repairFee->amount)->toBeGreaterThanOrEqual(10000);
        expect($repairFee->amount)->toBeLessThanOrEqual(50000);

        // OS 재설치 타입
        $osFee = RepairFee::factory()->osInstall()->create();
        expect($osFee->repair_type)->toBe('OSinstall');
        expect($osFee->amount)->toBeGreaterThanOrEqual(15000);
        expect($osFee->amount)->toBeLessThanOrEqual(30000);
    });

    it('캐스팅이 올바르게 작동한다', function () {
        $fee = RepairFee::factory()->create([
            'amount' => '5000',
        ]);

        expect($fee->amount)->toBe(5000);
        expect(is_int($fee->amount))->toBeTrue();
    });

    it('금액이 0 이상이어야 한다', function () {
        expect(function () {
            RepairFee::factory()->create([
                'repair_fee_range_id' => $this->feeRange->id,
                'amount' => -1000,
            ]);
        })->toThrow(Exception::class);
    });

    it('다양한 수리 타입의 이름이 올바르다', function () {
        $types = [
            'CHECK' => '점검',
            'REPAIR' => '수리',
            'OSinstall' => 'OS reinstall',
            'COMPLETE' => 'COMPLETE',
            'DOA' => 'DOA',
            'BEST' => 'BEST',
            'GOOD' => 'GOOD',
            'NORMAL' => 'NORMAL',
        ];

        foreach ($types as $type => $expectedName) {
            $fee = RepairFee::factory()->create(['repair_type' => $type]);
            expect($fee->repair_type_name)->toBe($expectedName);
        }
    });

    it('큰 금액의 포맷팅이 올바르다', function () {
        $fee = RepairFee::factory()->create([
            'amount' => 1234567,
        ]);

        expect($fee->formatted_amount)->toBe('1,234,567원');
    });
}); 