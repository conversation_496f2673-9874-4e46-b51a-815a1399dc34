<?php

namespace Tests\Feature;

use App\Models\ProductBarcode;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductBarcodeTest extends TestCase
{
    use RefreshDatabase;

    /**
     * barcode + wms_sku_id 조합으로 unique 처리 테스트
     */
    public function test_barcode_and_wms_sku_id_unique_combination(): void
    {
        // 첫 번째 레코드 생성
        $barcode1 = ProductBarcode::create([
            'barcode' => '1234567890123',
            'wms_sku_id' => 'SKU-001',
            'external_wms_sku_id' => 'EXT-001'
        ]);

        // 같은 barcode + wms_sku_id 조합으로 다시 생성 시도 (실패해야 함)
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        ProductBarcode::create([
            'barcode' => '1234567890123',
            'wms_sku_id' => 'SKU-001',
            'external_wms_sku_id' => 'EXT-002'
        ]);
    }

    /**
     * 다른 wms_sku_id로 같은 barcode 생성 가능 테스트
     */
    public function test_same_barcode_with_different_wms_sku_id(): void
    {
        // 첫 번째 레코드
        ProductBarcode::create([
            'barcode' => '1234567890123',
            'wms_sku_id' => 'SKU-001',
            'external_wms_sku_id' => 'EXT-001'
        ]);

        // 같은 barcode이지만 다른 wms_sku_id로 생성 (성공해야 함)
        $barcode2 = ProductBarcode::create([
            'barcode' => '1234567890123',
            'wms_sku_id' => 'SKU-002',
            'external_wms_sku_id' => 'EXT-002'
        ]);

        $this->assertDatabaseHas('product_barcodes', [
            'barcode' => '1234567890123',
            'wms_sku_id' => 'SKU-001',
            'external_wms_sku_id' => 'EXT-001'
        ]);

        $this->assertDatabaseHas('product_barcodes', [
            'barcode' => '1234567890123',
            'wms_sku_id' => 'SKU-002',
            'external_wms_sku_id' => 'EXT-002'
        ]);
    }

    /**
     * null 값을 '-'로 처리하는 테스트
     */
    public function test_null_values_are_stored_as_dash(): void
    {
        $barcode = ProductBarcode::create([
            'barcode' => '1234567890123',
            'wms_sku_id' => null,
            'external_wms_sku_id' => null
        ]);

        // 데이터베이스에는 '-'로 저장되어야 함
        $this->assertDatabaseHas('product_barcodes', [
            'barcode' => '1234567890123',
            'wms_sku_id' => '-',
            'external_wms_sku_id' => '-'
        ]);

        // 모델에서 접근할 때는 null로 반환되어야 함
        $this->assertNull($barcode->wms_sku_id);
        $this->assertNull($barcode->external_wms_sku_id);
    }

    /**
     * firstOrCreate 동작 테스트
     */
    public function test_first_or_create_behavior(): void
    {
        // 첫 번째 생성
        $barcode1 = ProductBarcode::firstOrCreate(
            [
                'barcode' => '1234567890123',
                'wms_sku_id' => 'SKU-001',
            ],
            [
                'external_wms_sku_id' => 'EXT-001',
            ]
        );

        // 같은 조건으로 다시 호출 (기존 레코드 반환)
        $barcode2 = ProductBarcode::firstOrCreate(
            [
                'barcode' => '1234567890123',
                'wms_sku_id' => 'SKU-001',
            ],
            [
                'external_wms_sku_id' => 'EXT-002', // 이 값은 무시됨
            ]
        );

        $this->assertEquals($barcode1->id, $barcode2->id);
        $this->assertEquals('EXT-001', $barcode2->external_wms_sku_id);
    }

    /**
     * 문자열 표현 테스트
     */
    public function test_string_representation(): void
    {
        $barcode = ProductBarcode::create([
            'barcode' => '1234567890123',
            'wms_sku_id' => 'SKU-001',
            'external_wms_sku_id' => 'EXT-001'
        ]);

        $expected = 'Barcode: 1234567890123, WMS: SKU-001, External: EXT-001';
        $this->assertEquals($expected, (string)$barcode);
    }

    /**
     * null 값 문자열 표현 테스트
     */
    public function test_string_representation_with_null_values(): void
    {
        $barcode = ProductBarcode::create([
            'barcode' => '1234567890123',
            'wms_sku_id' => null,
            'external_wms_sku_id' => null
        ]);

        $expected = 'Barcode: 1234567890123, WMS: -, External: -';
        $this->assertEquals($expected, (string)$barcode);
    }
} 