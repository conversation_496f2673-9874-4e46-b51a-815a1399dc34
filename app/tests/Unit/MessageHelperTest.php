<?php

namespace Tests\Unit;

use App\Helpers\MessageHelper;
use PHPUnit\Framework\TestCase;

class MessageHelperTest extends TestCase
{
    public function test_format_method_works_correctly()
    {
        $result = MessageHelper::format('해당 %s [%s]이 존재하지 않습니다.', '상품', 'TEST123');
        $this->assertEquals('해당 상품 [TEST123]이 존재하지 않습니다.', $result);
    }

    public function test_product_not_found_message()
    {
        $result = MessageHelper::productNotFound('TEST123');
        $this->assertEquals('해당 상품 [TEST123]이 존재하지 않습니다.', $result);
    }

    public function test_product_already_carried_out_message()
    {
        $result = MessageHelper::productAlreadyCarriedOut('TEST123');
        $this->assertEquals('해당 상품 [TEST123]은 이미 외주 반출 처리된 상품입니다.', $result);
    }

    public function test_carryout_not_found_message()
    {
        $result = MessageHelper::carryoutNotFound('123');
        $this->assertEquals('해당 외주 반출 정보 [123]가 존재하지 않습니다.', $result);
    }

    public function test_operation_success_message()
    {
        $result = MessageHelper::operationSuccess('외주 반출');
        $this->assertEquals('외주 반출 처리가 성공적으로 완료되었습니다.', $result);
    }

    public function test_custom_message_with_replacements()
    {
        $template = '사용자 {user}가 {action}을 수행했습니다.';
        $replacements = ['user' => '홍길동', 'action' => '외주 반출'];
        
        $result = MessageHelper::custom($template, $replacements);
        $this->assertEquals('사용자 홍길동가 외주 반출을 수행했습니다.', $result);
    }

    public function test_required_field_missing_message()
    {
        $result = MessageHelper::requiredFieldMissing('qaid');
        $this->assertEquals('필수 필드가 누락되었습니다: qaid', $result);
    }

    public function test_resource_not_found_generic_message()
    {
        $result = MessageHelper::resourceNotFound('창고', 'WH001');
        $this->assertEquals('해당 창고 [WH001]이 존재하지 않습니다.', $result);
    }

    public function test_already_processed_message()
    {
        $result = MessageHelper::alreadyProcessed('팰릿', 'PL001');
        $this->assertEquals('해당 팰릿 [PL001]은 이미 처리된 항목입니다.', $result);
    }
}