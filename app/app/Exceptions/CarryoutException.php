<?php

namespace App\Exceptions;

/**
 * 외주 관련 예외 클래스
 *
 * 외주 반출/반입 처리 중 발생하는 비즈니스 로직 예외입니다.
 */
class CarryoutException extends BusinessException
{
    protected string $module = 'carryout';

    /**
     * 외주 반출되지 않은 상품에 대한 반입 시도
     */
    public static function notCarriedOut(string $qaid): self
    {
        $message = "해당 상품 [QAID: {$qaid}]은 외주 반출 상태가 아닙니다.";
        return new self($message, [
            'qaid' => $qaid,
            'type' => 'not_carried_out'
        ]);
    }

    /**
     * 이미 외주 반출된 상품에 대한 중복 처리 시도
     */
    public static function alreadyExported(string $qaid): self
    {
        $message = "해당 상품 [QAID: {$qaid}]은 이미 외주 반출 처리된 상품입니다.";
        return new self($message, [
            'qaid' => $qaid,
            'type' => 'already_carried_out'
        ]);
    }

    /**
     * 이미 반입 완료된 상품에 대한 중복 반입 시도
     */
    public static function alreadyImported(string $qaid): self
    {
        $message = "해당 상품 [QAID: {$qaid}]은 이미 반입 완료된 상품입니다.";
        return new self($message, [
            'qaid' => $qaid,
            'type' => 'already_imported'
        ]);
    }

    /**
     * 외주 반출이 취소된 경우
     */
    public static function alreadyCanceled(string $qaid): self
    {
        $message = "해당 상품 [QAID: {$qaid}]은 외주 취소된 상품입니다.";
        return new self($message, [
            'type' => 'already_canceled'
        ]);
    }

    /**
     * 외주 정보를 찾을 수 없을 때
     */
    public static function carryoutNotFound(): self
    {
        $message = "외주 반출/반입 정보가 존재하지 않습니다.";
        return new self($message, [
            'type' => 'carryout_not_found'
        ]);
    }

    /**
     * 상품이 외주 반출 가능한 상태가 아닐 때
     */
    public static function isNotExportable(string $qaid, string $currentStatus): self
    {
        $message = "해당 상품 [QAID: {$qaid}]은 외주 반출 가능한 상태가 아닙니다. (현재 상태: {$currentStatus})";
        return new self($message, [
            'qaid' => $qaid,
            'current_status' => $currentStatus,
            'type' => 'invalid_product_status'
        ]);
    }

    /**
     * 상품이 외주 반입 가능한 상태가 아닐 때
     */
    public static function isNotImportable(string $qaid, string $currentStatus): self
    {
        $message = "해당 상품 [QAID: {$qaid}]은 반입 가능한 상태가 아닙니다. (현재 상태: {$currentStatus})";
        return new self($message, [
            'qaid' => $qaid,
            'current_status' => $currentStatus,
            'type' => 'invalid_product_status'
        ]);
    }

    /**
     * 잘못된 데이터 입력
     */
    public static function invalidData(string $message): self
    {
        return new self($message, [
            'type' => 'invalid_data'
        ]);
    }

    /**
     * 잘못된 작업 시도
     */
    public static function invalidOperation(string $message): self
    {
        return new self($message, [
            'type' => 'invalid_operation'
        ]);
    }
}
