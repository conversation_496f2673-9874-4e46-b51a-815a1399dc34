<?php

namespace App\Exceptions;

class ProductException extends BusinessException
{
    protected string $module = 'product';

    public static function alreadyExists(string $qaid): self
    {
        $message = "이미 존재하는 [QAID: {$qaid}]입니다.";
        return new self($message, [
            'qaid' => $qaid,
            'type' => 'exists_product'
        ]);
    }

    /**
     * 존재하지 않는 상품일 경우
     */
    public static function notFound(string $qaid): self
    {
        $message = "해당 상품 [QAID: {$qaid}]은(는) 존재하지 않습니다.";
        return new self($message, [
            'qaid' => $qaid,
            'type' => 'not_product'
        ]);
    }

    /**
     * 미입고 된 상품일 경우<br>
     * 입고 리스트(엑셀)에는 있지만 입고 검수 과정 중 실제로 입고 되지 않은 경우
     */
    public static function undelivered(string $qaid): self
    {
        $message = "해당 상품 [QAID: {$qaid}]은(는) 미입고된 상품 입니다.";
        return new self($message, [
            'qaid' => $qaid,
            'type' => 'undelivered_product'
        ]);
    }

    /**
     * 중복 등록된 상품일 경우
     */
    public static function duplicated(string $qaid): self
    {
        $message = "해당 상품 [QAID: {$qaid}]은(는) 중복 등록된 상품 입니다.";
        return new self($message, [
            'qaid' => $qaid,
            'type' => 'duplicated_product'
        ]);
    }
}
