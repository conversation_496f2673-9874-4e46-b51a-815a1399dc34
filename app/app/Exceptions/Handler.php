<?php

namespace App\Exceptions;

use App\Services\SimpleLogService;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            // WMS 예외에 대한 로깅 처리
            if ($e instanceof WmsException) {
                $this->logWmsException($e);
            }
        });

        // WMS 예외에 대한 렌더링 처리
        $this->renderable(function (WmsException $e, Request $request) {
            if ($request->expectsJson()) {
                return $this->renderWmsExceptionAsJson($e);
            }
        });
    }

    /**
     * WMS 예외를 로깅합니다.
     */
    protected function logWmsException(WmsException $exception): void
    {
        $module = $exception->getModule();
        $context = $exception->getContext();
        
        // 예외 타입에 따라 로그 레벨 결정
        if ($exception instanceof ValidationException || $exception instanceof BusinessException) {
            // 비즈니스 로직 오류는 warning 레벨
            SimpleLogService::warning($module, $exception->getMessage(), $context, $exception);
        } elseif ($exception instanceof ResourceNotFoundException) {
            // 리소스 없음은 info 레벨
            SimpleLogService::info($module, $exception->getMessage(), $context);
        } else {
            // 기타 시스템 오류는 error 레벨
            SimpleLogService::error($module, $exception->getMessage(), $context, $exception);
        }
    }

    /**
     * WMS 예외를 JSON 응답으로 렌더링합니다.
     */
    protected function renderWmsExceptionAsJson(WmsException $exception): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $exception->getMessage(),
            'module' => $exception->getModule(),
        ];

        // 개발 환경에서는 추가 정보 포함
        if (config('app.debug')) {
            $response['context'] = $exception->getContext();
            $response['trace'] = $exception->getTraceAsString();
        }

        return response()->json($response, $exception->getCode() ?: 500);
    }
}
