<?php

namespace App\Exceptions;

/**
 * 창고 관련 예외 클래스
 * 
 * 창고 관리 중 발생하는 비즈니스 로직 예외입니다.
 */
class WarehouseException extends BusinessException
{
    protected string $module = 'warehouse';

    /**
     * 창고를 찾을 수 없을 때
     */
    public static function warehouseNotFound(string $warehouseId): self
    {
        $message = "해당 창고 [{$warehouseId}]가 존재하지 않습니다.";
        return new self($message, [
            'warehouse_id' => $warehouseId,
            'type' => 'warehouse_not_found'
        ]);
    }

    /**
     * 창고 용량 부족 시
     */
    public static function warehouseFull(string $warehouseId): self
    {
        $message = "해당 창고 [{$warehouseId}]의 용량이 부족합니다.";
        return new self($message, [
            'warehouse_id' => $warehouseId,
            'type' => 'warehouse_full'
        ]);
    }

    /**
     * 팰릿을 찾을 수 없을 때
     */
    public static function palletNotFound(string $palletId): self
    {
        $message = "해당 팰릿 [{$palletId}]이 존재하지 않습니다.";
        return new self($message, [
            'pallet_id' => $palletId,
            'type' => 'pallet_not_found'
        ]);
    }
}