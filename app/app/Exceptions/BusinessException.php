<?php

namespace App\Exceptions;

/**
 * 비즈니스 로직 예외 클래스
 * 
 * 비즈니스 규칙 위반이나 로직 오류 시 발생하는 예외입니다.
 * HTTP 상태 코드 400 (Bad Request)와 함께 사용됩니다.
 */
class BusinessException extends WmsException
{
    protected string $module = 'business';

    public function __construct(string $message, array $context = [], ?\Throwable $previous = null)
    {
        parent::__construct($message, 400, $previous, $context);
    }

    /**
     * 중복 처리 시도 시 사용하는 정적 생성자
     */
    public static function forDuplicate(string $resource, string $identifier): self
    {
        $message = "해당 {$resource} [{$identifier}]은 이미 처리된 항목입니다.";
        return new self($message, [
            'resource' => $resource,
            'identifier' => $identifier,
            'type' => 'duplicate'
        ]);
    }

    /**
     * 잘못된 상태에서의 작업 시도 시 사용하는 정적 생성자
     */
    public static function forInvalidStatus(string $resource, string $identifier, string $currentStatus, string $expectedStatus): self
    {
        $message = "해당 {$resource} [{$identifier}]의 상태가 올바르지 않습니다. 현재: {$currentStatus}, 필요: {$expectedStatus}";
        return new self($message, [
            'resource' => $resource,
            'identifier' => $identifier,
            'current_status' => $currentStatus,
            'expected_status' => $expectedStatus,
            'type' => 'invalid_status'
        ]);
    }

    /**
     * 권한 부족 시 사용하는 정적 생성자
     */
    public static function forPermission(string $operation): self
    {
        $message = "해당 작업 [{$operation}]에 대한 권한이 없습니다.";
        return new self($message, [
            'operation' => $operation,
            'type' => 'permission_denied'
        ], null);
    }
}