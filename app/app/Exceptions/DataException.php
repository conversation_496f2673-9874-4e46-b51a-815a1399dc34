<?php

namespace App\Exceptions;

/**
 * 데이터베이스 관련 예외 클래스
 * 
 * 데이터베이스 작업 중 발생하는 예외입니다.
 * HTTP 상태 코드 500 (Internal Server Error)와 함께 사용됩니다.
 */
class DataException extends WmsException
{
    protected string $module = 'data';

    public function __construct(string $message, array $context = [], ?\Throwable $previous = null)
    {
        parent::__construct($message, 500, $previous, $context);
    }

    /**
     * 데이터베이스 연결 실패 시 사용하는 정적 생성자
     */
    public static function forConnection(string $connection = 'default'): self
    {
        $message = "데이터베이스 연결에 실패했습니다.";
        return new self($message, [
            'connection' => $connection,
            'type' => 'connection_failed'
        ]);
    }

    /**
     * 트랜잭션 실패 시 사용하는 정적 생성자
     */
    public static function forTransaction(string $operation, ?\Throwable $previous = null): self
    {
        $message = "트랜잭션 처리 중 오류가 발생했습니다: {$operation}";
        return new self($message, [
            'operation' => $operation,
            'type' => 'transaction_failed'
        ], $previous);
    }

    /**
     * 데이터 무결성 위반 시 사용하는 정적 생성자
     */
    public static function forIntegrity(string $constraint, ?\Throwable $previous = null): self
    {
        $message = "데이터 무결성 제약 조건 위반: {$constraint}";
        return new self($message, [
            'constraint' => $constraint,
            'type' => 'integrity_violation'
        ], $previous);
    }
}