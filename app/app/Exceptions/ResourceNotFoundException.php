<?php

namespace App\Exceptions;

/**
 * 리소스 없음 예외 클래스
 * 
 * 요청한 리소스를 찾을 수 없을 때 발생하는 예외입니다.
 * HTTP 상태 코드 404 (Not Found)와 함께 사용됩니다.
 */
class ResourceNotFoundException extends WmsException
{
    protected string $module = 'resource';

    public function __construct(string $message, array $context = [], ?\Throwable $previous = null)
    {
        parent::__construct($message, 404, $previous, $context);
    }

    /**
     * 특정 리소스를 찾을 수 없을 때 사용하는 정적 생성자
     */
    public static function forResource(string $resource, string $identifier): self
    {
        $message = "해당 {$resource} [{$identifier}]이 존재하지 않습니다.";
        return new self($message, [
            'resource' => $resource,
            'identifier' => $identifier
        ]);
    }

    /**
     * ID로 리소스를 찾을 수 없을 때 사용하는 정적 생성자
     */
    public static function forId(string $resource, int $id): self
    {
        return self::forResource($resource, (string) $id);
    }
}