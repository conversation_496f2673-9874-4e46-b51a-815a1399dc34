<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Slack\SlackMessage;
use Illuminate\Notifications\Notification;

class SlackNotification extends Notification
{
    use Queueable;

    protected string $level;
    protected string $title;
    protected array $context;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $level, string $title, array $context = [])
    {
        $this->level = $level;
        $this->title = $title;
        $this->context = $context;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['slack'];
    }

    /**
     * 슬랙 메시지 구성 (Laravel 11 SectionBlock 방식)
     */
    public function toSlack(object $notifiable): SlackMessage
    {
        $env = app()->environment();
        $envLabel = strtoupper($env);
        $emoji = $this->getEmoji();

        // UTF-8 인코딩 문제 방지를 위해 데이터 정리
        $safeTitle = $this->sanitizeUtf8($this->title);

        return (new SlackMessage)
            ->headerBlock("{$emoji} [{$envLabel}] {$safeTitle}")
            ->dividerBlock()
            ->sectionBlock(function ($section) {
                $section->field("- 레벨: {$this->level}")->markdown();
                $section->field("- 시간: " . now()->format('Y-m-d H:i:s'))->markdown();
                $section->field("- 환경: " . app()->environment())->markdown();
            })
            ->sectionBlock(function ($section) {
                $details = $this->buildDetails();
                if (!empty($details)) {
                    $section->text("*상세 정보:*\n" . implode("\n", $details))->markdown();
                }
            });
    }

    /**
     * UTF-8 문자열 정리
     */
    private function sanitizeUtf8(string $string): string
    {
        // UTF-8이 아닌 문자 제거 또는 변환
        $string = mb_convert_encoding($string, 'UTF-8', 'UTF-8');

        // 잘못된 UTF-8 시퀀스 제거
        $string = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $string);

        // 제어 문자 제거
        $string = preg_replace('/[\x{200B}-\x{200D}\x{FEFF}]/u', '', $string);

        return $string;
    }

    /**
     * 상세 정보 구성 (마크다운 형식)
     */
    protected function buildDetails(): array
    {
        $details = [];

        // 사용자 정보 추가
        if (!empty($this->context['user']['id'])) {
            $userName = $this->sanitizeUtf8($this->context['user']['name'] ?? '');
            $userUsername = $this->sanitizeUtf8($this->context['user']['username'] ?? '');
            $details[] = "• *사용자:* {$userName}(아이디: `{$userUsername}`, 인덱스: `{$this->context['user']['id']}`)";
        }

        // IP 정보 추가
        if (isset($this->context['ip'])) {
            $details[] = "• *IP:* `{$this->context['ip']}`";
        }

        // URL 정보 추가
        if (isset($this->context['url'])) {
            $details[] = "• *URL:* <{$this->context['url']}|링크>";
        }

        // 경로 정보 추가
        if (isset($this->context['path'])) {
            $details[] = "• *라우트 경로:* `{$this->context['path']}`";
        }

        // 메소드 정보 추가
        if (isset($this->context['method'])) {
            $details[] = "• *Method:* `{$this->context['method']}`";
        }

        // 오류 정보 추가
        if (isset($this->context['exception']['message'])) {
            $errorMessage = $this->sanitizeUtf8($this->context['exception']['message']);
            $details[] = "• *오류:* {$errorMessage}";

            // 추가 오류 정보
            if (isset($this->context['exception']['file'])) {
                $details[] = "• *파일:* `{$this->context['exception']['file']}`";
            }
        }

        return $details;
    }

    /**
     * 레벨에 따른 색상 반환
     */
    protected function getLevelColor(): string
    {
        return [
            'emergency' => '#ff0000', // 빨간색
            'alert'     => '#ff0000', // 빨간색
            'critical'  => '#ff6b6b', // 진한 빨간색
            'error'     => '#ff6b6b', // 진한 빨간색
            'warning'   => '#ffa500', // 주황색
            'notice'    => '#ffff00', // 노란색
            'info'      => '#00bfff', // 파란색
            'debug'     => '#808080', // 회색
        ][strtolower($this->level)] ?? '#00bfff';
    }

    /**
     * 레벨에 따른 이모지 반환
     */
    protected function getEmoji(): string
    {
        return [
            'emergency' => '🚨',
            'alert'     => '🚨',
            'critical'  => '❌',
            'error'     => '❌',
            'warning'   => '⚠️',
            'notice'    => '📢',
            'info'      => 'ℹ️',
            'debug'     => '🔍',
        ][strtolower($this->level)] ?? 'ℹ️';
    }
}
