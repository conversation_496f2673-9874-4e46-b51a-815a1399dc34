<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @see \App\Services\DynamicWorkStatusService
 * 
 * @method static \App\Models\WorkStatus getOrCreateStatus(string $categoryCode, string $actionCode, array $context = [])
 * @method static array getStatusesByCondition(string $categoryCode, string $actionCode, array $context = [])
 * @method static array getGeneratedStatusStats(string $categoryCode)
 * @method static array convertLegacyConstants()
 * @method static void clearCache()
 * @method static array getGenerationStats()
 * @method static array getRecentlyGeneratedStatuses(int $limit = 10)
 * @method static int getStatusCountByTemplate(int $templateId)
 * @method static array getUnusedTemplates()
 * @method static array getFrequentlyUsedStatuses(int $limit = 10)
 */
class DynamicWorkStatus extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return \App\Services\DynamicWorkStatusService::class;
    }
} 