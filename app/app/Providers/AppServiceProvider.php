<?php

namespace App\Providers;

use App\Repositories\Interfaces\WorkCategoryRepositoryInterface;
use App\Repositories\WorkCategoryRepository;
use App\Repositories\Interfaces\WorkActionRepositoryInterface;
use App\Repositories\WorkActionRepository;
use App\Repositories\Interfaces\WorkStatusTemplateRepositoryInterface;
use App\Repositories\WorkStatusTemplateRepository;
use App\Repositories\Interfaces\WorkStatusRepositoryInterface;
use App\Repositories\WorkStatusRepository;
use App\Services\DynamicWorkStatusService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Repository 인터페이스와 구현체 바인딩
        $this->app->bind(WorkCategoryRepositoryInterface::class, WorkCategoryRepository::class);
        $this->app->bind(WorkActionRepositoryInterface::class, WorkActionRepository::class);
        $this->app->bind(WorkStatusTemplateRepositoryInterface::class, WorkStatusTemplateRepository::class);
        $this->app->bind(WorkStatusRepositoryInterface::class, WorkStatusRepository::class);

        // DynamicWorkStatusService를 싱글톤으로 등록 (Facade 지원)
        $this->app->singleton(DynamicWorkStatusService::class, function ($app) {
            return new DynamicWorkStatusService(
                $app->make(WorkCategoryRepositoryInterface::class),
                $app->make(WorkActionRepositoryInterface::class),
                $app->make(WorkStatusTemplateRepositoryInterface::class),
                $app->make(WorkStatusRepositoryInterface::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
