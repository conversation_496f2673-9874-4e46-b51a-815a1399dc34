<?php

namespace App\DTOs;

/**
 * User 모델에서 사용할 검색 필터 DTO
 */
class UserFilterDto
{
    public ?string $keyword;
    public ?string $role;
    public ?int $status;
    public ?int $pageSize;
    // 추가 필드...

    /**
     * Class Constructor
     *
     * @param string|null $keyword 검색어(기본값 : null)
     * @param string|null $role 회원의 권한(기본값 : null)
     * @param int|null $status 회원의 현재 상태(기본값 : null)
     * // Additional fields...
     */
    public function __construct(
        ?string $keyword = null,
        ?string $role = null,
        ?int $status = null,
        ?int $pageSize = 15
        // 추가 필드...
    ) {
        $this->keyword = $keyword;
        $this->role = $role;
        $this->status = $status;
        $this->pageSize = $pageSize;
        // 추가 필드...
    }
}
