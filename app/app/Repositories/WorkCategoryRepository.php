<?php

namespace App\Repositories;

use App\Models\WorkCategory;
use App\Repositories\Interfaces\WorkCategoryRepositoryInterface;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class WorkCategoryRepository implements WorkCategoryRepositoryInterface
{
    /**
     * 모든 활성 카테고리 조회
     */
    public function getAllActive(): Collection
    {
        return WorkCategory::where('is_active', true)
            ->orderBy('name')
            ->get();
    }

    /**
     * 모든 카테고리 조회 (비활성 포함)
     */
    public function getAll(): Collection
    {
        return WorkCategory::orderBy('name')->get();
    }

    /**
     * ID로 카테고리 조회
     */
    public function findById(int $id): ?WorkCategory
    {
        return WorkCategory::find($id);
    }

    /**
     * 코드로 카테고리 조회
     */
    public function findByCode(string $code): ?WorkCategory
    {
        return WorkCategory::where('code', $code)->first();
    }

    /**
     * 활성 상태 코드로 카테고리 조회
     */
    public function findActiveByCode(string $code): ?WorkCategory
    {
        return WorkCategory::where('code', $code)
            ->where('is_active', true)
            ->first();
    }

    /**
     * 카테고리 생성
     */
    public function create(array $data): WorkCategory
    {
        return WorkCategory::create($data);
    }

    /**
     * 카테고리 수정
     */
    public function update(int $id, array $data): WorkCategory
    {
        $category = WorkCategory::findOrFail($id);
        $category->update($data);
        return $category;
    }

    /**
     * 카테고리 삭제
     * @throws Exception
     */
    public function delete(int $id): bool
    {
        $category = WorkCategory::findOrFail($id);

        if (!$category->canDelete()) {
            throw new Exception('관련 데이터가 있어 삭제할 수 없습니다.');
        }

        return $category->delete();
    }

    /**
     * 카테고리 코드 중복 체크
     */
    public function existsByCode(string $code, ?int $excludeId = null): bool
    {
        $query = WorkCategory::where('code', $code);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * 관계 포함 카테고리 목록 조회
     */
    public function getAllWithRelations(): Collection
    {
        return WorkCategory::with([
            'actions' => function ($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            },
            'statusTemplates' => function ($query) {
                $query->where('is_active', true);
            }
        ])
        ->where('is_active', true)
        ->orderBy('name')
        ->get();
    }

    /**
     * 통계 정보 포함 카테고리 목록 조회
     */
    public function getAllWithCounts(): Collection
    {
        return WorkCategory::where('is_active', true)
            ->withCount([
                'actions',
                'statusTemplates',
                'workStatuses'
            ])
            ->orderBy('name')
            ->get();
    }

    /**
     * 카테고리 비활성화 가능 여부 확인
     */
    public function canDeactivate(int $categoryId): bool
    {
        $category = WorkCategory::find($categoryId);
        if (!$category) {
            return false;
        }

        // 관련된 활성 액션이 있는지 확인
        $hasActiveActions = $category->activeActions()->exists();

        // 관련된 활성 템플릿이 있는지 확인
        $hasActiveTemplates = $category->statusTemplates()
            ->where('is_active', true)
            ->exists();

        return !$hasActiveActions && !$hasActiveTemplates;
    }

    /**
     * 카테고리 삭제 가능 여부 확인
     */
    public function canDelete(int $categoryId): bool
    {
        $category = WorkCategory::find($categoryId);
        if (!$category) {
            return false;
        }

        // 관련된 WorkStatus가 있는지 확인
        $hasWorkStatuses = $category->workStatuses()->exists();

        // 관련된 액션이 있는지 확인
        $hasActions = $category->actions()->exists();

        return !$hasWorkStatuses && !$hasActions;
    }

    /**
     * 카테고리별 액션 개수 조회
     */
    public function getActionsCount(int $categoryId): int
    {
        return WorkCategory::find($categoryId)?->actions()->count() ?? 0;
    }

    /**
     * 카테고리별 활성 액션 개수 조회
     */
    public function getActiveActionsCount(int $categoryId): int
    {
        return WorkCategory::find($categoryId)?->activeActions()->count() ?? 0;
    }

    /**
     * 카테고리별 템플릿 개수 조회
     */
    public function getTemplatesCount(int $categoryId): int
    {
        return WorkCategory::find($categoryId)?->statusTemplates()->count() ?? 0;
    }
}
