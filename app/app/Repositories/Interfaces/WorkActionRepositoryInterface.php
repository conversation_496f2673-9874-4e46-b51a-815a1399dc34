<?php

namespace App\Repositories\Interfaces;

use App\Models\WorkAction;
use Illuminate\Database\Eloquent\Collection;

interface WorkActionRepositoryInterface
{
    /**
     * 모든 활성 액션 조회
     */
    public function getAllActive(): Collection;

    /**
     * 모든 액션 조회 (비활성 포함)
     */
    public function getAll(): Collection;

    /**
     * ID로 액션 조회
     */
    public function findById(int $id): ?WorkAction;

    /**
     * 카테고리별 활성 액션 조회
     */
    public function findActiveByCategoryId(int $categoryId): Collection;

    /**
     * 카테고리와 코드로 액션 조회
     */
    public function findByCategoryAndCode(int $categoryId, string $code): ?WorkAction;

    /**
     * 활성 상태 카테고리와 코드로 액션 조회
     */
    public function findActiveByCategoryAndCode(int $categoryId, string $code): ?WorkAction;

    /**
     * 최상위 액션들 조회 (부모가 없는 액션)
     */
    public function getTopLevelActions(int $categoryId): Collection;

    /**
     * 자식 액션들 조회
     */
    public function getChildrenActions(int $parentId): Collection;

    /**
     * 액션 생성
     */
    public function create(array $data): WorkAction;

    /**
     * 액션 수정
     */
    public function update(int $id, array $data): WorkAction;

    /**
     * 액션 삭제
     */
    public function delete(int $id): bool;

    /**
     * 관계 포함 액션 목록 조회
     */
    public function getAllWithRelations(): Collection;

    /**
     * 통계 정보 포함 액션 목록 조회
     */
    public function getAllWithCounts(): Collection;

    /**
     * 순환 참조 체크
     */
    public function checkCircularReference(int $actionId, int $parentId): bool;
} 