<?php

namespace App\Repositories\Interfaces;

use App\Models\WorkCategory;
use Illuminate\Database\Eloquent\Collection;

interface WorkCategoryRepositoryInterface
{
    /**
     * 모든 활성 카테고리 조회
     */
    public function getAllActive(): Collection;

    /**
     * 모든 카테고리 조회 (비활성 포함)
     */
    public function getAll(): Collection;

    /**
     * ID로 카테고리 조회
     */
    public function findById(int $id): ?WorkCategory;

    /**
     * 코드로 카테고리 조회
     */
    public function findByCode(string $code): ?WorkCategory;

    /**
     * 활성 상태 코드로 카테고리 조회
     */
    public function findActiveByCode(string $code): ?WorkCategory;

    /**
     * 카테고리 생성
     */
    public function create(array $data): WorkCategory;

    /**
     * 카테고리 수정
     */
    public function update(int $id, array $data): WorkCategory;

    /**
     * 카테고리 삭제
     */
    public function delete(int $id): bool;

    /**
     * 카테고리 코드 중복 체크
     */
    public function existsByCode(string $code, ?int $excludeId = null): bool;

    /**
     * 관계 포함 카테고리 목록 조회
     */
    public function getAllWithRelations(): Collection;

    /**
     * 통계 정보 포함 카테고리 목록 조회
     */
    public function getAllWithCounts(): Collection;
} 