<?php

namespace App\Exports;

use App\Helpers\XLSXWriter\XLSXWriter;
use App\Models\Product;
use App\Models\RepairGrade;
use App\Models\RepairProduct;
use App\Services\ProductQueryBuilderService;
use App\Services\SimpleLogService;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class ProductsExport extends BaseExport
{
    // 식별자로, 이 Export 클래스가 어떤 타입(종류)인지 구분할 때 사용
    public static function type(): string { return 'products'; }

    // request에서 필요한 데이터 추출
    public static function fromRequest(Request $request): self
    {
        $origin = $request->headers->get('origin');

        // 기본 메타데이터 생성
        $meta = [
            'author' => auth()->user()->name ?? '',
            'company' => 'Cornerstone Project',
            'origin' => $origin,
        ];

        // 동적 제목 설정
        $exportType = $request->input('type', 'default');
        $title = self::getDynamicTitle($exportType);
        $meta['title'] = $title;

        // 실제 쿼리 파라미터
        $data = [
            'type' => $request->input('type'),
            'reqId' => $request->input('reqId'),
            'reqType' => $request->input('reqType'),
            'beginAt' => $request->input('beginAt'),
            'endAt' => $request->input('endAt'),
            'checkedStatus' => $request->input('checkedStatus'),
            'productStatus' => $request->input('productStatus'),
            'processCd' => $request->input('processCd'),
            'palletStatus' => $request->input('palletStatus'),
            'cate4' => $request->input('cate4'),
            'cate5' => $request->input('cate5'),
            'isRg' => $request->input('isRg'),
            'isAg' => $request->input('isAg'),
            'searchType' => $request->input('searchType'),
            'keyword' => $request->input('keyword'),
        ];

        return new self($meta, $data);
    }

    /**
     * Export 제목 반환<br>
     * 기본 제목이고, 아래 getDynamicTitle() 메서드에서 동적으로 변경됨
     */
    public static function getExportTitle(): string
    {
        return '작업 목록';
    }

    /**
     * 동적 제목 생성
     */
    private static function getDynamicTitle(string $type): string
    {
        return match ($type) {
            'apple' => '애플(DOA)상품목록',
            'rg' => 'RG상품목록',
            'undelivered' => '미입고상품목록',
            'unlinked' => '미등록상품목록',
            'duplicate' => '중복상품목록',
            'inspect' => '검수대상상품목록',
            default => '입고작업목록',
        };
    }

    /**
     * 헤더 정보 반환
     */
    protected function getHeader(): array
    {
        return [
            'A' => ['번호'=>'integer'], # 8
            'B' => ['입고번호'=>'string'], # 10
            'C' => ['입고일자'=>'string'], # 12
            'D' => ['로트번호'=>'string'], # 20
            'E' => ['카테고리4차'=>'string'], # 20
            'F' => ['카테고리5차'=>'string'], # 20
            'G' => ['QAID'=>'string'], # 15
            'H' => ['바코드'=>'string'], # 20
            'I' => ['상품명'=>'string'], # 50
            'J' => ['중복여부'=>'string'], # 10
            'K' => ['단가'=>'#,##0'], # 10
            'L' => ['처리상태'=>'string'], # 17
            'M' => ['수리상태'=>'string'], # 12
            'N' => ['등록일시'=>'string'], # 17
            'O' => ['등록자'=>'string'], # 10
            'P' => ['검수상태'=>'string'], # 10
            'Q' => ['검수일자'=>'string'], # 17
            'R' => ['검수자'=>'string'], # 10
            'S' => ['출고일자'=>'string'], # 12
            'T' => ['비고'=>'string'], # 10
        ];
    }

    /**
     * 헤더 옵션 반환
     */
    protected function getHeaderOption(): array
    {
        $fontSize = 13;
        $height = 30;

        return [
            'widths' => [8, 10, 12, 20, 20, 20, 15, 20, 50, 10, 10, 17, 12, 17, 10, 10, 17, 10, 12, 10],

            // 상단 첫 번째 행 고정
            'freeze_rows' => 1,
            'freeze_columns' => 2,

            // 전체적으로 하나의 스타일을 적용하려면 이것만 있어도 됨 - height는 안 됨.
            'styles' => [
                'font' => '맑은 고딕',
                'font-size' => $fontSize,
                'font-style' => 'bold',
                'halign' => 'center',
                'fill' => '#A4A4A4',
                'height' => $height
            ],
        ];
    }

    /**
     * 데이터 조회
     */
    protected function getData(): Collection
    {
        return $this->getProducts($this->data);
    }

    /**
     * 행 데이터 작성
     */
    protected function writeRows(XLSXWriter $writer, Collection $rows): void
    {
        $no = 1;
        foreach ($rows as $product) {
            $repairProduct = $product->repairProduct ?? null;

            $gradeName = '';
            if ($repairProduct?->status === RepairProduct::STATUS_WAITING) {
                $gradeCode = RepairGrade::GRADE_WAITING;
                $gradeName = RepairGrade::$GRADE_NAME[$gradeCode];
            } elseif ($repairProduct?->status === RepairProduct::STATUS_REPAIRED) {
                $gradeCode = $repairProduct?->repairGrade?->code;
                $gradeName = RepairGrade::$GRADE_NAME[$gradeCode];
            }

            $exportAt = $palletProduct?->pallet?->exported_at ?? null;
            if ($exportAt) {
                $exportAt = substr($exportAt, 0, 10);
            }

            $item = [
                'A' => $no++,
                'B' => $product->req_id,
                'C' => $product->req->req_at ?? '',
                'D' => $product->lot->name ?? '',
                'E' => $product->cate4->name ?? '',
                'F' => $product->cate5->name ?? '',
                'G' => $product->qaid,
                'H' => $product->barcode ?? '',
                'I' => $product->name ?? '',
                'J' => Product::$DUPLICATED_NAME[$product->duplicated],
                'K' => $product->amount,
                'L' => Product::$STATUS_NAME[$product->status],
                'M' => $gradeName, # 수리상태
                'N' => $product->created_at, # Carbon 객체
                'O' => $product->user->name ?? '',
                'P' => Product::$CHECK_STATUS_NAME[$product->checked_status],
                'Q' => $product->checked_at ?? '', # Carbon 개체가 아님
                'R' => $product->checkedUser->name ?? '',
                'S' => $exportAt, # 출고일자
                'T' => Product::$RG_NAME[$product->rg], # RG 상품
            ];

            $writer->writeSheetRow($this->sheetName, $item, $this->defaultCellStyle);
        }
    }

    /**
     * 상품 리스트
     *
     * @param array $data
     * @return Collection
     */
    private function getProducts(array $data): Collection
    {
        $productService = new ProductQueryBuilderService();

        if ($data['type'] === 'inspects') {
            $products = $productService->inspectPage([
                'reqId' => $data['reqId'],
                'cate4' => $data['cate4'],
                'cate5' => $data['cate5'],
                'searchType' => $data['searchType'],
                'keyword' => $data['keyword'],
                'isRg' => $data['isRg'],
                'isAg' => $data['isAg'],
            ])->with(['palletProducts.pallet', 'repairProduct', 'req', 'lot', 'cate4', 'cate5', 'user', 'checkedUser'])->get();
        } elseif ($data['type'] === 'duplicate') {
            $products = $productService->duplicatedPage([
                'reqId' => $data['reqId'],
                'productStatus' => $data['productStatus'],
                'processCd' => $data['processCd'],
                'cate4' => $data['cate4'],
                'cate5' => $data['cate5'],
                'searchType' => $data['searchType'],
                'keyword' => $data['keyword'],
            ])->with(['palletProducts.pallet', 'repairProduct', 'req', 'lot', 'cate4', 'cate5', 'user', 'checkedUser'])->get();
        } elseif ($data['type'] === 'unlinked') {
            $products = $productService->unlinkedPage([
                'reqId' => $data['reqId'],
                'productStatus' => $data['productStatus'],
                'processCd' => $data['processCd'],
                'cate4' => $data['cate4'],
                'cate5' => $data['cate5'],
                'searchType' => $data['searchType'],
                'keyword' => $data['keyword'],
            ])->with(['palletProducts.pallet', 'repairProduct', 'req', 'lot', 'cate4', 'cate5', 'user', 'checkedUser'])->get();
        } else {
            $products = $productService->taskPage([
                'reqId' => $data['reqId'],
                'type' => $data['reqType'],
                'beginAt' => $data['beginAt'],
                'endAt' => $data['endAt'],
                'checkedStatus' => $data['checkedStatus'],
                'productStatus' => $data['productStatus'],
                'processCd' => $data['processCd'],
                'palletStatus' => $data['palletStatus'],
                'cate4' => $data['cate4'],
                'cate5' => $data['cate5'],
                'isRg' => $data['isRg'],
                'isAg' => $data['isAg'],
                'searchType' => $data['searchType'],
                'keyword' => $data['keyword'],
            ])->with(['palletProducts.pallet', 'req', 'lot', 'cate4', 'cate5', 'user', 'checkedUser'])->get();
        }

        return $products;
    }
}
