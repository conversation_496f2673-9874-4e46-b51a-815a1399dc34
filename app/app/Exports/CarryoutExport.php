<?php

namespace App\Exports;

use App\Helpers\HelperLibrary;
use App\Helpers\XLSXWriter\XLSXWriter;
use App\Models\CarryoutProduct;
use App\Models\Pallet;
use App\Models\Process;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class CarryoutExport extends BaseExport
{
    // 식별자로, 이 Export 클래스가 어떤 타입(종류)인지 구분할 때 사용
    public static function type(): string { return 'carryouts'; }

    // request에서 필요한 데이터 추출
    public static function fromRequest(Request $request): self
    {
        $user = Auth::user();
        $origin = $request->headers->get('origin');

        // 기본 메타데이터 생성
        $meta = [
            'title' => '외주반출작업목록',
            'author' => $user->name,
            'company' => 'Cornerstone Project',
            'origin' => $origin,
        ];

        // 실제 쿼리 파라미터
        $data = [
            'carryoutId' => $request->input('carryoutId'),
        ];

        return new self($meta, $data);
    }

    /**
     * Export 제목 반환<br>
     * 기본 제목이고, 아래 getDynamicTitle() 메서드에서 동적으로 변경됨
     */
    public static function getExportTitle(): string
    {
        return '외주 반출 작업 목록';
    }

    /**
     * 헤더 정보 반환
     */
    protected function getHeader(): array
    {
        return [
            'A' => ['번호'=>'integer'], # 8
            'B' => ['외주반출번호'=>'integer'],
            'C' => ['입고일자'=>'string'],
            'D' => ['반출등록일'=>'string'],
            'E' => ['반입일'=>'string'],
            'F' => ['외주반출일시'=>'string'],
            'G' => ['외주반출자'=>'string'],
            'H' => ['QAID'=>'string'],
            'I' => ['4차카테고리'=>'string'],
            'J' => ['5차카테고리'=>'string'],
            'K' => ['바코드'=>'string'],
            'L' => ['상품명'=>'string'],
            'M' => ['상품가격'=>'#,##0'],
            'N' => ['점검상태'=>'string'],
            'O' => ['수리상태'=>'string'],
            'P' => ['재생등급'=>'string'],
            'Q' => ['수리금액'=>'#,##0'],
            'R' => ['수리내용'=>'string'],
            'S' => ['처리상태'=>'string'],
            'T' => ['수리일자'=>'string'],
            'U' => ['수리코드'=>'string'],
        ];
    }

    /**
     * 헤더 옵션 반환
     */
    protected function getHeaderOption(): array
    {
        $fontSize = 13;
        $height = 30;

        return [
            'widths' => [8, 15, 12, 12, 12, 17, 11, 12, 20, 20, 20, 40, 14, 17, 10, 10, 17, 10, 17, 12, 12],

            // 상단 첫 번째 행 고정
            'freeze_rows' => 1,
            'freeze_columns' => 2,

            // 전체적으로 하나의 스타일을 적용하려면 이것만 있어도 됨 - height는 안 됨.
            'styles' => [
                'font' => '맑은 고딕',
                'font-size' => $fontSize,
                'font-style' => 'bold',
                'halign' => 'center',
                'fill' => '#A4A4A4',
                'height' => $height
            ],
        ];
    }

    /**
     * 데이터 조회
     */
    protected function getData(): Collection
    {
        return $this->getProducts($this->data);
    }

    /**
     * 행 데이터 작성
     */
    protected function writeRows(XLSXWriter $writer, Collection $rows): void
    {
        $no = 1;
        foreach ($rows as $row) {
            // $row는 이미 Eloquent 모델 객체이므로 (object) 변환 불필요
            $carryout = $row->carryout;
            $product = $row->product;
            $barcode = $product->barcode;

            $carryinAt =  $carryout->carryin_at ?? null;
            if ($row->carryin_at !== null) {
                $carryinAt = $row->carryin_at;
            }

            $memo = '';
            if (!empty($row->memo)) {
                $memo = $row->memo;
            }

            $checkedUser = $row->checkedUser()->withTrashed()->first();

            $item = [
                'A' => $no++,
                'B' => $row->carryout_id, # 외주 반출 번호
                'C' => $product->req->req_at ?? '', # 입고일
                'D' => substr($carryout->carryout_at, 0, 10) ?? '', # 반출일
                'E' => substr($carryinAt, 0, 10) ?? '', # 반입일
                'F' => substr($row->checked_at, 0, 19) ?? '', # 외주반출일시
                'G' => $checkedUser->name ?? '', # 반출 직원
                'H' => $product->qaid ?? '',
                'I' => $product->cate4->name ?? '',
                'J' => $product->cate5->name ?? '',
                'K' => $product->barcode ?? '',
                'L' => $product->name ?? '',
                'M' => $product->amount ?? '',
                'N' => $row->repairSymptom->name ?? '', # 점검 상태
                'O' => $row->repairProcess->name ?? '', # 수리 상태
                'P' => $row->repairGrade->name ?? '', # 재생 상태
                'Q' => $row->invoice2 ?? '', # 수리금액
                'R' => $memo, # 수리 내용
                'S' => CarryoutProduct::$STATUS_NAME[$row->status], # 처리 상태
                'T' => substr($row->renovate_at, 0, 10) ?? '', # 수리일자
                'U' => $carryout->token->token ?? '', # 수리 키
            ];

            $writer->writeSheetRow($this->sheetName, $item, $this->defaultCellStyle);
        }
    }

    /**
     * 상품 리스트
     *
     * @param array $data
     * @return Collection
     */
    private function getProducts(array $data): Collection
    {
        $builder = CarryoutProduct::with([
            'product.req', 
            'product.cate4', 
            'product.cate5', 
            'carryout.token', 
            'repairSymptom', 
            'repairProcess', 
            'repairGrade'
        ])
            ->select('carryout_products.*')
            ->leftJoin('carryouts', 'carryouts.id', '=', 'carryout_products.carryout_id')
            ->leftJoin('products', 'carryout_products.product_id', '=', 'products.id')
            ->where('carryout_products.status', '!=', CarryoutProduct::STATUS_CANCELED)
            ->where('products.status', Product::STATUS_CARRIED_OUT);

        if ($data['carryoutId']) {
            $builder->where('carryout_products.carryout_id', $data['carryoutId']);
        }

        $builder->orderBy('carryouts.carryout_at', 'desc')
            ->orderBy('carryout_products.checked_at', 'desc');

        return $builder->get();
    }
}
