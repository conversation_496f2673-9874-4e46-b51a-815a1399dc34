<?php

namespace App\Services;

use App\Helpers\HelperLibrary;
use App\Models\CarryoutToken;

class TokenService
{
    public function existToken(string $token): bool
    {
        return CarryoutToken::where('token', $token)->exists();
    }

    public function generateToken(): string
    {
        $characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $numbers = "0123456789";

        do {
            $token = HelperLibrary::generateRandomString($characters)
                . HelperLibrary::generateRandomString($numbers, 6);

            $exist = $this->existToken($token);
        } while($exist);

        return $token;
    }

    public function storeToken(string $token): CarryoutToken
    {
        $carryoutToken = new CarryoutToken;
        $carryoutToken->token = $token;
        $carryoutToken->save();

        return $carryoutToken;
    }
}
