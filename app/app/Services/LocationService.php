<?php

namespace App\Services;

use App\Models\Location;
use App\Helpers\HelperLibrary;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Throwable;

class LocationService
{
    public function isCode(string $place, string $code): bool
    {
        return Location::where('place', $place)
            ->where('code', 'like', '%' . $code)
            ->exists();
    }

    /**
     * 임의의 Pallet 위치를 생성
     */
    public function createCode(string $place): array
    {
        $characters = "ABCDEFGHJKLMNQRSTUWXYZ";
        $characters2 = "ABCDEFGHJKLMNQRSTUWXYZ23456789";
        $numbers = "0123456789";

        // 코드 생성과 검증이 실패할 때까지 계속
        do {
            $level = HelperLibrary::generateRandomString($characters)
                . HelperLibrary::generateRandomString($characters2, 2)
                . HelperLibrary::generateRandomString($characters);
            $column = HelperLibrary::generateRandomString($numbers, 4);

            $newCode = $level . '-' . $column;
            $isCode = $this->isCode($place, $newCode);
        } while($isCode);

        return [
            'level' => $level,
            'column' => $column
        ];
    }

    public function getList($data): Builder
    {
        $productCountQuery = "SELECT COUNT(DISTINCT p.barcode) FROM pallet_products pltp, products p, pallets plt WHERE pltp.product_id=p.id AND pltp.pallet_id=plt.id AND plt.location_id=locations.id";
        $stockSumQuery = "SELECT SUM(pltp.quantity) FROM pallet_products pltp, pallets plt WHERE pltp.pallet_id=plt.id AND plt.location_id=locations.id";

        $builder = Location::with('pallets')
            ->select(
                'locations.*',
                DB::raw("($productCountQuery) AS pcount"),
                DB::raw("($stockSumQuery) AS stock_sum")
            )
            ->where('place', 'like', 'KR-ESCS%');

        if ($data['enable'] === 'Y' || $data['enable'] === 'N') {
            $builder->where('enable', $data['enable']);
        }

        $fields = ['store', 'line', 'rack', 'level', 'column'];

        foreach ($fields as $field) {
            if (!empty($data[$field])) {
                $builder->where('code', 'like', '%' . $data[$field] . '%');
            }
        }

        $builder->orderBy('place')
            ->orderBy('created_at', 'desc')
            ->orderBy('code');

        return $builder;
    }

    /**
     * @throws Exception|Throwable
     */
    public function updateLocation($data): void
    {
        try {
            DB::beginTransaction();

            $code = $data['store'] . '-' . $data['line'] . '-' . $data['rack'] . '-' . $data['level'] . '-' . $data['column'];
            Location::where('id', $data['id'])->update([
                'code' => $code,
                'name' => $data['name'],
                'enable' => $data['enable'],
            ]);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('setting', '[Location]팔레트 위치 설정 실패: 팔레트 정보 업데이트 오류', [], $e);

            throw new Exception('[Location]팔레트 위치 설정 실패');
        }
    }

    /**
     * 팔레트 위치 저장
     * @throws Exception|Throwable
     */
    public function store(string $place, string $code): Model|Location
    {
        $codeArr = explode("-", $code);
        $name = $codeArr[0] . "존 "
            . $codeArr[1] . "층 "
            . $codeArr[2] . "번 "
            . $codeArr[3] . " "
            . $codeArr[4];

        $location = Location::firstOrNew([
            'place' => $place,
            'code' => $code
        ]);

        if ($location->exists && $location->enable === Location::F_ENABLE_N) {
            throw new Exception("팔래트 상태(적재불가능)를 확인해 주시기 바랍니다.");
        }

        $location->fill([
            'place' => $place,
            'code' => $code,
            'name' => $name,
            'enable' => Location::F_ENABLE_Y,
        ]);

        $location->save();

        return $location;
    }
}
