<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Qaid;
use App\Models\User;
use Exception;

/**
 * @todo: 사용 안 함
 * 현재 LoadedService::handleCheckedProduct 에 연결 되어 있지만
 * 이 부분 자체를 삭제할 예정이라 삭제하면 이것도 같이 삭제 할 것.
 */
class HistoryLogger
{
    private ?Product $product = null;
    private ?User $user = null;
    private string $title;
    private ?string $previousStatus = null;
    private ?string $currentStatus = null;
    private ?int $palletId = null;
    private ?int $palletProductId = null;
    private ?int $carryoutId = null;
    private ?int $carryoutProductId = null;

    public function __construct()
    {
    }

    public function setProduct(?Product $product): static
    {
        $this->product = $product;
        return $this;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;
        return $this;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;
        return $this;
    }

    public function setPreviousStatus(?string $status): static
    {
        $this->previousStatus = $status ? Product::$STATUS_NAME[$status] : '변경사항 없음';
        return $this;
    }

    public function setCurrentStatus(?string $status): static
    {
        $this->currentStatus = $status ? Product::$STATUS_NAME[$status] : '변경사항 없음';
        return $this;
    }

    public function setPalletId(?int $id): static
    {
        $this->palletId = $id ?? null;
        return $this;
    }

    public function setPalletProductId(?int $id): static
    {
        $this->palletProductId = $id ?? null;
        return $this;
    }

    public function setCarryoutId(?int $id): static
    {
        $this->carryoutId = $id ?? null;
        return $this;
    }

    public function setCarryoutProductId(?int $id): static
    {
        $this->carryoutProductId = $id ?? null;
        return $this;
    }

    // 배열을 반환 -> 이걸 직접 저장되도록 변경할 것
    public function build(): array
    {
        return [
            'qaid' => $this->product->qaid,
            'req_id' => $this->product->req_id,
            'product_id' => $this->product->id,
            'log' => [
                'date' => date('Y-m-d H:i:s'),
                'username' => $this->user->name,
                'title' => $this->title,
                'previous_status' => $this->previousStatus,
                'current_status' => $this->currentStatus,
                'pallet_id' => $this->palletId,
                'pallet_product_id' => $this->palletProductId,
                'carryout_id' => $this->carryoutId,
                'carryout_product_id' => $this->carryoutProductId,
            ],
        ];
    }

    /**
     * 1개씩 바로 저장할 수 있음
     *
     * @return void
     * @throws Exception
     */
    public function save(): void
    {
        if (!$this->product || !$this->user) {
            throw new Exception("QAID 히스토리를 저장하기 위해서는 Product 정보와 User 정보가 반듯이 필요합니다.");
        }

        $qaid = Qaid::firstOrNew([
            'qaid' => $this->product->qaid,
            'req_id' => $this->product->req_id,
            'product_id' => $this->product->id
        ]);

        $history = $this->build();
        if (!empty($history['log'])) {
            $histories = $qaid->histories ? json_decode($qaid->histories, true) : [];
            $histories[] = $history['log'];
            $qaid->histories = json_encode($histories);
        }

        $qaid->save();
    }

    /**
     * 반복문으로 생성된 배열을 바로 처리할 수 있음
     *
     * @param  array  $histories
     * @param  int  $chunkSize 해당 개수만큼 끊어 가면서 입력(부하 방지)
     * @return void
     */
    public function saveBatch(array $histories, int $chunkSize = 500): void
    {
        collect($histories)->chunk($chunkSize)->each(function ($chunk) {
            foreach ($chunk as $history) {
                $qaid = Qaid::firstOrNew([
                    'qaid' => $history['qaid'],
                    'req_id' => $history['req_id'],
                    'product_id' => $history['product_id']
                ]);

                if (!empty($history['log'])) {
                    $existingHistories = $qaid->histories ? json_decode($qaid->histories, true) : [];
                    $existingHistories[] = $history['log'];
                    $qaid->histories = json_encode($existingHistories);
                }

                $qaid->save();
            }
        });
    }

    /**
     * 새로 생성된 QAID일 경우 추가해 준다.
     *
     * @param string $oldQaid 기존 QAID
     * @param int $reqId
     * @param int $productId
     * @param string $newQaid  새로 발급한 QAID
     * @return void
     */
    public function updateNewQaid(string $oldQaid, int $reqId, int $productId, string $newQaid): void
    {
        Qaid::where('qaid', $oldQaid)
            ->where('req_id', $reqId)
            ->where('product_id', $productId)
            ->update([
                'new_qaid' => $newQaid
            ]);
    }
}
