<?php

namespace App\Services;

use App\Models\WorkCategory;
use App\Repositories\WorkCategoryRepository;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use Throwable;

class WorkCategoryService
{
    protected WorkCategoryRepository $categoryRepository;

    public function __construct(WorkCategoryRepository $categoryRepository)
    {
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * 모든 활성 카테고리 조회
     */
    public function getAllActiveCategories(): Collection
    {
        return Cache::remember('work_categories_active', 3600, function () {
            return $this->categoryRepository->getAllActive();
        });
    }

    /**
     * 관계 포함 카테고리 목록 조회
     */
    public function getCategoriesWithRelations(): Collection
    {
        return Cache::remember('work_categories_with_relations', 3600, function () {
            return $this->categoryRepository->getAllWithRelations();
        });
    }

    /**
     * 통계 정보 포함 카테고리 목록 조회
     */
    public function getCategoriesWithCounts(): Collection
    {
        return $this->categoryRepository->getAllWithCounts();
    }

    /**
     * 카테고리 조회 (ID로)
     */
    public function findById(int $id): ?WorkCategory
    {
        return $this->categoryRepository->findById($id);
    }

    /**
     * 카테고리 조회 (코드로)
     */
    public function findByCode(string $code): ?WorkCategory
    {
        return $this->categoryRepository->findByCode($code);
    }

    /**
     * 활성 카테고리 조회 (코드로)
     */
    public function findActiveByCode(string $code): ?WorkCategory
    {
        return $this->categoryRepository->findActiveByCode($code);
    }

    /**
     * 카테고리 생성
     * @throws Exception|Throwable
     */
    public function createCategory(array $data): WorkCategory
    {
        // 코드 중복 체크
        if ($this->categoryRepository->existsByCode($data['code'])) {
            throw new InvalidArgumentException('이미 존재하는 카테고리 코드입니다.');
        }

        try {
            DB::beginTransaction();

            $category = $this->categoryRepository->create($data);

            // 캐시 초기화
            $this->clearCache();

            DB::commit();

            SimpleLogService::debug('work_status', '카테고리 생성 완료', [
                'payload' => $data,

                'category' => $category
            ]);

            return $category;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 카테고리 수정
     * @throws Exception|Throwable
     */
    public function updateCategory(int $id, array $data): WorkCategory
    {
        $category = $this->categoryRepository->findById($id);
        if (!$category) {
            throw new InvalidArgumentException('카테고리를 찾을 수 없습니다.');
        }

        // 코드 중복 체크 (자신 제외)
        if (isset($data['code']) && $this->categoryRepository->existsByCode($data['code'], $id)) {
            throw new InvalidArgumentException('이미 존재하는 카테고리 코드입니다.');
        }

        try {
            DB::beginTransaction();
            $category = $this->categoryRepository->update($id, $data);

            // 캐시 초기화
            $this->clearCache();

            DB::commit();

            SimpleLogService::debug('work_status', '카테고리 수정 완료', [
                'payload' => $data,
                'category' => $category,
            ]);

            return $category;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 카테고리 삭제
     * @throws Exception|Throwable
     */
    public function deleteCategory(int $id): bool
    {
        $category = $this->categoryRepository->findById($id);
        if (!$category) {
            throw new InvalidArgumentException('카테고리를 찾을 수 없습니다.');
        }

        if (!$this->categoryRepository->canDelete($id)) {
            throw new InvalidArgumentException('관련 데이터가 있어 삭제할 수 없습니다.');
        }

        try {
            DB::beginTransaction();

            $result = $this->categoryRepository->delete($id);

            // 캐시 초기화
            $this->clearCache();

            DB::commit();

            SimpleLogService::debug('work_status', '카테고리 삭제 완료', [
                'category_id' => $id,
                'result' => $result,
            ]);

            return $result;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 카테고리 활성화
     * @throws Exception|Throwable
     */
    public function activateCategory(int $id): WorkCategory
    {
        return $this->updateCategory($id, ['is_active' => true]);
    }

    /**
     * 카테고리 비활성화
     * @throws Exception|Throwable
     */
    public function deactivateCategory(int $id): WorkCategory
    {
        $category = $this->categoryRepository->findById($id);
        if (!$category) {
            throw new InvalidArgumentException('카테고리를 찾을 수 없습니다.');
        }

        if (!$this->categoryRepository->canDeactivate($id)) {
            throw new InvalidArgumentException('관련된 활성 데이터가 있어 비활성화할 수 없습니다.');
        }

        return $this->updateCategory($id, ['is_active' => false]);
    }

    /**
     * 카테고리 코드 중복 체크
     */
    public function isCodeDuplicate(string $code, ?int $excludeId = null): bool
    {
        return $this->categoryRepository->existsByCode($code, $excludeId);
    }

    /**
     * 카테고리 통계 조회
     */
    public function getCategoryStats(): array
    {
        $activeCount = $this->categoryRepository->getAllActive()->count();
        $totalCount = $this->categoryRepository->getAll()->count();
        $inactiveCount = $totalCount - $activeCount;

        return [
            'active_count' => $activeCount,
            'inactive_count' => $inactiveCount,
            'total_count' => $totalCount,
        ];
    }

    /**
     * 캐시 초기화
     */
    private function clearCache(): void
    {
        Cache::forget('work_categories_active');
        Cache::forget('work_categories_with_relations');
        Cache::tags(['work_categories'])->flush();
    }
}
