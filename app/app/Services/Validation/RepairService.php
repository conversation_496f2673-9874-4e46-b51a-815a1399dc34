<?php

namespace App\Services\Validation;

use App\Models\Product;
use Exception;

class RepairService
{
    /**
     * 수리 가능 여부 확인
     * @throws Exception
     */
    public function canBeRepaired(Product $product, string $qaid): void
    {
        $user = auth()->user();
        $role = mb_strtolower($user->role);

        // 잠금이 되어 있는지 확인
        if ($product->isLocked()) {
            throw new Exception("잠금된 상품입니다. ($product->memo) 관리자에게 문의하세요. (QAID: {$product->qaid})");
        }

        // 외주 업체일 경우
        if ($role === 'guest' && $product->status !== Product::STATUS_CARRIED_OUT) {
            throw new Exception("점검 불가능한 상품입니다. 입력한 QAID: $qaid");
        }

        // 본사 직원일 경우
        if ($role !== 'guest' && $product->status === Product::STATUS_CARRIED_OUT) {
            throw new Exception("[$qaid]는 외주 반출(외부수리의뢰) 상품으로 수리/점검이 불가능합니다.");
        }

        if ($product->checked_status === Product::CHECKED_STATUS_UNCHECKED) {
            throw new Exception("[$qaid]는 아직 검수되지 않았습니다. 상품을 검수해 주세요.");
        }

        if ($product->checked_status === Product::CHECKED_STATUS_UNDELIVERED) {
            throw new Exception("[$qaid]는 미입고 상품으로 수리/점검이 불가능합니다.");
        }

        if ($product->duplicated === Product::IS_DUPLICATED_Y) {
            throw new Exception("같은 QAID=" . $qaid . "로 중복된 상품(보류처리)이 있습니다.");
        }

        if ($product->status !== Product::STATUS_CARRIED_OUT
            && $product->status > Product::STATUS_WAITING)
        {
            throw new Exception("[$qaid]는 이미 수리/점검이 완료된 상품입니다.");
        }
    }
}
