<?php

namespace App\Services;

use App\Models\WorkStatusTemplate;
use App\Repositories\Interfaces\WorkStatusTemplateRepositoryInterface;
use App\Repositories\Interfaces\WorkCategoryRepositoryInterface;
use App\Repositories\Interfaces\WorkActionRepositoryInterface;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use Throwable;

class WorkStatusTemplateService
{
    protected WorkStatusTemplateRepositoryInterface $templateRepository;
    protected WorkCategoryRepositoryInterface $categoryRepository;
    protected WorkActionRepositoryInterface $actionRepository;

    public function __construct(
        WorkStatusTemplateRepositoryInterface $templateRepository,
        WorkCategoryRepositoryInterface $categoryRepository,
        WorkActionRepositoryInterface $actionRepository
    ) {
        $this->templateRepository = $templateRepository;
        $this->categoryRepository = $categoryRepository;
        $this->actionRepository = $actionRepository;
    }

    /**
     * 모든 활성 템플릿 조회
     */
    public function getAllActiveTemplates(): Collection
    {
        return Cache::remember('work_status_templates_active', 3600, function () {
            return $this->templateRepository->getAllActive();
        });
    }

    /**
     * 카테고리별 활성 템플릿 조회
     */
    public function getTemplatesByCategory(int $categoryId): Collection
    {
        return Cache::remember("work_status_templates_category_{$categoryId}", 3600, function () use ($categoryId) {
            return $this->templateRepository->findActiveByCategoryId($categoryId);
        });
    }

    /**
     * 액션별 활성 템플릿 조회
     */
    public function getTemplatesByAction(int $actionId): Collection
    {
        return Cache::remember("work_status_templates_action_{$actionId}", 3600, function () use ($actionId) {
            return $this->templateRepository->findActiveByActionId($actionId);
        });
    }

    /**
     * 카테고리와 액션으로 템플릿 조회
     */
    public function getTemplatesByCategoryAndAction(int $categoryId, int $actionId): Collection
    {
        return $this->templateRepository->findActiveByCategoryAndAction($categoryId, $actionId);
    }

    /**
     * 관계 포함 템플릿 목록 조회
     */
    public function getTemplatesWithRelations(): Collection
    {
        return $this->templateRepository->getAllWithRelations();
    }

    /**
     * 통계 정보 포함 템플릿 목록 조회
     */
    public function getTemplatesWithCounts(): Collection
    {
        return $this->templateRepository->getAllWithCounts();
    }

    /**
     * 템플릿 조회 (ID로)
     */
    public function findById(int $id): ?WorkStatusTemplate
    {
        return $this->templateRepository->findById($id);
    }

    /**
     * 템플릿 조회 (카테고리, 액션, 템플릿 코드로)
     */
    public function findByKeys(int $categoryId, int $actionId, string $templateCode): ?WorkStatusTemplate
    {
        return $this->templateRepository->findByKeys($categoryId, $actionId, $templateCode);
    }

    /**
     * 조건에 맞는 최적의 템플릿 조회
     */
    public function findBestMatchTemplate(int $categoryId, int $actionId, array $context): ?WorkStatusTemplate
    {
        return $this->templateRepository->findBestMatch($categoryId, $actionId, $context);
    }

    /**
     * 생성자별 템플릿 조회
     */
    public function getTemplatesByCreator(int $creatorId): Collection
    {
        return $this->templateRepository->findByCreator($creatorId);
    }

    /**
     * 템플릿 생성
     * @throws Exception|Throwable
     */
    public function createTemplate(array $data): WorkStatusTemplate
    {
        // 카테고리 존재 확인
        $category = $this->categoryRepository->findById($data['category_id']);
        if (!$category) {
            throw new InvalidArgumentException('카테고리를 찾을 수 없습니다.');
        }

        // 액션 존재 확인
        $action = $this->actionRepository->findById($data['action_id']);
        if (!$action) {
            throw new InvalidArgumentException('액션을 찾을 수 없습니다.');
        }

        // 액션이 카테고리에 속하는지 확인
        if ($action->category_id !== $category->id) {
            throw new InvalidArgumentException('액션이 선택된 카테고리에 속하지 않습니다.');
        }

        // 템플릿 코드 중복 체크
        if ($this->templateRepository->findByKeys($data['category_id'], $data['action_id'], $data['template_code'])) {
            throw new InvalidArgumentException('이미 존재하는 템플릿 코드입니다.');
        }

        // 조건부 생성 규칙 검증
        if (isset($data['conditions']) && !empty($data['conditions'])) {
            $template = new WorkStatusTemplate();
            if (!$template->validateConditions($data['conditions'])) {
                throw new InvalidArgumentException('조건부 생성 규칙이 올바르지 않습니다.');
            }
        }

        DB::beginTransaction();
        try {
            $data['created_by'] = $data['created_by'] ?? auth()->id();
            $template = $this->templateRepository->create($data);

            // 캐시 초기화
            $this->clearCache();

            DB::commit();

            SimpleLogService::debug('work_status', '템플릿 생성 완료', [
                'template_id' => $template->id,
                'category_id' => $template->category_id,
                'action_id' => $template->action_id,
                'template_code' => $template->template_code,
                'name' => $template->name
            ]);

            return $template;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 템플릿 수정
     * @throws Exception|Throwable
     */
    public function updateTemplate(int $id, array $data): WorkStatusTemplate
    {
        $template = $this->templateRepository->findById($id);
        if (!$template) {
            throw new InvalidArgumentException('템플릿을 찾을 수 없습니다.');
        }

        // 카테고리 존재 확인
        if (isset($data['category_id'])) {
            $category = $this->categoryRepository->findById($data['category_id']);
            if (!$category) {
                throw new InvalidArgumentException('카테고리를 찾을 수 없습니다.');
            }
        }

        // 액션 존재 확인
        if (isset($data['action_id'])) {
            $action = $this->actionRepository->findById($data['action_id']);
            if (!$action) {
                throw new InvalidArgumentException('액션을 찾을 수 없습니다.');
            }
        }

        // 액션이 카테고리에 속하는지 확인
        if (isset($data['category_id']) || isset($data['action_id'])) {
            $categoryId = $data['category_id'] ?? $template->category_id;
            $actionId = $data['action_id'] ?? $template->action_id;

            $action = $this->actionRepository->findById($actionId);
            if ($action && $action->category_id !== $categoryId) {
                throw new InvalidArgumentException('액션이 선택된 카테고리에 속하지 않습니다.');
            }
        }

        // 템플릿 코드 중복 체크
        if (isset($data['template_code']) || isset($data['category_id']) || isset($data['action_id'])) {
            $categoryId = $data['category_id'] ?? $template->category_id;
            $actionId = $data['action_id'] ?? $template->action_id;
            $templateCode = $data['template_code'] ?? $template->template_code;

            $existingTemplate = $this->templateRepository->findByKeys($categoryId, $actionId, $templateCode);
            if ($existingTemplate && $existingTemplate->id !== $id) {
                throw new InvalidArgumentException('이미 존재하는 템플릿 코드입니다.');
            }
        }

        // 조건부 생성 규칙 검증
        if (isset($data['conditions']) && !empty($data['conditions'])) {
            if (!$template->validateConditions($data['conditions'])) {
                throw new InvalidArgumentException('조건부 생성 규칙이 올바르지 않습니다.');
            }
        }

        try {
            DB::beginTransaction();

            $template = $this->templateRepository->update($id, $data);

            // 캐시 초기화
            $this->clearCache();

            DB::commit();

            SimpleLogService::debug('work_status', '템플릿 수정 완료', [
                'template_id' => $template->id,
                'category_id' => $template->category_id,
                'action_id' => $template->action_id,
                'template_code' => $template->template_code,
                'name' => $template->name
            ]);

            return $template;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 템플릿 삭제
     * @throws Exception|Throwable
     */
    public function deleteTemplate(int $id): bool
    {
        $template = $this->templateRepository->findById($id);
        if (!$template) {
            throw new InvalidArgumentException('템플릿을 찾을 수 없습니다.');
        }

        if (!$this->templateRepository->canDelete($template)) {
            throw new InvalidArgumentException('이 템플릿을 사용하는 상태가 있어 삭제할 수 없습니다.');
        }

        DB::beginTransaction();
        try {
            $result = $this->templateRepository->delete($id);

            // 캐시 초기화
            $this->clearCache();

            DB::commit();

            SimpleLogService::debug('work_status', '템플릿 삭제 완료', [
                'template_id' => $id,
                'template_code' => $template->template_code,
                'name' => $template->name
            ]);

            return $result;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 템플릿 활성화
     */
    public function activateTemplate(int $id): WorkStatusTemplate
    {
        return $this->updateTemplate($id, ['is_active' => true]);
    }

    /**
     * 템플릿 비활성화
     */
    public function deactivateTemplate(int $id): WorkStatusTemplate
    {
        $template = $this->templateRepository->findById($id);
        if (!$template) {
            throw new InvalidArgumentException('템플릿을 찾을 수 없습니다.');
        }

        if (!$this->templateRepository->canDeactivate($template)) {
            throw new InvalidArgumentException('이 템플릿을 사용하는 상태가 있어 비활성화할 수 없습니다.');
        }

        return $this->updateTemplate($id, ['is_active' => false]);
    }

    /**
     * 템플릿 복사
     * @throws Exception|Throwable
     */
    public function duplicateTemplate(int $id, string $newTemplateCode, ?int $newCreatorId = null): WorkStatusTemplate
    {
        $template = $this->templateRepository->findById($id);
        if (!$template) {
            throw new InvalidArgumentException('템플릿을 찾을 수 없습니다.');
        }

        // 새 템플릿 코드 중복 체크
        if ($this->templateRepository->findByKeys($template->category_id, $template->action_id, $newTemplateCode)) {
            throw new InvalidArgumentException('이미 존재하는 템플릿 코드입니다.');
        }

        try {
            DB::beginTransaction();

            $newTemplate = $this->templateRepository->duplicate($template, $id, $newTemplateCode, $newCreatorId);

            // 캐시 초기화
            $this->clearCache();

            DB::commit();

            SimpleLogService::debug('work_status', '동적 WorkStatus: 템플릿 복사 완료', [
                'original_template_id' => $id,
                'new_template_id' => $newTemplate->id,
                'new_template_code' => $newTemplateCode
            ]);

            return $newTemplate;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 사용되지 않는 템플릿 조회
     */
    public function getUnusedTemplates(): Collection
    {
        return $this->templateRepository->getUnusedTemplates();
    }

    /**
     * 템플릿 통계 조회
     */
    public function getTemplateStats(): array
    {
        $activeCount = $this->templateRepository->getAllActive()->count();
        $totalCount = $this->templateRepository->getAll()->count();
        $inactiveCount = $totalCount - $activeCount;
        $unusedCount = $this->templateRepository->getUnusedTemplates()->count();

        return [
            'active_count' => $activeCount,
            'inactive_count' => $inactiveCount,
            'unused_count' => $unusedCount,
            'total_count' => $totalCount,
        ];
    }

    /**
     * 템플릿 사용 빈도 조회
     */
    public function getTemplateUsageStats(): array
    {
        $templates = $this->templateRepository->getAllWithCounts();

        return $templates->map(function ($template) {
            return [
                'template_id' => $template->id,
                'template_code' => $template->template_code,
                'name' => $template->name,
                'category_name' => $template->category->name,
                'action_name' => $template->action->name,
                'usage_count' => $template->work_statuses_count,
                'is_active' => $template->is_active,
            ];
        })->sortByDesc('usage_count')->values()->toArray();
    }

    /**
     * 캐시 초기화
     */
    private function clearCache(): void
    {
        Cache::forget('work_status_templates_active');

        // 카테고리별 캐시 초기화
        $categories = $this->categoryRepository->getAll();
        foreach ($categories as $category) {
            Cache::forget("work_status_templates_category_{$category->id}");
        }

        // 액션별 캐시 초기화
        $actions = $this->actionRepository->getAll();
        foreach ($actions as $action) {
            Cache::forget("work_status_templates_action_{$action->id}");
        }

        Cache::tags(['work_status_templates'])->flush();
    }
}
