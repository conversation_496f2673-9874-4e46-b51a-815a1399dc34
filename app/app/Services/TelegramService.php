<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Storage;
use Telegram\Bot\Exceptions\TelegramSDKException;
use Telegram\Bot\FileUpload\InputFile;
use Telegram\Bot\Laravel\Facades\Telegram;

class TelegramService
{
    public function sendMessage(string $message): void
    {
        try {
            Telegram::sendMessage([
                'chat_id' => config('services.telegram.chat_id'),
                'text' => $message,
                'parse_mode' => 'HTML'
            ]);
        } catch (TelegramSDKException $e) {
            // Telegram API 관련 예외 처리
            SimpleLogService::error('telegram', 'Telegram 메시지 전송 실패', [], $e);
        } catch (Exception $e) {
            SimpleLogService::error('telegram', '메시지 전송 중 예상치 못한 오류 발생', [], $e);
        }
    }

    /**
     * @param  string  $path storage 안의 경로와 파일명이 필요함 ex: temp/test.xlsx
     * @param  string  $filename
     * @param  string  $caption
     * @return void
     */
    public function sendDocument(string $path, string $filename, string $caption = ''): void
    {
        try {
            Telegram::sendDocument([
                'chat_id' => config('services.telegram.chat_id'),
                'document' => InputFile::createFromContents(Storage::get($path), $filename),
                'caption' => $caption
            ]);
        } catch (TelegramSDKException $e) {
            // Telegram API 관련 예외 처리
            SimpleLogService::error('telegram', 'Telegram 문서 전송 실패', [], $e);
        } catch (Exception $e) {
            SimpleLogService::error('telegram', '문서 전송 중 예상치 못한 오류 발생', [], $e);
        }
    }
}
