<?php

namespace App\Services;

use App\Models\Product;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Exception;

class ProductLockService
{
    /**
     * 상품들을 잠금 처리
     * @throws Exception
     */
    public function lockProducts(array $productIds, string $reason, User $user): array
    {
        $lockedCount = 0;
        $failedCount = 0;

        DB::beginTransaction();
        try {
            foreach ($productIds as $productId) {
                $product = Product::find($productId);

                if (!$product) {
                    $failedCount++;
                    continue;
                }

                // 이미 잠금된 상품인지 확인
                if ($product->isLocked()) {
                    $failedCount++;
                    continue;
                }

                // 잠금 처리
                $product->update([
                    'is_locked' => true,
                    'locked_by' => $user->id,
                    'locked_at' => now(),
                    'memo' => $reason,
                ]);

                // 로그 기록
                SimpleLogService::info('product', '상품 잠금 처리', [
                    'product_id' => $product->id,
                    'qaid' => $product->qaid,
                    'locked_by' => $user->name,
                    'reason' => $reason,
                ]);

                $lockedCount++;
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }

        return [
            'locked_count' => $lockedCount,
            'failed_count' => $failedCount,
        ];
    }

    /**
     * 상품들의 잠금 해제
     * @throws Exception
     */
    public function unlockProducts(array $productIds, User $user): array
    {
        $unlockedCount = 0;

        DB::beginTransaction();
        try {
            foreach ($productIds as $productId) {
                $product = Product::find($productId);

                if (!$product || !$product->isLocked()) {
                    continue;
                }

                // 잠금 해제
                $product->update([
                    'is_locked' => false,
                    'locked_by' => null,
                    'locked_at' => null,
                    'memo' => null,
                ]);

                // 로그 기록
                SimpleLogService::info('product_unlock', '상품 잠금 해제', [
                    'product_id' => $product->id,
                    'qaid' => $product->qaid,
                    'unlocked_by' => $user->name,
                ]);

                $unlockedCount++;
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }

        return ['unlocked_count' => $unlockedCount];
    }

    /**
     * 잠금된 상품 목록 조회
     */
    public function getLockedProducts(int $pageSize = 16): LengthAwarePaginator|array
    {
        return Product::with(['lockedByUser:id,name', 'req:id,req_at'])
            ->where('is_locked', true)
            ->orderBy('locked_at', 'desc')
            ->paginate($pageSize);
    }
}
