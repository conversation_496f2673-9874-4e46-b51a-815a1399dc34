<?php

namespace App\Services;

use App\Models\WorkStatus;
use App\Repositories\WorkCategoryRepository;
use App\Repositories\WorkActionRepository;
use App\Repositories\WorkStatusTemplateRepository;
use App\Repositories\WorkStatusRepository;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Throwable;

class DynamicWorkStatusService
{
    /**
     * 캐시 TTL (초)
     */
    private const CACHE_TTL = 3600;

    public function __construct(
        private readonly WorkCategoryRepository $workCategoryRepository,
        private readonly WorkActionRepository $workActionRepository,
        private readonly WorkStatusTemplateRepository $workStatusTemplateRepository,
        private readonly WorkStatusRepository $workStatusRepository
    ) {}

    /**
     * 컨텍스트 기반 상태 생성 및 조회
     * @throws Exception
     */
    public function getOrCreateStatus(string $categoryCode, string $actionCode, array $context = []): WorkStatus
    {
        try {
            // 캐시 키 생성
            $cacheKey = $this->generateCacheKey($categoryCode, $actionCode, $context);

            return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($categoryCode, $actionCode, $context) {
                return $this->createStatusFromTemplate($categoryCode, $actionCode, $context);
            });
        } catch (Exception $e) {
            SimpleLogService::error('work_status', '동적 WorkStatus: 상태 생성 또는 조회 실패', [
                'category_code' => $categoryCode,
                'action_code' => $actionCode,
            ], $e);

            throw $e;
        }
    }

    /**
     * 템플릿 기반 상태 생성
     * @throws Exception|Throwable
     */
    private function createStatusFromTemplate(string $categoryCode, string $actionCode, array $context = []): WorkStatus
    {
        // 카테고리와 액션 조회
        $category = $this->workCategoryRepository->findByCode($categoryCode);
        if (!$category) {
            throw new Exception("카테고리를 찾을 수 없습니다: {$categoryCode}");
        }

        $action = $this->workActionRepository->findByCategoryAndCode($category->id, $actionCode);
        if (!$action) {
            throw new Exception("액션을 찾을 수 없습니다: {$categoryCode}.{$actionCode}");
        }

        // 템플릿 조회 (조건에 맞는 최적의 템플릿 찾기)
        $template = $this->workStatusTemplateRepository->findBestMatch($category->id, $action->id, $context);

        if (!$template) {
            // 기본 템플릿 생성
            $template = $this->createDefaultTemplate($category, $action, $context);
        }

        // 링크 코드 생성
        $linkCode = $this->generateLinkCode($categoryCode, $actionCode, $context);

        // WorkStatus 생성 또는 조회
        return DB::transaction(function() use ($category, $action, $template, $linkCode, $context) {
            $existingStatus = $this->workStatusRepository->findByLinkCode($linkCode);

            if ($existingStatus) {
                return $existingStatus;
            }

            return $this->workStatusRepository->create([
                'category_id' => $category->id,
                'action_id' => $action->id,
                'template_id' => $template->id,
                'link_code' => $linkCode,
                'code' => $linkCode,
                'name' => $this->workStatusRepository->generateName($context),
                'description' => $this->workStatusTemplateRepository->generateDescription($template, $context),
                'auto_generated' => true,
                'generation_context' => $context,
            ]);
        });
    }

    /**
     * 링크 코드 생성
     */
    private function generateLinkCode(string $categoryCode, string $actionCode, array $context): string
    {
        $parts = [strtoupper($categoryCode), strtoupper($actionCode)];

        // 컨텍스트에서 추가 정보 추출
        $contextOrder = ['detail', 'symptom_code', 'process_code', 'grade_code', 'defect_type'];

        foreach ($contextOrder as $key) {
            if (isset($context[$key]) && !empty($context[$key])) {
                $parts[] = strtoupper($context[$key]);
            }
        }

        return implode('_', $parts);
    }

    /**
     * 캐시 키 생성
     */
    private function generateCacheKey(string $categoryCode, string $actionCode, array $context): string
    {
        $contextHash = md5(json_encode($context));
        return "work_status:{$categoryCode}:{$actionCode}:{$contextHash}";
    }

    /**
     * 기본 템플릿 생성
     */
    private function createDefaultTemplate($category, $action, array $context)
    {
        SimpleLogService::info('work_status', '동적 WorkStatus: 기본 템플릿 생성', [
            'category_code' => $category->code,
            'action_code' => $action->code,
            'context' => $context
        ]);

        return $this->workStatusTemplateRepository->create([
            'category_id' => $category->id,
            'action_id' => $action->id,
            'template_code' => 'DEFAULT',
            'name' => $action->name,
            'description' => "자동 생성된 기본 템플릿: {$category->name} - {$action->name}",
            'is_active' => true,
            'created_by' => 1, // 시스템 사용자
        ]);
    }

    /**
     * 특정 조건으로 생성된 상태 목록 조회
     */
    public function getStatusesByCondition(string $categoryCode, string $actionCode, array $context = []): array
    {
        try {
            $category = $this->workCategoryRepository->findByCode($categoryCode);
            if (!$category) {
                return [];
            }

            $action = $this->workActionRepository->findByCategoryAndCode($category->id, $actionCode);
            if (!$action) {
                return [];
            }

            return $this->workStatusRepository->getAutoGeneratedByCondition($category->id, $action->id);
        } catch (Exception $e) {
            SimpleLogService::error('work_status', '동적 WorkStatus: 조건별 상태 조회 실패', [
                'category_code' => $categoryCode,
                'action_code' => $actionCode,
            ], $e);

            return [];
        }
    }

    /**
     * 카테고리별 자동 생성된 상태 통계
     */
    public function getGeneratedStatusStats(string $categoryCode): array
    {
        try {
            $category = $this->workCategoryRepository->findByCode($categoryCode);
            if (!$category) {
                return [];
            }

            return $this->workStatusRepository->getStatsByCategory($category->id);
        } catch (Exception $e) {
            SimpleLogService::error('work_status', '동적 WorkStatus: 카테고리별 상태 통계 조회 실패', [
                'category_code' => $categoryCode,
            ], $e);

            return [];
        }
    }

    /**
     * 하드코딩된 상수를 동적 시스템으로 변환
     */
    public function convertLegacyConstants(): array
    {
        $convertedCount = 0;
        $errors = [];

        // 기존 하드코딩된 상수 매핑
        $legacyMappings = [
            'REPAIR_WAITING' => ['REPAIR', 'WAITING'],
            'REPAIR_COMPLETE' => ['REPAIR', 'COMPLETE'],
            'REPAIR_XL1' => ['REPAIR', 'SYMPTOM', ['symptom_code' => 'CH_BROKEN']],
            'REPAIR_XL2' => ['REPAIR', 'SYMPTOM', ['symptom_code' => 'CH_STAIN']],
            'REPAIR_XL3' => ['REPAIR', 'SYMPTOM', ['symptom_code' => 'CH_COMPO']],
            'REPAIR_XL4' => ['REPAIR', 'SYMPTOM', ['symptom_code' => 'CH_RUN']],
            'REPAIR_XL5' => ['REPAIR', 'SYMPTOM', ['symptom_code' => 'CH_DIFFER']],
            'REPAIR_XL6' => ['REPAIR', 'SYMPTOM', ['symptom_code' => 'CH_EMPTY']],
            'REPAIR_NORMAL' => ['REPAIR', 'GRADE', ['grade_code' => 'NORMAL']],
            'REPAIR_GOOD' => ['REPAIR', 'GRADE', ['grade_code' => 'GOOD']],
            'REPAIR_BEST' => ['REPAIR', 'GRADE', ['grade_code' => 'BEST']],
            'REPAIR_PROCESS1' => ['REPAIR', 'PROCESS', ['process_code' => 'RP_PART']],
            'REPAIR_PROCESS2' => ['REPAIR', 'PROCESS', ['process_code' => 'RP_POWER']],
            'REPAIR_PROCESS3' => ['REPAIR', 'PROCESS', ['process_code' => 'RP_CLEAN']],
            'REPAIR_PROCESS4' => ['REPAIR', 'PROCESS', ['process_code' => 'RP_SCREEN']],
            'REPAIR_PROCESS5' => ['REPAIR', 'PROCESS', ['process_code' => 'RP_BATTERY']],
            'REPAIR_PROCESS6' => ['REPAIR', 'PROCESS', ['process_code' => 'RP_MAINBOARD']],
            'REPAIR_PROCESS7' => ['REPAIR', 'PROCESS', ['process_code' => 'RP_CAMERA']],
            'REPAIR_PROCESS8' => ['REPAIR', 'PROCESS', ['process_code' => 'RP_SPEAKER']],
            'REPAIR_PROCESS9' => ['REPAIR', 'PROCESS', ['process_code' => 'RP_VIBRATION']],
            'PALLET_LOADED' => ['PALLET', 'LOADED'],
            'PALLET_CLOSED' => ['PALLET', 'CLOSED'],
            'PALLET_SHIPPED' => ['PALLET', 'SHIPPED'],
            'CARRYOUT_OUT' => ['CARRYOUT', 'OUT'],
            'CARRYOUT_IN' => ['CARRYOUT', 'IN'],
        ];

        foreach ($legacyMappings as $legacyConstant => $mapping) {
            try {
                $categoryCode = $mapping[0];
                $actionCode = $mapping[1];
                $context = $mapping[2] ?? [];

                $this->getOrCreateStatus($categoryCode, $actionCode, $context);
                $convertedCount++;
            } catch (Exception $e) {
                SimpleLogService::error('work_status', '동적 WorkStatus: 레거시 상수 변환 실패', [
                    'constant' => $legacyConstant,
                ], $e);

                $errors[] = [
                    'constant' => $legacyConstant,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'converted_count' => $convertedCount,
            'total_count' => count($legacyMappings),
            'errors' => $errors
        ];
    }

    /**
     * 캐시 초기화
     */
    public function clearCache(): void
    {
        Cache::flush();
        SimpleLogService::info('work_status', '동적 WorkStatus: 캐시 초기화 완료');
    }

    /**
     * 상태 생성 통계 조회
     */
    public function getGenerationStats(): array
    {
        try {
            $stats = $this->workStatusRepository->getGenerationStats();

            return [
                'total_generated' => $stats['total_generated'] ?? 0,
                'categories' => $stats['categories'] ?? [],
                'templates_used' => $stats['templates_used'] ?? 0,
                'recent_generation_count' => $stats['recent_generation_count'] ?? 0,
            ];
        } catch (Exception $e) {
            SimpleLogService::error('work_status', '동적 WorkStatus: 상태 생성 통계 조회 실패', [], $e);

            return [
                'total_generated' => 0,
                'categories' => [],
                'templates_used' => 0,
                'recent_generation_count' => 0,
            ];
        }
    }

    /**
     * 최근 생성된 상태 조회
     */
    public function getRecentlyGeneratedStatuses(int $limit = 10): Collection|array
    {
        try {
            return $this->workStatusRepository->getRecent($limit);
        } catch (Exception $e) {
            SimpleLogService::error('work_status', '동적 WorkStatus: 최근 생성된 상태 조회 실패', [
                'limit' => $limit,
            ], $e);

            return [];
        }
    }

    /**
     * 템플릿별 상태 사용 횟수 조회
     */
    public function getStatusCountByTemplate(int $templateId): int
    {
        try {
            return $this->workStatusRepository->getCountByTemplate($templateId);
        } catch (Exception $e) {
            SimpleLogService::error('work_status', '동적 WorkStatus: 템플릿별 상태 사용 횟수 조회 실패', [
                'template_id' => $templateId,
            ], $e);

            return 0;
        }
    }

    /**
     * 사용되지 않는 템플릿 조회
     */
    public function getUnusedTemplates(): Collection|array
    {
        try {
            return $this->workStatusTemplateRepository->getUnusedTemplates();
        } catch (Exception $e) {
            SimpleLogService::error('work_status', '동적 WorkStatus: 사용되지 않는 템플릿 조회 실패', [], $e);

            return [];
        }
    }

    /**
     * 자주 사용되는 상태 조회
     */
    public function getFrequentlyUsedStatuses(int $limit = 10): Collection|array
    {
        try {
            return $this->workStatusRepository->getFrequentlyUsed($limit);
        } catch (Exception $e) {
            SimpleLogService::error('work_status', '동적 WorkStatus: 자주 사용되는 상태 조회 실패', [], $e);

            return [];
        }
    }
}
