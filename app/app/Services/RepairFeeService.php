<?php

namespace App\Services;

use App\Models\RepairCategory;
use App\Models\RepairFee;
use App\Models\RepairFeeRange;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Throwable;

class RepairFeeService
{
    public function getRepairOptions(): array
    {
        return [
            'categories' => (new CategoryService())->getAllCategories(),
            'repair_range_types' => $this->getRepairRangeTypes(),
            'repair_range_models' => $this->getRepairRangeModels(),
            'repair_range_fee_types' => $this->getRepairRangeFeeTypes(),
            'repair_range_fee_units' => $this->getRepairRangeFeeUnits(),
            'repair_types' => $this->getRepairTypes()
        ];
    }

    private function getRepairRangeTypes(): array
    {
        return $this->formatOptions(
            RepairFeeRange::REPAIR_RANGE_TYPE,
            RepairFeeRange::$REPAIR_RANGE_TYPE_NAME
        );
    }

    private function getRepairRangeModels(): array
    {
        return $this->formatOptions(
            RepairFeeRange::REPAIR_RANGE_MODEL,
            RepairFeeRange::$REPAIR_RANGE_MODEL_NAME
        );
    }

    private function getRepairRangeFeeTypes(): array
    {
        return $this->formatOptions(
            RepairFeeRange::REPAIR_RANGE_FEE_TYPE,
            RepairFeeRange::$REPAIR_RANGE_FEE_TYPE_NAME
        );
    }

    private function getRepairRangeFeeUnits(): array
    {
        return $this->formatOptions(
            RepairFeeRange::REPAIR_RANGE_FEE_UNIT,
            RepairFeeRange::$REPAIR_RANGE_FEE_UNIT_NAME
        );
    }

    private function getRepairTypes(): array
    {
        return $this->formatOptions(
            RepairFee::REPAIR_TYPE,
            RepairFee::$REPAIR_TYPE_NAME
        );
    }

    private function formatOptions(array $types, array $names): array
    {
        return array_map(
            fn($type) => [
                'id' => $type,
                'name' => $names[$type]
            ],
            array_values($types)
        );
    }

    /**
     * 수리비 내역을 검색하여 가져오는 메서드
     *
     * @param array $data 검색 조건
     */
    public function getRepairFeeList(array $data): Builder
    {
        $query = RepairFee::query()
            ->select([
                'repair_fees.*',
                'repair_fee_ranges.type as range_type',
                'repair_fee_ranges.model as range_model',
                'repair_fee_ranges.fee_type as range_fee_type',
                'repair_fee_ranges.fee_unit as range_fee_unit',
                'repair_fee_ranges.min_value',
                'repair_fee_ranges.max_value',
                'repair_categories.cate4_id',
                'repair_categories.cate5_id',
                'cate4.name as cate4_name',
                'cate5.name as cate5_name',
            ])
            ->join('repair_fee_ranges', 'repair_fees.repair_fee_range_id', '=', 'repair_fee_ranges.id')
            ->join('repair_categories', 'repair_fee_ranges.repair_category_id', '=', 'repair_categories.id')
            ->leftJoin('cate4', 'cate4.id', '=', 'repair_categories.cate4_id')
            ->leftJoin('cate5', 'cate5.id', '=', 'repair_categories.cate5_id');

        // 카테고리 필터링
        if (!empty($data['cate4'])) {
            $query->where('repair_categories.cate4_id', $data['cate4']);
        }

        if (!empty($data['cate5'])) {
            $query->where('repair_categories.cate5_id', $data['cate5']);
        }

        // 수리비 범위 타입 필터링
        if (!empty($data['repair_range_type'])) {
            $query->where('repair_fee_ranges.type', $data['repair_range_type']);
        }

        // 수리비 범위 모델 필터링
        if (!empty($data['repair_range_model'])) {
            $query->where('repair_fee_ranges.model', $data['repair_range_model']);
        }

        // 수리비 범위 요금 타입 필터링
        if (!empty($data['repair_range_fee_type'])) {
            $query->where('repair_fee_ranges.fee_type', $data['repair_range_fee_type']);
        }

        // 수리비 범위 요금 단위 필터링
        if (!empty($data['repair_range_fee_unit'])) {
            $query->where('repair_fee_ranges.fee_unit', $data['repair_range_fee_unit']);
        }

        // 수리 타입 필터링
        if (!empty($data['repair_type'])) {
            $query->where('repair_fees.repair_type', $data['repair_type']);
        }

        // 정렬
        return $query->orderBy('cate4.name')
            ->orderBy('cate5.name')
            ->orderBy('repair_fee_ranges.type')
            ->orderBy('repair_fee_ranges.model')
            ->orderBy('repair_fee_ranges.min_value');
    }

    /**
     * DB 결과에서 코드값을 한글 이름으로 변환
     *
     * @param  LengthAwarePaginator  $items 페이지네이션 결과
     * @return LengthAwarePaginator
     */
    public function translateCodeToName(LengthAwarePaginator $items): LengthAwarePaginator
    {
        $items->getCollection()->transform(function ($item) {
            // min_value, max_value 변환 및 단위 추가
            if (isset($item->min_value)) {
                // 소숫점이 없는 경우(정수인 경우) 정수로 변환
                $item->min_value = $this->formatNumberWithUnit($item->min_value, $item->range_fee_unit);
            }

            if (isset($item->max_value)) {
                // 소숫점이 없는 경우(정수인 경우) 정수로 변환
                $item->max_value = $this->formatNumberWithUnit($item->max_value, $item->range_fee_unit);
            }

            // range_type 변환 (general -> 일반)
            if (isset($item->range_type)) {
                $item->range_type = RepairFeeRange::$REPAIR_RANGE_TYPE_NAME[$item->range_type] ?? $item->range_type;
            }

            // range_model 변환
            if (isset($item->range_model)) {
                $item->range_model = RepairFeeRange::$REPAIR_RANGE_MODEL_NAME[$item->range_model] ?? $item->range_model;
            }

            // range_fee_type 변환
            if (isset($item->range_fee_type)) {
                $item->range_fee_type = RepairFeeRange::$REPAIR_RANGE_FEE_TYPE_NAME[$item->range_fee_type] ?? $item->range_fee_type;
            }

            // range_fee_unit 변환
            if (isset($item->range_fee_unit)) {
                $item->range_fee_unit = RepairFeeRange::$REPAIR_RANGE_FEE_UNIT_NAME[$item->range_fee_unit] ?? $item->range_fee_unit;
            }

            // repair_type 변환
            if (isset($item->repair_type)) {
                $item->repair_type = RepairFee::$REPAIR_TYPE_NAME[$item->repair_type] ?? $item->repair_type;
            }

            // $item->amount = number_format($item->amount);

            return $item;
        });

        return $items;
    }

    /**
     * 숫자 값을 형식화하고 단위를 추가합니다
     *
     * @param mixed $value 값
     * @param  string  $unitCode 단위 코드
     * @return string 형식화된 문자열
     */
    private function formatNumberWithUnit(mixed $value, string $unitCode): string
    {
        // // 단위 이름 가져오기
        // $unitName = '';
        // if ($unitCode === RepairFeeRange::REPAIR_RANGE_FEE_UNIT['WON']) {
        //     $unitName = '원';
        // } elseif ($unitCode === RepairFeeRange::REPAIR_RANGE_FEE_UNIT['CM']) {
        //     $unitName = 'cm';
        // } elseif ($unitCode === RepairFeeRange::REPAIR_RANGE_FEE_UNIT['INCH']) {
        //     $unitName = '인치';
        // }

        // 소수점이 의미 있는 경우에만 표시
        if (is_numeric($value)) {
            $formatted = (string)$value;
            if (strpos($formatted, '.') !== false) {
                // 소수점 이하 불필요한 0 제거
                $formatted = rtrim(rtrim($formatted, '0'), '.');
                // // 천 단위 구분자 추가
                // $parts = explode('.', $formatted);
                // $parts[0] = number_format((int)$parts[0]);
                // $formatted = implode('.', $parts);
            } else {
                // $formatted = number_format((int)$value);
                $formatted = (string)$value;
            }
            // return $formatted . $unitName;
            return $formatted;
        }

        // return number_format($value) . $unitName;
        return $value;
    }

    /**
     * @param  array  $data
     * @return array
     * @throws Throwable
     */
    public function storeRepairFee(array $data): array
    {
        try {
            DB::beginTransaction();

            $category = RepairCategory::firstOrNew([
                'cate4_id' => $data['cate4'],
                'cate5_id' => $data['cate5'],
            ]);
            $category->save();

            $repairRange = RepairFeeRange::firstOrNew([
                'repair_category_id' => $category->id,
                'type' => $data['repair_range_type'],
                'model' => $data['repair_range_model'],
                'fee_type' => $data['repair_range_fee_type'],
                'fee_unit' => $data['repair_range_fee_unit'],
                'min_value' => $data['repair_range_min_value'],
                'max_value' => $data['repair_range_max_value'],
            ]);
            $repairRange->save();

            $repairFee = RepairFee::firstOrNew([
                'repair_fee_range_id' => $repairRange->id,
                'repair_type' => $data['repair_type'],
                'amount' => $data['repair_amount'],
            ]);
            $repairFee->save();

            DB::commit();

            return [
                'category' => $category,
                'repairRange' => $repairRange,
                'repairFee' => $repairFee,
            ];
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
    }

    /**
     * @param  array  $data
     * @return bool
     * @throws Throwable
     */
    public function updateRepairFee(array $data): bool
    {
        try {
            DB::beginTransaction();

            $repairFeeRange = RepairFeeRange::find($data['range_id']);
            $repairFeeRange->update([
                'min_value' => $data['min_value'],
                'max_value' => $data['max_value'],
                // 'memo' => $data['memo'] ?? null,
            ]);

            $repairFee = RepairFee::find($data['fee_id']);
            $repairFee->update([
                'amount' => $data['amount'],
            ]);

            DB::commit();

            return true;
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
    }

    /**
     * Deletes a repair fee record identified by its ID.
     *
     * @param  int  $id  The ID of the repair fee to be deleted.
     * @return bool Returns true if the repair fee was successfully deleted.
     * @throws Exception|Throwable If an error occurs during the operation.
     */
    public function destroyRepairFee(int $id): bool
    {
        try {
            DB::beginTransaction();

            $repairFee = RepairFee::findOrFail($id);
            $repairFee->delete();

            DB::commit();

            return true;
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
    }
}
