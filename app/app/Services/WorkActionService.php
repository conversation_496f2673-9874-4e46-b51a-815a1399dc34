<?php

namespace App\Services;

use App\Models\WorkAction;
use App\Repositories\Interfaces\WorkActionRepositoryInterface;
use App\Repositories\Interfaces\WorkCategoryRepositoryInterface;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use Throwable;

class WorkActionService
{
    protected WorkActionRepositoryInterface $actionRepository;
    protected WorkCategoryRepositoryInterface $categoryRepository;

    public function __construct(
        WorkActionRepositoryInterface $actionRepository,
        WorkCategoryRepositoryInterface $categoryRepository
    ) {
        $this->actionRepository = $actionRepository;
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * 모든 활성 액션 조회
     */
    public function getAllActiveActions(): Collection
    {
        return Cache::remember('work_actions_active', 3600, function () {
            return $this->actionRepository->getAllActive();
        });
    }

    /**
     * 카테고리별 활성 액션 조회
     */
    public function getActionsByCategory(int $categoryId): Collection
    {
        return Cache::remember("work_actions_category_{$categoryId}", 3600, function () use ($categoryId) {
            return $this->actionRepository->findActiveByCategoryId($categoryId);
        });
    }

    /**
     * 최상위 액션들 조회
     */
    public function getTopLevelActions(int $categoryId): Collection
    {
        return $this->actionRepository->getTopLevelActions($categoryId);
    }

    /**
     * 자식 액션들 조회
     */
    public function getChildrenActions(int $parentId): Collection
    {
        return $this->actionRepository->getChildrenActions($parentId);
    }

    /**
     * 관계 포함 액션 목록 조회
     */
    public function getActionsWithRelations(): Collection
    {
        return $this->actionRepository->getAllWithRelations();
    }

    /**
     * 통계 정보 포함 액션 목록 조회
     */
    public function getActionsWithCounts(): Collection
    {
        return $this->actionRepository->getAllWithCounts();
    }

    /**
     * 액션 조회 (ID로)
     */
    public function findById(int $id): ?WorkAction
    {
        return $this->actionRepository->findById($id);
    }

    /**
     * 액션 조회 (카테고리와 코드로)
     */
    public function findByCategoryAndCode(int $categoryId, string $code): ?WorkAction
    {
        return $this->actionRepository->findByCategoryAndCode($categoryId, $code);
    }

    /**
     * 활성 액션 조회 (카테고리와 코드로)
     */
    public function findActiveByCategoryAndCode(int $categoryId, string $code): ?WorkAction
    {
        return $this->actionRepository->findActiveByCategoryAndCode($categoryId, $code);
    }

    /**
     * 액션 생성
     * @throws Exception|Throwable
     */
    public function createAction(array $data): WorkAction
    {
        // 카테고리 존재 확인
        $category = $this->categoryRepository->findById($data['category_id']);
        if (!$category) {
            throw new InvalidArgumentException('카테고리를 찾을 수 없습니다.');
        }

        // 순환 참조 체크
        if (isset($data['parent_id'])) {
            if ($this->actionRepository->checkCircularReference($data['category_id'], $data['parent_id'])) {
                throw new InvalidArgumentException('순환 참조가 발생합니다.');
            }
        }

        // 카테고리 + 코드 중복 체크
        if ($this->actionRepository->findByCategoryAndCode($data['category_id'], $data['code'])) {
            throw new InvalidArgumentException('이미 존재하는 액션 코드입니다.');
        }

        DB::beginTransaction();
        try {
            $action = $this->actionRepository->create($data);

            // 캐시 초기화
            $this->clearCache();

            DB::commit();

            SimpleLogService::debug('work_status', '액션 생성 완료', [
                'action_id' => $action->id,
                'category_id' => $action->category_id,
                'code' => $action->code,
                'name' => $action->name
            ]);

            return $action;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 액션 수정
     * @throws Exception|Throwable
     */
    public function updateAction(int $id, array $data): WorkAction
    {
        $action = $this->actionRepository->findById($id);
        if (!$action) {
            throw new InvalidArgumentException('액션을 찾을 수 없습니다.');
        }

        // 카테고리 존재 확인
        if (isset($data['category_id'])) {
            $category = $this->categoryRepository->findById($data['category_id']);
            if (!$category) {
                throw new InvalidArgumentException('카테고리를 찾을 수 없습니다.');
            }
        }

        // 순환 참조 체크
        if (isset($data['parent_id']) && $data['parent_id'] != $action->parent_id) {
            if ($this->actionRepository->checkCircularReference($id, $data['parent_id'])) {
                throw new InvalidArgumentException('순환 참조가 발생합니다.');
            }
        }

        // 카테고리 + 코드 중복 체크
        if (isset($data['code']) || isset($data['category_id'])) {
            $categoryId = $data['category_id'] ?? $action->category_id;
            $code = $data['code'] ?? $action->code;

            $existingAction = $this->actionRepository->findByCategoryAndCode($categoryId, $code);
            if ($existingAction && $existingAction->id !== $id) {
                throw new InvalidArgumentException('이미 존재하는 액션 코드입니다.');
            }
        }

        DB::beginTransaction();
        try {
            $action = $this->actionRepository->update($id, $data);

            // 캐시 초기화
            $this->clearCache();

            DB::commit();

            SimpleLogService::debug('work_status', '액션 수정 완료', [
                'action_id' => $action->id,
                'category_id' => $action->category_id,
                'code' => $action->code,
                'name' => $action->name
            ]);

            return $action;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 액션 삭제
     * @throws Exception|Throwable
     */
    public function deleteAction(int $id): bool
    {
        $action = $this->actionRepository->findById($id);
        if (!$action) {
            throw new InvalidArgumentException('액션을 찾을 수 없습니다.');
        }

        DB::beginTransaction();
        try {
            $result = $this->actionRepository->delete($id);

            // 캐시 초기화
            $this->clearCache();

            DB::commit();

            SimpleLogService::debug('work_status', '액션 삭제 완료', [
                'action_id' => $id,
                'code' => $action->code,
                'name' => $action->name
            ]);

            return $result;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 액션 활성화
     * @throws
     */
    public function activateAction(int $id): WorkAction
    {
        return $this->updateAction($id, ['is_active' => true]);
    }

    /**
     * 액션 비활성화
     * @throws
     */
    public function deactivateAction(int $id): WorkAction
    {
        $action = $this->actionRepository->findById($id);
        if (!$action) {
            throw new InvalidArgumentException('액션을 찾을 수 없습니다.');
        }

        if (!$this->actionRepository->canDeactivate($id)) {
            throw new InvalidArgumentException('관련된 활성 데이터가 있어 비활성화할 수 없습니다.');
        }

        return $this->updateAction($id, ['is_active' => false]);
    }

    /**
     * 액션 순서 변경
     * @throws
     */
    public function updateActionOrder(int $id, int $sortOrder): WorkAction
    {
        return $this->updateAction($id, ['sort_order' => $sortOrder]);
    }

    /**
     * 액션 계층 구조 조회
     */
    public function getActionHierarchy(int $categoryId): Collection
    {
        $topLevelActions = $this->getTopLevelActions($categoryId);

        return $topLevelActions->map(function ($action) {
            return $this->buildActionTree($action);
        });
    }

    /**
     * 액션 트리 구조 생성
     */
    private function buildActionTree(WorkAction $action): array
    {
        $children = $this->getChildrenActions($action->id);

        return [
            'id' => $action->id,
            'code' => $action->code,
            'name' => $action->name,
            'description' => $action->description,
            'sort_order' => $action->sort_order,
            'is_active' => $action->is_active,
            'children' => $children->map(function ($child) {
                return $this->buildActionTree($child);
            })->toArray()
        ];
    }

    /**
     * 액션 통계 조회
     */
    public function getActionStats(): array
    {
        $activeCount = $this->actionRepository->getAllActive()->count();
        $totalCount = $this->actionRepository->getAll()->count();
        $inactiveCount = $totalCount - $activeCount;

        return [
            'active_count' => $activeCount,
            'inactive_count' => $inactiveCount,
            'total_count' => $totalCount,
        ];
    }

    /**
     * 캐시 초기화
     */
    private function clearCache(): void
    {
        Cache::forget('work_actions_active');

        // 카테고리별 캐시 초기화
        $categories = $this->categoryRepository->getAll();
        foreach ($categories as $category) {
            Cache::forget("work_actions_category_{$category->id}");
        }

        Cache::tags(['work_actions'])->flush();
    }
}
