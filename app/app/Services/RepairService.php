<?php

namespace App\Services;

use App\Models\Product;
use App\Models\RepairFee;
use App\Models\RepairProduct;
use App\Models\WorkStatus;
use App\Traits\Repair\AppleTrait;
use App\Traits\Repair\FeeTypeTrait;
use App\Traits\Repair\GeneralTrait;
use App\Traits\Repair\GradeTrait;
use App\Traits\Repair\MonitorTrait;
use App\Traits\Repair\ProductPartsTrait;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class RepairService
{
    use MonitorTrait, AppleTrait, GeneralTrait;
    use GradeTrait, FeeTypeTrait , ProductPartsTrait;

    private CountService $countService;
    private WorkStatusService $workStatusService;
    private ProductLogService $logger;
    private LoadedService $loadedService;

    /**
     * 생성자
     */
    public function __construct(
        CountService $countService,
        WorkStatusService $workStatusService,
        ProductLogService $logger,
        LoadedService $loadedService
    ) {
        $this->countService = $countService;
        $this->workStatusService = $workStatusService;
        $this->logger = $logger;
        $this->loadedService = $loadedService;
    }

    // ========================================
    // 공개 메서드 (Public Methods)
    // ========================================

    /**
     * 수리내역 저장: waiting
     * @param array $data
     * @param Product $product
     * @return void
     * @throws Exception|Throwable
     */
    public function waiting(array $data, Product $product): void
    {
        try {
            DB::beginTransaction();

            $user = auth()->user();
            $now = now();
            $data['status'] = RepairProduct::STATUS_WAITING;
            $data['user_id'] = $user->id;
            $data['now'] = $now;

            // 수리대기(구성품 신청) 상태 변경
            $product->update([
                'status' => Product::STATUS_WAITING,
            ]);

            // 로그 저장을 위한 상태 id
            $data['log_mode'] = 'repair_waiting'; // 로그에 수리 대기 찍히게
            $statusIds = $this->getStatusIds($data);

            foreach ($statusIds as $statusId) {
                $this->logger->addLog($product, 'App\Models\Product', $product->id, $statusId, $user->id);
            }

            $memo = $data['memo'] ?? null;
            $invoice1 = $this->calculateInvoice1($product, $data); # 수리비 계산
            $invoice3 = $this->calculateInvoice3($product, $data); # OS 재설치비
            $repairFeeInfo = $this->getRepairFeeInfo($product, $data); # 수리비 정보 가져오기

            if ($invoice3 > 0) {
                $formattedInvoice3 = number_format($invoice3);
                $logMemo = "[$product->qaid]$product->name<br>OS 재설치비: $formattedInvoice3 원";

                // 안전한 배열 접근을 위해 isset() 체크 추가
                if (isset($statusIds[WorkStatus::LINK_REPAIR_OS_REINSTALL])) {
                    $this->logger->addLog($product, 'App\Models\RepairProduct', $product->id, $statusIds[WorkStatus::LINK_REPAIR_OS_REINSTALL], $user->id, $logMemo);
                }
                unset($logMemo);

                $memo .= "OS설치 : $formattedInvoice3 원";
            }

            // 수리내역 저장
            $data['invoice1'] = $invoice1;
            $data['invoice3'] = $invoice3;
            $data['memo'] = $memo;
            $data['repair_fee_info'] = $repairFeeInfo;

            $repairProduct = $this->createRepairProduct($product, $data);

            if (isset($statusIds[WorkStatus::LINK_REPAIR_WAITING])) {
                $this->logger->addLog($product, 'App\Models\RepairProduct', $repairProduct->id, $statusIds[WorkStatus::LINK_REPAIR_WAITING], $user->id, "수리/점검 대기(구성품 신청 중)");
            }

            // 구성품 처리 추가 - complete 메서드와 동일한 코드
            $this->updateRepairProductParts($repairProduct, $data, $user, $this->workStatusService, $this->logger);

            // 구성품 총 비용 계산 후 invoice2 업데이트
            $invoice2 = $repairProduct->parts_total_cost;
            $repairProduct->update(['invoice2' => $invoice2]);

            // 수리대기(구성품 신청) 카운팅<br>
            // 입고검수완료 : checked - 1, 수리대기(구성품 신청) : waiting + 1
            $counters[$product->req_id] = [
                'checked' => -1,
                'waiting' => 1
            ];

            $this->countService->multipleUpdate($counters);
            $this->logger->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('product', '수리/점검 저장 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 수리내역 저장
     * waiting, complete 에 따라 처리 되어야 함.
     * 특히 waiting 이었다가 complete 로 바뀌는 경우 등급 같은 것은 로그에 저장하지 않아도 됨
     * @param array $data
     * @param Product $product
     * @return void
     * @throws Exception|Throwable
     */
    public function complete(array $data, Product $product): void
    {
        try {
            DB::beginTransaction();

            $user = auth()->user();
            $now = now();
            $data['status'] = RepairProduct::STATUS_REPAIRED;
            $data['user_id'] = $user->id;
            $data['now'] = $now;

            // 수리/점검 완료 상품 상태 업데이트
            $product->update([
                'checked_status' => Product::CHECKED_STATUS_CHECKED,
                'status' => Product::STATUS_REPAIRED,
            ]);

            // 로그 저장을 위한 상태 id
            $data['log_mode'] = 'repair_complete'; // 로그에 수리 완료 찍히게
            $statusIds = $this->getStatusIds($data);

            // repair_product_id 확인
            $repairProductId = $data['repair_product_id'] ?? null;
            $repairProduct = $repairProductId ? RepairProduct::find($repairProductId) : null;

            if ($repairProduct === null) {
                // 새 수리 등록 (바로 complete 처리)
                foreach ($statusIds as $statusId) {
                    SimpleLogService::debug('daily', '수리 완료', [
                        'product_id' => $product->id,
                        'qaid' => $product->qaid,
                        'status_id' => $statusId,
                    ]);
                    $this->logger->addLog($product, 'App\Models\Product', $product->id, $statusId, $user->id);
                }

                // 메모 및 청구금액 계산
                $memo = $data['memo'] ?? null;
                $invoice1 = $this->calculateInvoice1($product, $data);
                $invoice3 = $this->calculateInvoice3($product, $data);
                $repairFeeInfo = $this->getRepairFeeInfo($product, $data); # 수리비 정보 가져오기

                if ($invoice3 > 0) {
                    $formattedInvoice3 = number_format($invoice3);
                    // 안전한 배열 접근을 위해 isset() 체크 추가
                    if (isset($statusIds[WorkStatus::LINK_REPAIR_OS_REINSTALL])) {
                        $this->logger->addLog($product, 'App\Models\RepairProduct', $product->id, $statusIds[WorkStatus::LINK_REPAIR_OS_REINSTALL], $user->id, "[$product->qaid]$product->name<br>OS 재설치비: $formattedInvoice3 원");
                    }
                    $memo .= "OS설치 : $formattedInvoice3 원";
                }

                // 수리내역 저장
                $data['invoice1'] = $invoice1;
                $data['invoice3'] = $invoice3;
                $data['memo'] = $memo;
                $data['repair_fee_info'] = $repairFeeInfo;

                $repairProduct = $this->createRepairProduct($product, $data);

                // 카운터 업데이트
                $counters[$product->req_id] = [
                    'checked' => -1,
                    'repaired' => 1
                ];
            } else {
                if ($repairProduct->status === RepairProduct::STATUS_REPAIRED) {
                    // 이미 완료된 수리 내역에 대한 처리
                    SimpleLogService::info('repair', '이미 완료된 수리 내역 재요청', [
                        'repair_product_id' => $repairProduct->id,
                        'product_id' => $product->id,
                        'qaid' => $product->qaid
                    ]);

                    // 예외를 발생시켜서 처리 중단
                    throw new Exception("이미 수리/점검 완료된 상품입니다. 확인 부탁드립니다.");

                    // 또는 아래와 같이 구성품만 업데이트하고 상태는 변경하지 않음
                    // $this->updateRepairProductParts($repairProduct, $data, $this->workStatusService, $this->logger);

                    // 구성품 총 비용 계산 후 invoice2 업데이트
                    // $invoice2 = $repairProduct->parts_total_cost;
                    // $repairProduct->update(['invoice2' => $invoice2]);

                    // return; // 추가 처리 중단
                }

                // 대기상태에서 완료로 변경
                $memo = $data['memo'] ?? null;

                // 안전한 배열 접근을 위해 isset() 체크 추가
                if (isset($statusIds[WorkStatus::LINK_REPAIR_PROCESS5])) {
                    $this->logger->addLog($product, 'App\Models\Product', $product->id, $statusIds[WorkStatus::LINK_REPAIR_PROCESS5], $user->id, $memo);
                }

                if (isset($statusIds[WorkStatus::LINK_REPAIR_COMPLETE])) {
                    $this->logger->addLog($product, 'App\Models\Product', $product->id, $statusIds[WorkStatus::LINK_REPAIR_COMPLETE], $user->id, "수리/점검 완료");
                }

                // 수리 제품 정보 업데이트
                $repairProduct->update([
                    'status' => RepairProduct::STATUS_REPAIRED,
                    'completed_user_id' => $user->id,
                    'completed_at' => $now,
                    'repair_symptom_id' => $this->findSymptomIdByCode($data['symptom_code']),
                    'repair_process_id' => $this->findProcessIdByCode($data['process_code']),
                    'repair_grade_id' => $this->findGradeIdByCode($data['grade_code']),
                    'memo' => $memo,
                ]);

                // 카운터 업데이트
                $counters[$product->req_id] = [
                    'waiting' => -1,
                    'repaired' => 1
                ];
            }

            // 점검/수리 완료 로그
            // 안전한 배열 접근을 위해 isset() 체크 추가 (이미 위에서 체크했지만 재확인)
            if (isset($statusIds[WorkStatus::LINK_REPAIR_COMPLETE])) {
                $this->logger->addLog($product, 'App\Models\RepairProduct', $repairProduct->id, $statusIds[WorkStatus::LINK_REPAIR_COMPLETE], $user->id, '점검/수리 완료');
            }

            // 구성품 처리 - 새 수리든 대기 상태에서 완료로 바뀌든 모두 활용
            $this->updateRepairProductParts($repairProduct, $data, $user, $this->workStatusService, $this->logger);

            // 구성품 총 비용 계산 후 invoice2 업데이트
            $invoice2 = $repairProduct->parts_total_cost;
            $repairProduct->update(['invoice2' => $invoice2]);

            // 구성품 총 비용 로그 기록 (구성품이 있을 때만)
            if ($invoice2 > 0) {
                $formattedInvoice2 = number_format($invoice2);
                $this->logger->addLog(
                    $product,
                    'App\Models\RepairProductParts',
                    $repairProduct->id,
                    $statusIds[WorkStatus::LINK_REPAIR_ADD_PARTS],
                    $user->id,
                    "[$product->qaid]$product->name<br>구성품 총 비용: $formattedInvoice2 원"
                );
            }

            $this->countService->multipleUpdate($counters);
            $this->logger->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            SimpleLogService::error('product', '수리/점검 저장 실패', [], $e);
            throw $e;
        }
    }

    /**
     * OS 재설치비 계산(invoice2)
     * @param Product $product
     * @return int
     */
    public function getOsReInstall(Product $product): int
    {
        $rows = RepairFee::join('repair_fee_ranges', 'repair_fee_ranges.id', '=', 'repair_fees.repair_fee_range_id')
            ->join('repair_categories', 'repair_categories.id', '=', 'repair_fee_ranges.repair_category_id')
            ->where('repair_categories.cate4_id', $product->cate4_id)
            ->where('repair_categories.cate5_id', $product->cate5_id)
            ->where('repair_fees.repair_type', RepairFee::REPAIR_TYPE['OS'])
            ->select('repair_fees.amount', 'repair_fee_ranges.min_value', 'repair_fee_ranges.max_value')
            ->get();

        $invoice2 = 0;
        foreach ($rows as $row) {
            if ((int) $row->min_value === 0 && (int) $row->max_value === 0) {
                $invoice2 = $row->amount;
                break;
            }

            if ($product->amount >= $row->min_value && $product->amount <= $row->max_value) {
                $invoice2 = $row->amount;
                break;
            }
        }

        return $invoice2;
    }

    /**
     * 동적 WorkStatus 시스템을 사용하여 상태 ID 목록 생성
     * @throws Exception
     */
    private function getStatusIds(array $data): array
    {
        try {
            /**
             * 로그를 찍기 위해 $data['log_mode']를 사용<br>
             * 점검/수리 완료(repair_complete), 대기(repair_waiting),
             * 출고 등록(pallet_product_registered), 출고 적재(pallet_product_inspect)
             */
            $isParts = is_array($data['add_parts']) && !empty($data['add_parts']);

            return $this->workStatusService->getStatusIds([
                'symptom_code' => $data['symptom_code'],
                'process_code' => $data['process_code'],
                'grade_code' => $data['grade_code'],
                'os_reinstall' => !empty($data['os_reinstall']),
                'add_fee' => !empty($data['add_fee']),
                'add_parts' => $isParts,
                'log_mode' => $data['log_mode'],
            ]);
        } catch (Exception $e) {
            SimpleLogService::error('work_status', '동적 WorkStatus 생성 실패', [
                'data' => $data,
            ], $e);

            // 폴백: 기존 WorkStatusService 사용
            return $this->workStatusService->getStatusIds($data);
        }
    }

    /**
     * 수리비 정보 조회 (범위 ID 포함) - 개선된 버전
     * @param Product $product
     * @param array $data
     * @return array
     */
    private function getRepairFeeInfo(Product $product, array $data): array
    {
        $repairType = $this->getRepairTypeByRepairGradeCode($data['grade_code']);

        // repairType이 null인 경우 (ST_XL 등급) - 수리비 0원 처리
        if ($repairType === null) {
            return [
                'feeType' => 'none',
                'feeUnit' => 'won',
                'is_default_fee' => 'Y',
                'repair_fee_range_id' => null,
                'default_fee_range_id' => null,
            ];
        }

        $repairFeeResult = $this->getRepairFee($product, $repairType);

        return [
            'feeType' => $repairFeeResult['feeType'],
            'feeUnit' => $repairFeeResult['feeUnit'],
            'is_default_fee' => $repairFeeResult['isDefault'] ? 'Y' : 'N',
            'repair_fee_range_id' => $repairFeeResult['repairFeeRangeId'],
            'default_fee_range_id' => $repairFeeResult['isDefault'] ?
                RepairFee::DEFAULT_FEE_RANGE_IDS[$repairFeeResult['defaultFeeRangeCode']] ?? null : null,
        ];
    }

    /**
     * 수리비 계산 (기본값 정보 포함) - 개선된 버전
     * @param Product $product
     * @param string|null $repairType
     * @return array
     */
    public function getRepairFee(Product $product, string|null $repairType): array
    {
        // repairType이 null인 경우 (ST_XL 등급) - 수리비 0원 처리
        if ($repairType === null) {
            return [
                'feeType' => 'none',
                'feeUnit' => 'won',
                'minValue' => 0,
                'maxValue' => 0,
                'invoiceAmount' => 0,
                'isDefault' => true,
                'defaultFeeRangeCode' => null,
                'repairFeeRangeId' => null,
                'feeRangeList' => [],
            ];
        }

        if ($this->isMonitorProduct($product)) {
            $result = $this->processMonitorProduct($product, $repairType);
        } elseif ($this->isAppleProduct($product)) {
            $result = $this->processAppleProduct($product, $repairType);
        } else {
            $result = $this->processGeneralProduct($product, $repairType);
        }

        return [
            'feeType' => $result['feeInfo']['feeType'] ?? 'none',
            'feeUnit' => $result['feeInfo']['feeUnit'] ?? 'won',
            'minValue' => $result['feeInfo']['minValue'] ?? 0,
            'maxValue' => $result['feeInfo']['maxValue'] ?? 0,
            'invoiceAmount' => $result['feeInfo']['invoiceAmount'] ?? 0,
            'isDefault' => $result['feeInfo']['isDefault'] ?? false,
            'defaultFeeRangeCode' => $result['feeInfo']['defaultFeeRangeCode'] ?? null,
            'repairFeeRangeId' => $result['feeInfo']['repairFeeRangeId'] ?? null,
            'feeRangeList' => $result['feeRangeList'] ?? [],
        ];
    }

    // ========================================
    // 비공개 메서드 (Private Methods)
    // ========================================

    /**
     * 수리 내역 저장
     * @param Product $product
     * @param array $data
     * @return RepairProduct
     */
    private function createRepairProduct(Product $product, array $data): RepairProduct
    {
        $repairProduct = new RepairProduct();

        $repairProduct->product_id = $product->id;
        $repairProduct->status = $data['status'];
        if ($data['status'] === RepairProduct::STATUS_REPAIRED) {
            $repairProduct->completed_user_id = $data['user_id'];
            $repairProduct->completed_at = $data['now'];
        } else {
            $repairProduct->waiting_user_id = $data['user_id'];
            $repairProduct->waiting_at = $data['now'];
        }
        $repairProduct->amount = $product->amount;
        $repairProduct->repair_symptom_id = $this->findSymptomIdByCode($data['symptom_code']);
        $repairProduct->repair_process_id = $this->findProcessIdByCode($data['process_code']);
        $repairProduct->repair_grade_id = $this->findGradeIdByCode($data['grade_code']);
        $repairProduct->invoice1 = $data['invoice1'];
        $repairProduct->invoice3 = $data['invoice3'];
        $repairProduct->is_os_install = $data['os_reinstall'] === true ? 1 : 0;
        $repairProduct->memo = $data['memo'];

        // 수리비 정보 저장
        if (isset($data['repair_fee_info'])) {
            $repairProduct->fee_type = $data['repair_fee_info']['feeType'] ?? null;
            $repairProduct->fee_unit = $data['repair_fee_info']['feeUnit'] ?? null;
            $repairProduct->is_default_fee = $data['repair_fee_info']['is_default_fee'] ?? 'N';
            $repairProduct->repair_fee_range_id = $data['repair_fee_info']['repair_fee_range_id'] ?? null;
            $repairProduct->default_fee_range_id = $data['repair_fee_info']['default_fee_range_id'] ?? null;
        } else {
            // 수리비 정보가 없는 경우 기본값 설정
            $repairProduct->fee_type = null;
            $repairProduct->fee_unit = null;
            $repairProduct->is_default_fee = 'N';
            $repairProduct->repair_fee_range_id = null;
            $repairProduct->default_fee_range_id = null;
        }

        $repairProduct->save();

        return $repairProduct;
    }

    /**
     * 수리비 계산 (invoice1)
     * @param Product $product
     * @param array $data
     * @return int
     */
    private function calculateInvoice1(Product $product, array $data): int
    {
        $repairType = $this->getRepairTypeByRepairGradeCode($data['grade_code']);
        if ($repairType === null) {
            $invoice1 = 0;
        } else {
            $invoice1 = $this->getRepairFee($product, $repairType)['invoiceAmount'];
        }

        return $invoice1;
    }

    /**
     * OS 재설치비 계산 (invoice3)
     * @param Product $product
     * @param array $data
     * @return int
     */
    private function calculateInvoice3(Product $product, array $data): int
    {
        $invoice3 = 0;
        if ($data['os_reinstall']) {
            $invoice3 = $this->getOsReInstall($product);
        }

        return $invoice3;
    }

    // ========================================
    // 제품별 처리 메서드 (Product Processing Methods)
    // ========================================

    /**
     * 모니터 제품 처리
     */
    private function processMonitorProduct(Product $product, string $repairType): array
    {
        $model = $this->getMonitorModel($product);
        $sizeInfo = $this->extractSizeFromName($product);
        $size = $sizeInfo['size'];
        $feeUnit = $sizeInfo['unit'];

        $query = $this->monitorFeeListQuery($product, $model, $feeUnit, $repairType);
        $result = $query->where('repair_fee_ranges.min_value', '<', $size)
            ->where('repair_fee_ranges.max_value', '>=', $size)
            ->first();

        if ($result) {
            $feeInfo = $this->setDbFeeInfo($result);
            $feeInfo['feeUnit'] = $feeUnit;
        } else {
            $feeInfo = $this->setDefaultFeeInfo($product, $repairType);
            $feeInfo['feeUnit'] = $feeUnit;
        }

        // 범위 리스트 생성
        $feeRangeList = [];
        $feeValues = $query->get();
        foreach ($feeValues as $item) {
            $unitName = RepairFee::$FEE_UNIT_NAME[$item->fee_unit] ?? $item->fee_unit;
            $maxValue = $item->max_value >= 100000000 ? '∞' : $item->max_value;

            $feeRangeList[] = [
                'id' => $item->id,
                'min_value' => $item->min_value,
                'max_value' => $item->max_value,
                'amount' => $item->amount,
                'formatted_amount' => number_format($item->amount) . '원',
                'range' => "{$item->min_value}{$unitName} 초과 ~ {$maxValue}{$unitName} 이하",
            ];
        }

        return [
            'feeInfo' => $feeInfo,
            'feeRangeList' => $feeRangeList,
        ];
    }

    /**
     * Apple 제품 처리
     */
    private function processAppleProduct(Product $product, string $repairType): array
    {
        $appleModel = $this->getAppleProductModel($product);

        $query = $this->appleFeeListQuery($product, $appleModel['model'], $appleModel['feeType'])
            ->where('repair_fee_ranges.min_value', '<', $product->amount)
            ->where('repair_fee_ranges.max_value', '>=', $product->amount);
        $result = $query->first();

        if ($result === null) {
            $feeInfo = $this->setDefaultFeeInfo($product, $repairType);
            $feeRangeList = $this->getDefaultAppleFeeList($product)['feeRangeList'];
        } else {
            $feeInfo = $this->setDbFeeInfo($result);

            $feeRangeList = [];
            $feeRangeResult = $this->appleFeeListQuery($product, $appleModel['model'], $appleModel['feeType'])->get();
            foreach ($feeRangeResult as $item) {
                $unitName = RepairFee::$FEE_UNIT_NAME[$item->fee_unit] ?? $item->fee_unit;
                $maxValue = $item->max_value >= 100000000 ? '∞' : $item->max_value;

                $feeRangeList[] = [
                    'id' => $item->id,
                    'min_value' => $item->min_value,
                    'max_value' => $item->max_value,
                    'amount' => $item->amount,
                    'formatted_amount' => number_format($item->amount) . '원',
                    'range' => "{$item->min_value}{$unitName} 초과 ~ {$maxValue}{$unitName} 이하",
                ];
            }
        }

        return [
            'feeInfo' => $feeInfo,
            'feeRangeList' => $feeRangeList,
        ];
    }

    /**
     * 일반 제품 처리
     */
    private function processGeneralProduct(Product $product, string $repairType): array
    {
        $feeValues = $this->generalFeeListQuery($product, $repairType)->get();
        SimpleLogService::debug('repair', '일반 제품 수리비 정보', [
            'repairType' => $repairType,
            'product_amount' => $product->amount,
            'sql' => $this->generalFeeListQuery($product, $repairType)->toRawSql(),
        ]);

        if ($feeValues->isEmpty()) {
            try {
                $feeInfo = $this->setDefaultFeeInfo($product, $repairType);
                $defaultGeneralFeeList = $this->getDefaultGeneralFeeList($product, $repairType);
                $feeRangeList = $defaultGeneralFeeList['feeRangeList'] ?? [];

                SimpleLogService::debug('repair', '기본값 사용 성공', [
                    'feeInfo' => $feeInfo,
                    'feeRangeList_count' => count($feeRangeList),
                    'defaultGeneralFeeList' => $defaultGeneralFeeList,
                ]);
            } catch (Exception $e) {
                SimpleLogService::error('repair', '기본값 설정 중 오류 발생', [
                    'error' => $e->getMessage(),
                    'product_id' => $product->id,
                    'repairType' => $repairType,
                ]);

                // 오류 발생 시 안전한 기본값 반환
                $feeInfo = [
                    'isDefault' => true,
                    'defaultFeeRangeCode' => 'GENERAL_CHECK_LOW',
                    'feeType' => 'price',
                    'feeUnit' => 'won',
                    'minValue' => 0,
                    'maxValue' => 100000000,
                    'invoiceAmount' => 5800,
                ];
                $feeRangeList = [];
            }
        } else {
            $feeInfo = [
                'isDefault' => false,
                'defaultFeeRangeCode' => null,
                'minValue' => 0,
                'maxValue' => 0,
                'invoiceAmount' => $feeValues->first()->amount ?? 0,
                'repairFeeRangeId' => null,
            ];

            $feeRangeList = [];
            foreach ($feeValues as $item) {
                $feeInfo['feeType'] = $item->fee_type;
                $feeInfo['feeUnit'] = $item->fee_unit;

                $unitName = RepairFee::$FEE_UNIT_NAME[$item->fee_unit] ?? $item->fee_unit;
                $maxValue = $item->max_value >= 100000000 ? '∞' : $item->max_value;

                $feeRangeList[] = [
                    'id' => $item->id,
                    'min_value' => $item->min_value,
                    'max_value' => $item->max_value,
                    'amount' => $item->amount,
                    'formatted_amount' => number_format($item->amount) . '원',
                    'range' => "{$item->min_value}{$unitName} 초과 ~ {$maxValue}{$unitName} 이하",
                ];

                // 제품 가격이 해당 범위에 속하는지 확인
                if ($item->fee_type !== RepairFee::FEE_TYPE['NONE']
                    && ($product->amount > $item->min_value && $product->amount <= $item->max_value))
                {
                    $feeInfo['minValue'] = $item->min_value;
                    $feeInfo['maxValue'] = $item->max_value;
                    $feeInfo['invoiceAmount'] = $item->amount;
                    $feeInfo['repairFeeRangeId'] = $item->id;
                }
            }
        }

        return [
            'feeInfo' => $feeInfo,
            'feeRangeList' => $feeRangeList,
        ];
    }

    // ========================================
    // 헬퍼 메서드 (Helper Methods)
    // ========================================

    /**
     * 기본값 범위 코드 찾기
     * @param Product $product
     * @param string $repairType
     * @return string|null
     */
    private function findDefaultFeeRangeCode(Product $product, string $repairType): ?string
    {
        if ($this->isAppleProduct($product)) {
            return match (true) {
                $product->amount <= 50000 => 'APPLE_CHECK_1',
                $product->amount <= 100000 => 'APPLE_CHECK_2',
                $product->amount <= 200000 => 'APPLE_CHECK_3',
                default => 'APPLE_CHECK_4',
            };
        } else {
            $isLowPrice = $product->amount < 50000;

            return match ($repairType) {
                'CHECK' => $isLowPrice ? 'GENERAL_CHECK_LOW' : 'GENERAL_CHECK_HIGH',
                'REPAIR' => $isLowPrice ? 'GENERAL_REPAIR_LOW' : 'GENERAL_REPAIR_HIGH',
                default => $isLowPrice ? 'GENERAL_CHECK_LOW' : 'GENERAL_CHECK_HIGH', // 기본값은 CHECK로 처리
            };
        }
    }

    /**
     * 기본값 정보 설정 헬퍼 메서드
     * @param Product $product
     * @param string $repairType
     * @return array
     */
    private function setDefaultFeeInfo(Product $product, string $repairType): array
    {
        $defaultFeeRangeCode = $this->findDefaultFeeRangeCode($product, $repairType);

        // 디버깅을 위한 로그 추가
        SimpleLogService::debug('repair', '기본값 정보 설정', [
            'product_id' => $product->id,
            'product_amount' => $product->amount,
            'repairType' => $repairType,
            'defaultFeeRangeCode' => $defaultFeeRangeCode,
            'available_codes' => array_keys(RepairFee::DEFAULT_FEE_RANGE_IDS),
        ]);

        // 기본값 범위 코드가 존재하지 않는 경우 안전한 기본값 사용
        if (!isset(RepairFee::DEFAULT_FEE_RANGE_IDS[$defaultFeeRangeCode])) {
            SimpleLogService::error('repair', '기본값 범위 코드를 찾을 수 없음', [
                'defaultFeeRangeCode' => $defaultFeeRangeCode,
                'available_codes' => array_keys(RepairFee::DEFAULT_FEE_RANGE_IDS),
            ]);

            // 안전한 기본값으로 fallback
            $defaultFeeRangeCode = 'GENERAL_CHECK_LOW';
        }

        $defaultRangeId = RepairFee::DEFAULT_FEE_RANGE_IDS[$defaultFeeRangeCode];

        // 추가 디버깅 로그
        SimpleLogService::debug('repair', '기본값 ID 정보', [
            'defaultRangeId' => $defaultRangeId,
            'DEFAULT_FEE_RANGES_keys' => array_keys(RepairFee::DEFAULT_FEE_RANGES),
        ]);

        $defaultInfo = RepairFee::getDefaultFeeInfo($defaultRangeId);

        // defaultInfo 구조 확인
        SimpleLogService::debug('repair', '기본값 정보 결과', [
            'defaultInfo' => $defaultInfo,
        ]);

        return [
            'isDefault' => true,
            'defaultFeeRangeCode' => $defaultFeeRangeCode,
            'feeType' => $defaultInfo['feeType'],
            'feeUnit' => $defaultInfo['feeUnit'],
            'minValue' => $defaultInfo['min_value'],
            'maxValue' => $defaultInfo['max_value'],
            'invoiceAmount' => $defaultInfo['amount'],
            'repairFeeRangeId' => null, // 기본값은 DB 범위 ID가 없으므로 null
        ];
    }

    /**
     * DB 값 정보 설정 헬퍼 메서드
     * @param object $result
     * @return array
     */
    private function setDbFeeInfo(object $result): array
    {
        return [
            'isDefault' => false,
            'defaultFeeRangeCode' => null,
            'feeType' => $result->fee_type,
            'feeUnit' => $result->fee_unit,
            'minValue' => $result->min_value,
            'maxValue' => $result->max_value,
            'invoiceAmount' => $result->amount,
            'repairFeeRangeId' => $result->id,
        ];
    }
}
