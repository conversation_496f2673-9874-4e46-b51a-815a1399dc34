<?php

namespace App\Services;

use App\Exceptions\CarryoutException;
use App\Models\Carryout;
use App\Models\CarryoutProduct;
use App\Models\DeleteLog;
use App\Models\Product;
use App\Models\ProductLog;
use App\Models\User;
use App\Models\WorkStatus;
use App\Traits\Product\SearchTrait as ProductSearchTrait;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class CarryoutService
{
    use ProductSearchTrait;

    protected WorkStatusService $workStatusService;
    protected TokenService $tokenService;

    public function __construct(
        WorkStatusService $workStatusService,
        TokenService $tokenService
    ) {
        $this->workStatusService = $workStatusService;
        $this->tokenService = $tokenService;
    }

    /**
     * Carryout 기본 상태 검증 (공통 로직)
     *
     * @param Carryout $carryout
     * @param array $allowedStatuses 허용되는 상태 목록
     * @param string $operationName 작업명 (에러 메시지용)
     * @throws CarryoutException
     */
    private function validateCarryoutStatus(Carryout $carryout, array $allowedStatuses, string $operationName): void
    {
        // 취소된 Carryout은 모든 작업 불가 (상품이 삭제되어 되돌릴 수 없음)
        if ($carryout->status === Carryout::STATUS_CANCELED) {
            throw CarryoutException::invalidOperation("취소된 외주 반출 정보는 {$operationName}할 수 없습니다.");
        }

        // 허용되지 않은 상태인 경우
        if (!in_array($carryout->status, $allowedStatuses)) {
            $statusText = $this->getStatusText($carryout->status);
            throw CarryoutException::invalidOperation("현재 상태({$statusText})에서는 {$operationName}할 수 없습니다.");
        }
    }

    /**
     * Carryout 상태 텍스트 반환
     *
     * @param int $status
     * @return string
     */
    private function getStatusText(int $status): string
    {
        return match ($status) {
            Carryout::STATUS_CARRIED_OUT => '반출 완료',
            Carryout::STATUS_CARRIED_IN => '반입 완료',
            Carryout::STATUS_CANCELED => '취소',
            default => '알 수 없음'
        };
    }

    /**
     * 기존 Carryout 수정 가능 여부 검증
     *
     * @param Carryout $carryout
     * @throws CarryoutException
     */
    private function validateExistingCarryout(Carryout $carryout): void
    {
        // 수정 가능한 상태: 취소 상태가 아닌 모든 상태
        $allowedStatuses = [
            Carryout::STATUS_CARRIED_OUT,     // 반출 완료 상태
            Carryout::STATUS_CARRIED_IN,      // 반입 완료 상태
        ];

        $this->validateCarryoutStatus($carryout, $allowedStatuses, '수정');
    }

    /**
     * 외주 반출을 위한 Carryout 비즈니스 로직 검증
     *
     * @param Carryout $carryout
     * @throws CarryoutException
     */
    private function validateCarryoutForExport(Carryout $carryout): void
    {
        // 반출 가능한 상태: 반입 완료 상태 (반출로 변경 가능)
        $allowedStatuses = [
            Carryout::STATUS_CARRIED_IN,      // 반입 완료 상태 (다시 반출 가능)
        ];

        $this->validateCarryoutStatus($carryout, $allowedStatuses, '반출');
    }

    public function getCarryoutById(array $data, array $select = ['*']): object|null
    {
        if ($data['carryoutId']) {
            $carryout = Carryout::select($select)
                ->where('id', $data['carryoutId'])
                ->first();
        }

        return $carryout ?? null;
    }

    /**
     * 외주 반출 팔레트 등록/수정
     *
     * @param array $data
     * @param User $user
     * @throws CarryoutException|Exception|Throwable
     */
    public function storeCarryout(array $data, User $user): void
    {
        try {
            DB::beginTransaction();

            // 1. 기존 Carryout 조회 또는 새로 생성
            $carryout = Carryout::firstOrNew(['id' => $data['id'] ?? null]);

            // 2. 기존 Carryout이 존재할 때 비즈니스 로직 검증
            if ($carryout->exists) {
                $this->validateExistingCarryout($carryout);
            }

            // 3. Carryout 데이터 설정
            $carryout->status = empty($data['status']) ? Carryout::STATUS_CARRIED_OUT : (int) $data['status'];
            $carryout->carryout_user_id = $user->id;
            $carryout->carryout_at = $data['carryout_at'];
            $carryout->memo = $data['memo'] ?? null;

            // 4. 새로운 등록인 경우 토큰 생성
            if (!$carryout->exists) {
                $token = $this->tokenService->storeToken($this->tokenService->generateToken());
                $carryout->token_id = $token->id;
            }

            // 5. Carryout 저장
            $carryout->save();

            DB::commit();

            // 6. 성공 로그 기록
            SimpleLogService::info('carryout', '외주반출 정보등록 성공', [
                'carryout_id' => $carryout->id,
                'user_id' => $user->id,
                'action' => $carryout->exists ? '수정' : '등록'
            ]);

        } catch (CarryoutException $e) {
            DB::rollBack();
            SimpleLogService::error('carryout', '외주반출 정보등록 실패 (비즈니스 로직)', [
                'user_id' => $user->id,
                'data' => $data
            ], $e);
            throw $e;
        } catch (Exception $e) {
            DB::rollBack();
            SimpleLogService::error('carryout', '외주반출 정보등록 실패 (시스템 오류)', [
                'user_id' => $user->id,
                'data' => $data
            ], $e);
            throw $e;
        }
    }

    /**
     * 외주 반출::일괄 처리
     *
     * @param array $data
     * @param User $user
     * @throws CarryoutException|Exception|Throwable
     */
    public function exportCarryout(array $data, User $user): void
    {
        try {
            DB::beginTransaction();

            // 1. Carryout 존재 여부 및 상태 검증
            $carryout = Carryout::find($data['carryoutId']);
            if ($carryout === null) {
                throw CarryoutException::carryoutNotFound();
            }

            // 2. 비즈니스 로직 검증
            $this->validateCarryoutForExport($carryout);

            // 3. WorkStatus ID 조회
            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_CARRYOUT_EXPORT, #외주 반출
            ]);

            // 4. CarryoutProduct 조회 (성능 최적화)
            $carryoutProducts = CarryoutProduct::with(['product' => function ($query) {
                $query->select('id', 'req_id', 'status', 'checked_status');
            }])
                ->where('carryout_id', $carryout->id)
                ->get();

            // 5. 상품이 없는 경우 처리
            if ($carryoutProducts->isEmpty()) {
                throw CarryoutException::invalidOperation('반출할 상품이 없습니다.');
            }

            $countService = new CountService();
            $counters = [];
            $productLogs = [];
            $now = now();

            // 6. 각 상품별 반출 처리
            foreach ($carryoutProducts as $carryoutProduct) {
                $product = $carryoutProduct->product;

                // 상품 상태 검증 (등록된 상품 또는 반입된 상품만 반출 가능)
                if (!in_array($product->status, [Product::STATUS_REGISTERED, Product::STATUS_CARRIED_OUT])) {
                    throw CarryoutException::isNotExportable($product->qaid ?? 'unknown', $product->status);
                }

                // 상품 상태(반출 중) 업데이트
                $product->update([
                    'status' => Product::STATUS_CARRIED_OUT
                ]);

                // 상태 변환/통계::외주 반출 처리
                $productLogs[] = [
                    'product_id' => $product->id,
                    'model_type' => 'App\Models\CarryoutProduct',
                    'model_id' => $carryoutProduct->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_CARRYOUT_EXPORT],
                    'user_id' => $user->id,
                    'memo' => "[$product->qaid]외주 반출 처리",
                    'created_at' => $now,
                    'updated_at' => $now,
                ];

                // 카운터 기록
                // 반출/반입의 경우 수리 대기중 창고에서는 그대로 두고 반출/반입 카운터만 처리해 준다.
                // 외주 반출 수리중(carryout): +1
                $counters = $countService->initCounter($product->req_id, $counters);
                // $counters[$product->req_id]['checked'] -= 1;
                $counters[$product->req_id]['carryout'] += 1;
            }

            // 7. ProductLog 일괄 삽입
            if (!empty($productLogs)) {
                ProductLog::insert($productLogs);
            }

            // 8. 카운터 업데이트
            $countService->multipleUpdate($counters);

            // 9. Carryout 상태 업데이트
            $carryout->status = Carryout::STATUS_CARRIED_OUT;
            $carryout->carryout_user_id = $user->id;
            $carryout->carryout_at = $now;
            $carryout->save();

            DB::commit();

            // 10. 성공 로그 기록
            SimpleLogService::info('carryout', '외주반출 일괄처리 성공', [
                'carryout_id' => $carryout->id,
                'user_id' => $user->id,
                'product_count' => $carryoutProducts->count()
            ]);
        } catch (CarryoutException $e) {
            DB::rollBack();
            SimpleLogService::error('carryout', '외주반출 일괄처리 실패 (비즈니스 로직)', [
                'user_id' => $user->id,
                'carryout_id' => $data['id'] ?? null,
                'data' => $data
            ], $e);
            throw $e;
        } catch (Exception $e) {
            DB::rollBack();
            SimpleLogService::error('carryout', '외주반출 일괄처리 실패 (시스템 오류)', [
                'user_id' => $user->id,
                'carryout_id' => $data['id'] ?? null,
                'data' => $data
            ], $e);
            throw $e;
        }
    }

    /**
     * 외주 반입::일괄 처리
     *
     * @param array $data
     * @param User $user
     * @throws CarryoutException|Exception|Throwable
     */
    public function importCarryout(array $data, User $user): void
    {
        try {
            DB::beginTransaction();

            // 1. Carryout 존재 여부 및 상태 검증
            $carryout = Carryout::find($data['carryoutId']);
            if ($carryout === null) {
                throw CarryoutException::carryoutNotFound();
            }

            // 2. 비즈니스 로직 검증
            $this->validateCarryoutForImport($carryout);

            // 3. WorkStatus ID 조회
            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_CARRYOUT_IMPORT, #외주 반입
            ]);

            // 4. CarryoutProduct 조회 (성능 최적화)
            $carryoutProducts = CarryoutProduct::with(['product' => function ($query) {
                $query->select('id', 'req_id', 'status', 'checked_status');
            }])
                ->where('carryout_id', $carryout->id)
                ->get();

            // 5. 상품이 없는 경우 처리
            if ($carryoutProducts->isEmpty()) {
                throw CarryoutException::invalidOperation('반입할 상품이 없습니다.');
            }

            $countService = new CountService();
            $counters = [];
            $productLogs = [];
            $now = now();

            // 6. 각 상품별 반입 처리
            foreach ($carryoutProducts as $carryoutProduct) {
                $product = $carryoutProduct->product;

                // 상품 상태 검증 (등록된 상품 또는 반출된 상품만 반입 가능)
                if ($product->status !== Product::STATUS_CARRIED_OUT) {
                    throw CarryoutException::isNotImportable($product->qaid ?? 'unknown', $product->status);
                }

                // 상품 상태 업데이트
                $product->update([
                    'status' => Product::STATUS_REGISTERED
                ]);

                // 상태 변환/통계::일괄 외주 반입
                $productLogs[] = [
                    'product_id' => $product->id,
                    'model_type' => 'App\Models\CarryoutProduct',
                    'model_id' => $carryoutProduct->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_CARRYOUT_IMPORT],
                    'user_id' => $user->id,
                    'memo' => "[$product->qaid]외주 반입 처리",
                    'created_at' => $now,
                    'updated_at' => $now,
                ];

                // 카운터 기록
                // 외주 반출 수리중(carryout): -1
                $counters = $countService->initCounter($product->req_id, $counters);
                // $counters[$product->req_id]['checked'] += 1;
                $counters[$product->req_id]['carryout'] -= 1;
            }

            // 7. ProductLog 일괄 삽입
            if (!empty($productLogs)) {
                ProductLog::insert($productLogs);
            }

            // 8. 카운터 업데이트
            $countService->multipleUpdate($counters);

            // 9. Carryout 상태 업데이트
            $carryout->status = Carryout::STATUS_CARRIED_IN;
            $carryout->carryin_user_id = $user->id;
            $carryout->carryin_at = $now;
            $carryout->save();

            DB::commit();

            // 10. 성공 로그 기록
            SimpleLogService::info('carryout', '외주반입 일괄처리 성공', [
                'carryout_id' => $carryout->id,
                'user_id' => $user->id,
                'product_count' => $carryoutProducts->count()
            ]);

        } catch (CarryoutException $e) {
            DB::rollBack();
            SimpleLogService::error('carryout', '외주반입 일괄처리 실패 (비즈니스 로직)', [
                'user_id' => $user->id,
                'carryout_id' => $data['id'] ?? null,
                'data' => $data
            ], $e);
            throw $e;
        } catch (Exception $e) {
            DB::rollBack();
            SimpleLogService::error('carryout', '외주반입 일괄처리 실패 (시스템 오류)', [
                'user_id' => $user->id,
                'carryout_id' => $data['id'] ?? null,
                'data' => $data
            ], $e);
            throw $e;
        }
    }

    /**
     * 외주 반입을 위한 Carryout 비즈니스 로직 검증
     *
     * @param Carryout $carryout
     * @throws CarryoutException
     */
    private function validateCarryoutForImport(Carryout $carryout): void
    {
        // 반입 가능한 상태: 등록 상태, 반출 완료 상태 (반입으로 변경 가능)
        $allowedStatuses = [
            Carryout::STATUS_CARRIED_OUT,     // 반출 완료 상태 (다시 반입 가능)
        ];

        $this->validateCarryoutStatus($carryout, $allowedStatuses, '반입');
    }

    /**
     * 외주 취소::일괄 처리
     *
     * @param int $id
     * @param User $user
     * @throws CarryoutException|Exception|Throwable
     */
    public function destroyCarryout(int $id, User $user): void
    {
        try {
            DB::beginTransaction();

            // 1. Carryout 존재 여부 및 상태 검증
            $carryout = Carryout::find($id);
            if ($carryout === null) {
                throw CarryoutException::carryoutNotFound();
            }

            // 2. 비즈니스 로직 검증 (취소 가능한 상태인지 확인)
            $this->validateCarryoutForCancel($carryout);

            // 3. WorkStatus ID 조회
            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_CARRYOUT_CANCEL, #외주 취소
            ]);

            // 4. CarryoutProduct 조회 (성능 최적화)
            $carryoutProducts = CarryoutProduct::with(['product' => function ($query) {
                $query->select('id', 'req_id', 'status', 'checked_status');
            }])
                ->where('carryout_id', $carryout->id)
                ->get();

            // 5. 상품이 없는 경우 처리
            if ($carryoutProducts->isEmpty()) {
                throw CarryoutException::invalidOperation('취소할 상품이 없습니다.');
            }

            // 6. 삭제 로그 기록
            $deleteLog = $carryout->deleteLog();
            $deleteLog->create([
                'user_id' => $user->id,
                'content' => $carryout,
                'ip' => request()->getClientIp(),
            ]);

            $countService = new CountService();
            $counters = [];
            $productLogs = [];
            $deleteLogs = [];
            $now = now();

            // 7. 각 상품별 취소 처리
            foreach ($carryoutProducts as $carryoutProduct) {
                $product = $carryoutProduct->product;

                // 상품 상태 업데이트
                $product->update([
                    'status' => Product::STATUS_REGISTERED,
                    // 'checked_status' => Product::CHECKED_STATUS_CHECKED,
                ]);

                // 상태 변환/통계::일괄 외주 취소
                $productLogs[] = [
                    'product_id' => $product->id,
                    'model_type' => 'App\Models\CarryoutProduct',
                    'model_id' => $carryoutProduct->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_CARRYOUT_CANCEL],
                    'user_id' => $user->id,
                    'memo' => "[$product->qaid]외주 반출 삭제",
                    'created_at' => $now,
                    'updated_at' => $now,
                ];

                // 삭제 로그 기록
                $deleteLogs[] = [
                    'user_id' => $user->id,
                    'deletable_type' => 'App\Models\CarryoutProduct',
                    'deletable_id' => $carryoutProduct->id,
                    'content' => $carryoutProduct,
                    'ip' => request()->getClientIp(),
                    'created_at' => $now,
                    'updated_at' => $now,
                ];

                // 카운터 기록
                // 외주 반출 수리중(carryout): -1
                $counters = $countService->initCounter($product->req_id, $counters);
                // $counters[$product->req_id]['checked'] += 1;
                $counters[$product->req_id]['carryout'] -= 1;
            }

            // 8. 로그 일괄 삽입
            if (!empty($productLogs)) {
                ProductLog::insert($productLogs);
            }

            if (!empty($deleteLogs)) {
                DeleteLog::insert($deleteLogs);
            }

            // 9. 카운터 업데이트
            $countService->multipleUpdate($counters);

            // 10. Carryout 삭제
            $carryout->delete();

            DB::commit();

            // 11. 성공 로그 기록
            SimpleLogService::info('carryout', '외주반출 취소 성공', [
                'carryout_id' => $id,
                'user_id' => $user->id,
                'product_count' => $carryoutProducts->count()
            ]);

        } catch (CarryoutException $e) {
            DB::rollBack();
            SimpleLogService::error('carryout', '외주반출 취소 실패 (비즈니스 로직)', [
                'user_id' => $user->id,
                'carryout_id' => $id
            ], $e);
            throw $e;
        } catch (Exception $e) {
            DB::rollBack();
            SimpleLogService::error('carryout', '외주반출 취소 실패 (시스템 오류)', [
                'user_id' => $user->id,
                'carryout_id' => $id
            ], $e);
            throw $e;
        }
    }

    /**
     * 외주 취소를 위한 Carryout 비즈니스 로직 검증
     *
     * @param Carryout $carryout
     * @throws CarryoutException
     */
    private function validateCarryoutForCancel(Carryout $carryout): void
    {
        // 취소 가능한 상태: 등록 상태, 반출 완료 상태, 반입 완료 상태
        $allowedStatuses = [
            Carryout::STATUS_CARRIED_OUT,     // 반출 완료 상태
            Carryout::STATUS_CARRIED_IN,      // 반입 완료 상태
        ];

        $this->validateCarryoutStatus($carryout, $allowedStatuses, '취소');
    }

    /**
     * 반입 완료된 경우 예외 처리
     *
     * @param Product $product 확인할 상품
     * @throws CarryoutException 이미 반입 처리 된 경우
     */
    private function findExportableCarryoutProduct(Product $product): void
    {
        $carryoutProduct = CarryoutProduct::where('product_id', $product->id)->first();

        // 반입 완료된 경우 예외 발생
        if ($carryoutProduct && $carryoutProduct->carryin_at !== null) {
            throw CarryoutException::alreadyImported($product->qaid);
        }
    }

    /**
     * 외주: 반출 처리(반출 상품 테이블에 입력)
     * @throws Exception|Throwable
     */
    public function exportCarryoutProduct($data, User $user): void
    {
        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_INSPECTION_PASS, # 검수완료
                WorkStatus::LINK_CARRYOUT_EXPORT, #외주 반출
            ]);

            $product = $this->findExportableProducts($data['qaid']);
            $this->findExportableCarryoutProduct($product);

            $product->status = Product::STATUS_CARRIED_OUT;
            $now = now();
            if ($product->checked_status === Product::CHECKED_STATUS_UNCHECKED) {
                $product->checked_at = $now->copy();
                $product->checked_status = Product::CHECKED_STATUS_CHECKED;
                $product->checked_user_id = $user->id;

                // 상태 변환/통계::검수완료
                ProductLog::insert([
                    'product_id' => $product->id,
                    'model_type' => 'App\Models\Product',
                    'model_id' => $product->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_INSPECTION_PASS],
                    'user_id' => $user->id,
                    'memo' => "[$product->qaid]외주 반출로 인한 검수 완료 처리",
                    'created_at' => $now,
                    'updated_at' => $now,
                ]);
            }
            $product->save();

            $carryoutProduct = new CarryoutProduct();
            $carryoutProduct->req_id = $product->req_id;
            $carryoutProduct->carryout_id = intval($data['carryoutId']);
            $carryoutProduct->product_id = $product->id;
            $carryoutProduct->status = CarryoutProduct::STATUS_ONBOARD;
            $carryoutProduct->checked_user_id = $user->id;
            $carryoutProduct->checked_at = $now;
            $carryoutProduct->save();

            // 상태 변환/통계::반출 처리
            ProductLog::insert([
                'product_id' => $product->id,
                'model_type' => 'App\Models\CarryoutProduct',
                'model_id' => $carryoutProduct->id,
                'work_status_id' => $statusIds[WorkStatus::LINK_CARRYOUT_EXPORT],
                'user_id' => $user->id,
                'memo' => "[$product->qaid]외주 반출 처리",
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            // 카운터 기록
            // 입고검수완료(checked): -1, 외주 반출 수리중(carryout): +1
            $counters[$product->req_id] = [
                // 'checked' => -1,
                'carryout' => 1
            ];
            $countService = new CountService();
            $countService->multipleUpdate($counters);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('carryout', '외부점검을 위한 외주반출 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 상품이 반출중이 아니거나 이미 반입된 경우 예외 발생
     *
     * @param Product $product 확인할 상품
     * @throws CarryoutException 이미 반입 처리 된 경우
     */
    private function findImportableCarryoutProduct(Product $product): CarryoutProduct
    {
        $carryoutProduct = CarryoutProduct::where('product_id', $product->id)->first();

        if ($carryoutProduct === null) {
            throw CarryoutException::carryoutNotFound();
        }

        // 반입 완료된 경우 예외 발생
        if ($carryoutProduct->carryin_at !== null) {
            throw CarryoutException::alreadyImported($product->qaid);
        }

        // 상태가 취소일 경우
        if ($carryoutProduct->status === CarryoutProduct::STATUS_CANCELED) {
            throw CarryoutException::alreadyCanceled($product->qaid);
        }

        return $carryoutProduct;
    }

    /**
     * 외주: 반출 되었던 상품을 (개별)반입 처리
     * @throws Exception|Throwable
     */
    public function importCarryoutProduct($data, User $user): void
    {
        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_INSPECTION_PASS, # 검수완료
                WorkStatus::LINK_CARRYOUT_IMPORT, #외주 반입
            ]);

            $product = $this->findImportableProduct($data['qaid']);
            $carryoutProduct = $this->findImportableCarryoutProduct($product);

            $product->status = Product::STATUS_REPAIRED;
            $now = now();
            if ($product->checked_status === Product::CHECKED_STATUS_UNCHECKED) {
                $product->checked_at = $now;
                $product->checked_status = Product::CHECKED_STATUS_CHECKED;
                $product->checked_user_id = $user->id;

                // 상태 변환/통계::검수완료
                ProductLog::insert([
                    'product_id' => $product->id,
                    'model_type' => 'App\Models\Product',
                    'model_id' => $product->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_INSPECTION_PASS],
                    'user_id' => $user->id,
                    'memo' => "[$product->qaid]외주 반입 처리 및 검수 완료 처리",
                    'created_at' => $now,
                    'updated_at' => $now,
                ]);
            }
            $product->save();

            $carryoutProduct->update([
                'carryin_user_id' => $user->id,
                'carryin_at' => $now,
            ]);

            // 상태 변환/통계::반입 처리
            ProductLog::insert([
                'product_id' => $product->id,
                'model_type' => 'App\Models\CarryoutProduct',
                'model_id' => $carryoutProduct->id,
                'work_status_id' => $statusIds[WorkStatus::LINK_CARRYOUT_IMPORT],
                'user_id' => $user->id,
                'memo' => "[$product->qaid]외주 반입 처리",
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            // 카운터 기록
            // 외주 반출 수리중(carryout): -1
            $counters[$product->req_id] = [
                'carryout' => -1,
            ];
            $countService = new CountService();
            $countService->multipleUpdate($counters);

            // 반출 목록(팔레트) 처리
            // @todo: 이 팔레트의 모든 상품이 반입 완료 되었다면 반출 목록을 Carryout::STATUS_CARRIED_IN 으로 변경해야 함

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('carryout', '외주반출상품 반입처리 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 외주 취소::선택
     * @throws Exception|Throwable
     */
    public function destroyCarryoutProduct($data, User $user): void
    {
        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_INSPECTION_PASS, # 검수완료
                WorkStatus::LINK_CARRYOUT_CANCEL, #외주 취소
            ]);

            $carryoutProducts = CarryoutProduct::with('product')
                ->whereIn('id', $data['ids'])
                ->get();

            $countService = new CountService();
            $counters = [];

            $productLogs = [];
            $deleteLogs = [];

            foreach ($carryoutProducts as $carryoutProduct) {
                $product = $carryoutProduct->product;

                $product->update([
                    'status' => Product::STATUS_REGISTERED,
                    // 'checked_status' => Product::CHECKED_STATUS_CHECKED,
                ]);

                // 상태 변환/통계::선택 외주 취소
                $productLogs[] = [
                    'product_id' => $product->id,
                    'model_type' => 'App\Models\CarryoutProduct',
                    'model_id' => $carryoutProduct->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_CARRYOUT_CANCEL],
                    'user_id' => $user->id,
                    'memo' => "[$product->qaid]외주 반출 삭제",
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];

                $deleteLogs[] = [
                    'user_id' => $user->id,
                    'deletable_type' => 'App\Models\CarryoutProduct',
                    'deletable_id' => $carryoutProduct->id,
                    'content' => $carryoutProduct,
                    'ip' => request()->getClientIp(),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];

                // 카운터 기록
                // 외주 반출 수리중(carryout): -1, 입고검수완료(checked): +1
                $counters = $countService->initCounter($product->req_id, $counters);

                $counters[$product->req_id]['carryout'] -= 1;
                // $counters[$product->req_id]['checked'] += 1;
            }

            if (!empty($productLogs)) {
                ProductLog::insert($productLogs);
            }

            if (!empty($deleteLogs)) {
                DeleteLog::insert($deleteLogs);
            }

            // 실제 카운터 기록
            $countService->multipleUpdate($counters);

            CarryoutProduct::whereIn('id', $data['ids'])->delete();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('carryout', '외주 반출대상 삭제 실패', [], $e);

            throw $e;
        }
    }
}
