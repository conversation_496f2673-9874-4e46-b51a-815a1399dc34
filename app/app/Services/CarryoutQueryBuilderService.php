<?php

namespace App\Services;

use App\Models\Carryout;
use App\Models\CarryoutProduct;
use App\Models\Product;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder as QueryBuilder;

class CarryoutQueryBuilderService
{
    /**
     * 외주 반출(팔레트) 리스트 - withCount 최적화 버전 (권장)
     *
     * @param array $data
     * @return EloquentBuilder
     */
    public function getCarryoutList(array $data): EloquentBuilder
    {
        $builder = Carryout::withCount([
            'carryoutProducts as count_total',
            'carryoutProducts as count_renovated' => function (EloquentBuilder $query) {
                $query->where('status', CarryoutProduct::STATUS_RENOVATED);
            },
            'carryoutProducts as count_remained' => function (EloquentBuilder $query) {
                $query->where('status', CarryoutProduct::STATUS_ONBOARD);
            },
        ])
        ->where('carryouts.status', '!=', Carryout::STATUS_CANCELED);

        // 날짜 범위 필터링 (인덱스 활용)
        if (!empty($data['beginAt']) && !empty($data['endAt'])) {
            $builder->whereBetween('carryouts.carryout_at', [$data['beginAt'], $data['endAt']]);
        }

        // 정렬 (인덱스 활용)
        $builder->orderBy('carryouts.created_at', 'desc');

        return $builder;
    }

    /**
     * 외주 반출 상품 리스트
     *
     * @param array $data
     * @return EloquentBuilder|QueryBuilder
     */
    public function getCarryoutProductList(array $data): EloquentBuilder|QueryBuilder
    {
        $builder = CarryoutProduct::select([
            'carryout_products.*',
            'products.qaid as qaid',
            'cate4.name as cate4',
            'cate5.name as cate5',
            'products.name as product_name',
            'products.amount as amount',
            'products.status as product_status',
            'products.checked_status as product_checked_status',
        ])
            ->join('carryouts', 'carryouts.id', '=', 'carryout_products.carryout_id')
            ->leftJoin('products', 'products.id', '=', 'carryout_products.product_id')
            ->leftJoin('cate4', 'cate4.id', '=', 'products.cate4_id')
            ->leftJoin('cate5', 'cate5.id', '=', 'products.cate5_id')
            ->where('carryout_products.status', '!=', CarryoutProduct::STATUS_CANCELED);

        if ($data['carryoutId']) {
            $builder->where('carryouts.id', $data['carryoutId']);
        }

        if ($data['beginAt'] && $data['endAt']) {
            $builder->leftJoin('reqs', 'reqs.id', '=', 'carryout_products.req_id')
                ->whereBetween('reqs.req_at', [$data['beginAt'], $data['endAt']]);
        }

        if ($data['repairStatus']) {
            $builder->where('carryout_products.status', $data['repairStatus']);
        }

        if ($data['productCarryinYN'] === 'N') {
            $builder->where('products.status', Product::STATUS_CARRIED_OUT);
        } else {
            $builder->whereNotIn('products.status', [Product::STATUS_CARRIED_OUT, Product::STATUS_HELD, Product::STATUS_DELETED]);
        }

        if ($data['searchType'] && $data['keyword']) {
            if ($data['searchType'] === 'qaid') {
                $builder->where(function (EloquentBuilder $query) use ($data) {
                    $query->where('products.qaid', $data['keyword'])
                        ->orWhere('products.barcode', $data['keyword'])
                        ->orWhere('products.name', 'like', '%'.$data['keyword'].'%');
                });
            } elseif ($data['searchType'] === 'token') {
                $builder->leftJoin('carryout_tokens', 'carryout_tokens.id', '=', 'carryouts.token_id')
                    ->where('carryout_tokens.token', $data['keyword']);
            } elseif ($data['searchType'] === 'memo') {
                $builder->leftJoin('memos', function ($join) use ($data) {
                    $join->on('memos.memorable_id', '=', 'carryouts.memorable_id')
                        ->where('memos.memorable_type', 'App\Models\CarryoutProduct')
                        ->where('memos.content', 'like', '%'.$data['keyword'].'%');
                });
            }
        }

        $builder->orderBy('carryouts.carryout_at', 'desc')
            ->orderBy('carryout_products.checked_at', 'desc');

        return $builder;
    }
}
