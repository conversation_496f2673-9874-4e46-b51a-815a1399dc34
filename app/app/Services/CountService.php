<?php

namespace App\Services;

use App\Helpers\HelperLibrary;
use App\Models\Pallet;
use App\Models\PalletProduct;
use App\Models\Product;
use App\Models\Req;
use App\Models\ReqCount;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Throwable;

class CountService
{
    /**
     * @param  int  $id req_id를 나타낸다.
     * @param  array  $counter
     * @return array
     */
    public function initCounter(int $id, array $counter): array
    {
        if (!isset($counter[$id])) {
            $items = [
                'undelivered' => 0,
                'unchecked' => 0,
                'checked' => 0,
                'carryout' => 0,
                'waiting' => 0,
                'repaired' => 0,
                'checkout' => 0,
                'exporting' => 0,
                'duplicated' => 0,
                'unlinked' => 0,
                'completed' => 0,
                'deleted' => 0,
            ];

            $counter[$id] = $items;
        }

        return $counter;
    }

    /**
     * @param  int  $id
     * @param  array  $columns
     * @return ReqCount
     */
    public function update(int $id, array $columns = []): ReqCount
    {
        $reqCount = ReqCount::where('req_id', $id)->first();

        foreach ($columns as $column => $count) {
            if (in_array($column, ReqCount::COUNT_COLUMNS)) {
                $newCount = $reqCount->$column + $count;

                $reqCount->$column = max($newCount, 0);
            }
        }

        $reqCount->save();

        return $reqCount;
    }

    /**
     * 여러 요청서에 대한 카운트 업데이트
     */
    public function multipleUpdate(array $counters = []): void
    {
        if (empty($counters)) return;

        foreach ($counters as $id => $counter) {
            $reqCount = ReqCount::where('req_id', $id)->first();

            foreach ($counter as $column => $count) {
                if ($count === 0) continue;

                if (in_array($column, ReqCount::COUNT_COLUMNS)) {
                    $newCount = $reqCount->$column + $count;

                    $reqCount->$column = max($newCount, 0);
                }
            }

            $reqCount->save();
        }
    }

    /**
     * [미입고]개수를 구한다.
     *
     * 상품의 status: 수리대기중(창고) - 10<br>
     * 상품의 checked_status: 미입고 - 30<br>
     * 팔레트 상품에 존재하면 안 된다.
     */
    public function getUndeliveredCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::leftJoin('pallet_products', 'pallet_products.product_id', '=', 'products.id')
            ->where('products.status', Product::STATUS_REGISTERED)
            ->where('products.checked_status', Product::CHECKED_STATUS_UNDELIVERED)
            ->whereNull('pallet_products.id')
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * 해당하는 요청서에서 [입고검수(대기) 개수]를 구한다.
     *
     * 상품의 status: 수리대기중(창고) - 10<br>
     * 상품의 checked_status: 검수대기 - 10<br>
     * 팔레트 상품에 존재하면 안 된다.
     */
    public function getUncheckedCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::leftJoin('pallet_products', 'pallet_products.product_id', '=', 'products.id')
            ->where('products.status', Product::STATUS_REGISTERED)
            ->where('products.checked_status', Product::CHECKED_STATUS_UNCHECKED)
            ->whereNull('pallet_products.id')
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * 해당하는 요청서에서 [구성품 신청중(대기) 개수]를 구한다.
     *
     * 상품의 status: 구성품 신청중(대기) - 19<br>
     * 상품의 checked_status: 검수대기 - 10<br>
     * 팔레트 상품에 존재하면 안 된다.
     */
    public function getWaitingCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::leftJoin('pallet_products', 'pallet_products.product_id', '=', 'products.id')
            ->where('products.status', Product::STATUS_WAITING)
            ->where('products.checked_status', Product::CHECKED_STATUS_UNCHECKED)
            ->whereNull('pallet_products.id')
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * 해당하는 요청서에서 [점검/수리완료 개수]를 구한다.
     *
     * 상품의 status: 구성품 신청중(대기) - 20<br>
     * 상품의 checked_status: 검수완료 - 20<br>
     * 팔레트 상품에 존재하면 안 된다.
     */
    public function getRepairedCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::leftJoin('pallet_products', 'pallet_products.product_id', '=', 'products.id')
            ->where('products.status', Product::STATUS_REPAIRED)
            ->where('products.checked_status', Product::CHECKED_STATUS_CHECKED)
            ->whereNull('pallet_products.id')
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * 해당하는 요청서에서 [점검대기(검수완료) 개수]를 구한다.
     *
     * 상품의 status: 수리대기중(창고) - 10<br>
     * 상품의 checked_status: 검수완료 - 20<br>
     * 팔레트 상품에 존재하면 안 된다.
     */
    public function getCheckedCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::leftJoin('pallet_products', 'pallet_products.product_id', '=', 'products.id')
            ->where('products.status', Product::STATUS_REGISTERED)
            ->where('products.checked_status', Product::CHECKED_STATUS_CHECKED)
            ->whereNull('pallet_products.id')
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * 해당하는 요청서에서 [외주반출 수리중 개수]를 구한다.
     *
     * 상품의 status: 반출중 - 50<br>
     */
    public function getCarryoutCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::where('products.status', Product::STATUS_CARRIED_OUT)
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * 해당하는 요청서에서 [적재중(점검완료) 개수]를 구한다.
     *
     * 상품의 status: 점검완료 - 30<br>
     * 팔레트의 status: 적재중 - 20<br>
     * 팔레트 상품의 status: 적재 - 10
     */
    public function getCheckoutCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::leftJoin('pallet_products', 'pallet_products.product_id', '=', 'products.id')
            ->leftJoin('pallets', 'pallet_products.pallet_id', '=', 'pallets.id')
            ->where('products.status', Product::STATUS_CHECKED_ON_PALLET)
            ->where('pallet_products.status', PalletProduct::STATUS_REGISTERED)
            ->where('pallets.status', Pallet::STATUS_LOADED)
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * 해당하는 요청서에서 [출고대기(마감) 개수]를 구한다.
     *
     * 상품의 status: 점검완료 - 30<br>
     * 팔레트의 status: 적재마감(출고대기) - 30<br>
     * 팔레트 상품의 status: 적재 - 10
     */
    public function getExportingCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::leftJoin('pallet_products', 'pallet_products.product_id', '=', 'products.id')
            ->leftJoin('pallets', 'pallet_products.pallet_id', '=', 'pallets.id')
            ->where('products.status', Product::STATUS_CHECKED_ON_PALLET)
            ->where('pallet_products.status', PalletProduct::STATUS_REGISTERED)
            ->where('pallets.status', Pallet::STATUS_CLOSED)
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * 해당하는 요청서에서 [출고완료 개수]를 구한다.
     *
     * 상품의 status: 출고완료 - 70<br>
     * 팔레트의 status: 출고완료 - 40<br>
     * 팔레트 상품의 status: 출고 - 30
     */
    public function getCompletedCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::leftJoin('pallet_products', 'pallet_products.product_id', '=', 'products.id')
            ->leftJoin('pallets', 'pallet_products.pallet_id', '=', 'pallets.id')
            ->where('products.status', Product::STATUS_EXPORTED)
            ->where('pallet_products.status', PalletProduct::STATUS_EXPORTED)
            ->where('pallets.status', Pallet::STATUS_EXPORTED)
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * 해당하는 요청서에서 [보류(삭제) 개수]를 구한다.
     *
     * 상품의 status: 보류 - 80, 삭제 - 90 이 아닌 경우
     */
    public function getDeletedCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::where(function (Builder $query) {
            $query->whereIn('status', [
                Product::STATUS_HELD,
                Product::STATUS_DELETED
            ]);
        })
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * 해당하는 요청서에서 [중복등록 개수]를 구한다.
     * 간단버전을 사용할 수 있도록 프로그램 처리할 것
     */
    public function getDuplicatedCount(int $reqId = null): int
    {
        if ($reqId === 2) return 0;

        return Product::where('duplicated', Product::IS_DUPLICATED_Y)
            ->where('status', '!=', Product::STATUS_DELETED)
            ->when($reqId === null, function ($query) {
                return $query->where('products.req_id', '!=', 2);
            }, function ($query) use ($reqId) {
                return $query->where('products.req_id', $reqId);
            })
            ->distinct('products.id')
            ->count('products.id');
    }

    /**
     * [미등록 개수]를 구한다.<br>
     * req_id = 2는 미등록 상품을 등록하는 곳이므로,
     * 여기에서 삭제된 것만 제외하고 개수를 세면 된다.
     */
    public function getUnlinkedCount(): int
    {
        return Product::where('req_id', Req::UNLINKED_ID)
            ->where('status', '!=', Product::STATUS_DELETED)
            ->count();
    }

    /**
     * @throws Throwable
     */
    public function updateReqCount(int $id): void
    {
        // $id가 11(고스트 상품)이면 아무 작업도 하지 않고 함수를 종료
        if ($id === 11) {
            return;
        }

        try {
            DB::beginTransaction();

            $req = Req::find($id);
            if (!$req) {
                throw new Exception("$id 요청서: 존재하지 않는 요청서 입니다.");
            }

            if ($id === 2) {
                $updateData = [
                    'unlinked' => $this->getUnlinkedCount()
                ];
            } else {
                $updateData = [
                    'undelivered' => $this->getUndeliveredCount($id),
                    'unchecked' => $this->getUncheckedCount($id),
                    'checked' => $this->getCheckedCount($id),
                    'carryout' => $this->getCarryoutCount($id),
                    'checkout' => $this->getCheckoutCount($id),
                    'exporting' => $this->getExportingCount($id),
                    'duplicated' => $this->getDuplicatedCount($id),
                    'completed' => $this->getCompletedCount($id),
                    'deleted' => $this->getDeletedCount($id),
                    'waiting' => $this->getWaitingCount($id),
                    'repaired' => $this->getRepairedCount($id),
                ];
            }

            $req->reqCount->update($updateData);

            $totalCount = $req->products()->count();
            $req->update([
                'total_count' => $totalCount
            ]);

            // 미등록 요청서(ID=2) 제외
            $isNotUnlinkedReq = ($id !== Req::UNLINKED_ID);

            // 점검중 상태로 변경 (checked 개수가 1개 이상일 때)
            if ($isNotUnlinkedReq &&
                $req->status !== Req::STATUS_CHECKED &&
                $req->reqCount->checked > 1) {

                $req->update([
                    'status' => Req::STATUS_CHECKED
                ]);
            }

            // 요청서 완료 상태로 변경 (모든 상품이 completed 또는 deleted 상태)
            $completedItemsCount = $req->reqCount->completed + $req->reqCount->deleted;
            $allItemsProcessed = ($req->total_count > 0 && $completedItemsCount === $req->total_count);

            if ($isNotUnlinkedReq &&
                $req->status !== Req::STATUS_COMPLETED &&
                $allItemsProcessed) {

                $req->update([
                    'status' => Req::STATUS_COMPLETED
                ]);
            }

            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();

            SimpleLogService::error('req', "$id 요청서 카운트 업데이트 실패", [], $e);

            throw new Exception("$id 요청서 카운트 업데이트 실패: " . $e->getMessage());
        }
    }
}
