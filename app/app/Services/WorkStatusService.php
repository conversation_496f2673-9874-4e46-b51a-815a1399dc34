<?php

namespace App\Services;

use App\Models\RepairGrade;
use App\Models\WorkStatus;
use App\Repositories\WorkStatusRepository;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;

class WorkStatusService
{
    protected WorkStatusRepository $workStatusRepository;

    public function __construct(WorkStatusRepository $workStatusRepository)
    {
        $this->workStatusRepository = $workStatusRepository;
    }

    /**
     * 여러 개의 link_code를 기반으로 WorkStatus ID를 가져옵니다.
     *
     * @param array $linkCodes
     * @return array
     * @throws Exception
     */
    public function getIds(array $linkCodes): array
    {
        // WorkStatus 데이터를 가져오기
        $workStatuses = WorkStatus::whereIn('link_code', $linkCodes)->pluck('id', 'link_code');

        // 데이터가 없는 경우 예외 처리
        foreach ($linkCodes as $code) {
            if (!isset($workStatuses[$code])) {
                throw new Exception("상태 기록을 위한 연결 정보가 존재하지 않습니다: {$code}");
            }
        }

        return $workStatuses->toArray(); // 배열로 반환
    }

    /**
     * @param $data
     * @return array
     * @throws Exception
     */
    public function getStatusIds($data): array
    {
        if (isset($data['symptom_code'])) {
            $symptomCode = $data['symptom_code'];
        } else {
            throw new Exception("증상 코드를 찾을 수 없습니다.");
        }

        if (isset($data['process_code'])) {
            $processCode = $data['process_code'];
        } else {
            throw new Exception("처리 내용 코드를 찾을 수 없습니다.");
        }

        if (isset($data['grade_code'])) {
            $gradeCode = $data['grade_code'];
        } else {
            throw new Exception("등급 코드를 찾을 수 없습니다.");
        }

        // match 표현식을 사용하여 gradeCode에 따른 상태 추가
        $additionalStatusIds = match(true) {
            str_starts_with($gradeCode, RepairGrade::GRADE_XL)
            => [match ($symptomCode) {
                'CH_BROKEN' => WorkStatus::LINK_REPAIR_XL1,
                'CH_STAIN' => WorkStatus::LINK_REPAIR_XL2,
                'CH_COMPO' => WorkStatus::LINK_REPAIR_XL3,
                'CH_RUN' => WorkStatus::LINK_REPAIR_XL4,
                'CH_DIFFER' => WorkStatus::LINK_REPAIR_XL5,
                'CH_EMPTY' => WorkStatus::LINK_REPAIR_XL6,
                'AP_BOOTX' => WorkStatus::LINK_REPAIR_APPLE_BOOTX,
                'AP_OSX' => WorkStatus::LINK_REPAIR_APPLE_OSX,
                'AP_POWERX' => WorkStatus::LINK_REPAIR_APPLE_POWERX,
                'AP_CHARGEX' => WorkStatus::LINK_REPAIR_APPLE_CHARGEX,
                'AP_WIFIX' => WorkStatus::LINK_REPAIR_APPLE_WIFIX,
                'AP_INPUTX' => WorkStatus::LINK_REPAIR_APPLE_INPUTX,
                'AP_PEACTX' => WorkStatus::LINK_REPAIR_APPLE_PEACTX,
                'AP_BUTTONX' => WorkStatus::LINK_REPAIR_APPLE_BUTTONX,
                'AP_USBX' => WorkStatus::LINK_REPAIR_APPLE_USBX,
                'AP_SCREENX' => WorkStatus::LINK_REPAIR_APPLE_SCREENX,
                'AP_PIXELX' => WorkStatus::LINK_REPAIR_APPLE_PIXELX,
                'AP_CASEX' => WorkStatus::LINK_REPAIR_APPLE_CASEX,
                'AP_CAMERAX' => WorkStatus::LINK_REPAIR_APPLE_CAMERAX,
                'AP_SPACEX' => WorkStatus::LINK_REPAIR_APPLE_SPACEX,
                'AP_STRAPX' => WorkStatus::LINK_REPAIR_APPLE_STRAPX,
                'AP_ETC' => WorkStatus::LINK_REPAIR_APPLE_ETC,
                default => throw new Exception("지원하지 않는 증상 코드입니다: {$symptomCode}"),
            }],
            $gradeCode === RepairGrade::GRADE_BEST => [WorkStatus::LINK_REPAIR_BEST],
            $gradeCode === RepairGrade::GRADE_GOOD => [WorkStatus::LINK_REPAIR_GOOD],
            $gradeCode === RepairGrade::GRADE_NORMAL => [WorkStatus::LINK_REPAIR_NORMAL],
            default => [
                match ($symptomCode) {
                    'CH_RUN' => WorkStatus::LINK_REPAIR_ISSUE1,
                    'CH_POWER' => WorkStatus::LINK_REPAIR_ISSUE2,
                    'CH_SCRATCH' => WorkStatus::LINK_REPAIR_ISSUE3,
                    'CH_STAIN' => WorkStatus::LINK_REPAIR_ISSUE4,
                    'CH_COMPO' => WorkStatus::LINK_REPAIR_ISSUE5,
                    'CH_ERRBOOT' => WorkStatus::LINK_REPAIR_ISSUE6,
                    default => throw new Exception("지원하지 않는 증상 코드입니다: {$symptomCode}"),
                },
                match ($processCode) {
                    'RP_PART' => WorkStatus::LINK_REPAIR_PROCESS1,
                    'RP_POWER' => WorkStatus::LINK_REPAIR_PROCESS2,
                    'RP_REFUR' => WorkStatus::LINK_REPAIR_PROCESS3,
                    'RP_CLEAN' => WorkStatus::LINK_REPAIR_PROCESS4,
                    'RP_COMPO' => WorkStatus::LINK_REPAIR_PROCESS5,
                    'RP_SW' => WorkStatus::LINK_REPAIR_PROCESS6,
                    'RP_WAITING' => WorkStatus::LINK_REPAIR_PROCESS7,
                    default => throw new Exception("지원하지 않는 처리 코드입니다: {$processCode}"),
                },
                WorkStatus::LINK_REPAIR_REFURB,
                ...(isset($data['os_reinstall']) && $data['os_reinstall'] ? [WorkStatus::LINK_REPAIR_OS_REINSTALL] : []),
                ...(isset($data['add_fee']) && $data['add_fee'] ? [WorkStatus::LINK_REPAIR_ADD_FEE] : []),
                ...(isset($data['add_parts']) && $data['add_parts'] ? [WorkStatus::LINK_REPAIR_ADD_PARTS] : []),
            ]
        };

        $extraWorkStatusIds = match($data['log_mode'] ?? '') {
            'repair_waiting' => [WorkStatus::LINK_REPAIR_WAITING],
            'repair_complete' => [WorkStatus::LINK_REPAIR_COMPLETE],
            'pallet_product_registered' => [WorkStatus::LINK_PALLET_PRODUCT_REGISTERED],
            'pallet_product_inspect' => [WorkStatus::LINK_PALLET_PRODUCT_INSPECT],
            default => [],
        };

        $workStatusIds = array_merge($additionalStatusIds, $extraWorkStatusIds);

        return $this->getIds($workStatusIds);
    }

    public function getFilteredStatuses(array $filters, int $perPage = 20): LengthAwarePaginator
    {
        return $this->workStatusRepository->getPaginated($perPage, $filters);
    }
}
