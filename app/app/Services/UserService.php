<?php

namespace App\Services;

use App\DTOs\UserFilterDto;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Facades\Hash;

class UserService
{
    /**
     * 검색 옵션을 이용한 사용자 검색
     * 만약 검색 로직이 복잡해 진다면 UserService 클래스로 이동해서 그 곳에서
     *
     * @param UserFilterDto $filter
     * @return EloquentBuilder|QueryBuilder|User 필터가 적용된 쿼리 빌더 인스턴스를 반환
     */
    public function getList(UserFilterDto $filter): EloquentBuilder|QueryBuilder|User
    {
        $keyword = $filter->keyword;
        $role = $filter->role;
        $status = $filter->status;

        $query = User::withTrashed();

        if ($status > 0) {
            $query->where('status', $status);
        }

        if ($role) {
            $query->where('role', $role);
        }

        if ($keyword) {
            $query->where(function ($query) use ($keyword) {
                $query->where('username', 'like', "%{$keyword}%")
                    ->orWhere('name', 'like', "%{$keyword}%")
                    ->orWhere('email', 'like', "%{$keyword}%");
            });
        }

        return $query->orderBy('status')
            ->orderBy('updated_at', 'desc');
    }

    public function getUserById(int $id): User
    {
        return User::withTrashed()->find($id);
    }

    /**
     * 사용자 생성
     *
     * @param array $data
     * @return void
     */
    public function storeUser(array $data): void
    {
        $user = new User;

        $user->company_id = 1;
        $user->role = $this->matchRole($data['role']);
        $user->username = $data['username'];
        $user->caps_id = $data['caps_id'];
        $user->name = $data['name'];
        $user->email = $data['email'];
        $user->cellphone = $data['cellphone'];
        $user->status = $data['status'];
        $user->part = $data['part'];
        $user->position = $data['position'];
        $user->menu = $data['menu'];
        $user->password = Hash::make($data['password']);

        $user->save();
    }

    /**
     * 사용자 정보 업데이트<br>
     * soft delete 된 회원 정보도 되돌릴 수 있음<br>
     * 추후 restore() 메서드를 활용 해서 관리자만 처리할 수 있도록 수정할 것
     *
     * @param int $id
     * @param array $data
     * @return void
     */
    public function updateUser(int $id, array $data): void
    {
        $user = User::withTrashed()->findOrFail($id);

        // 삭제된 직원을 다시 활성화 시키면
        if ($user->status === User::MEMBER_STATUS_DELETED) {
            $user->status = User::MEMBER_STATUS_ACTIVE;
            $user->deleted_at = null;
        }

        $user->role = $this->matchRole($data['role']);
        $user->username = $data['username'];
        $user->caps_id = $data['caps_id'];
        $user->name = $data['name'];
        $user->email = $data['email'];
        $user->cellphone = $data['cellphone'];
        $user->part = $data['part'];
        $user->position = $data['position'];
        $user->menu = $data['menu'];
        if (array_key_exists('password', $data)) {
            $user->password = Hash::make($data['password']);
        }

        $user->save();
    }

    /**
     * 사용자 삭제(soft deletion)
     *
     * @param int $id
     * @return void
     */
    public function deleteUser(int $id): void
    {
        $user = User::findOrFail($id);
        $user->status = User::MEMBER_STATUS_DELETED;
        $user->save();

        // soft delete
        $user->delete();
    }

    /**
     * 사용자 복구(활성)
     *
     * @param int $id
     * @return void
     */
    public function restoreUser(int $id): void
    {
        /**
         * @var User $user
         */
        $user = User::withTrashed()->findOrFail($id);
        $user->restore();

        // 상태를 활성화 상태로 변경 (필요한 경우)
        $user->status = User::MEMBER_STATUS_ACTIVE;
        $user->save();
    }

    /**
     * @param $role
     * @return string
     */
    private function matchRole($role): string
    {
        return match (strtolower($role)) {
            'employee' => User::ROLE_EMPLOYEE,
            'receiving-manager' => User::ROLE_RECEIVING_MANAGER,
            'pallet-manager' => User::ROLE_PALLET_MANAGER,
            'carryout-manager' => User::ROLE_CARRYOUT_MANAGER,
            'admin' => User::ROLE_ADMIN,
            'super-admin' => User::ROLE_SUPER_ADMIN,
            default => User::ROLE_GUEST,
        };
    }
}
