<?php

namespace App\Services;

use App\Models\Cate4;
use App\Models\Cate5;
use Illuminate\Database\Eloquent\Collection;

class CategoryService
{
    public function getAllCategories(): Collection|array
    {
        return Cate4::with('cate5')->orderBy('name')->get();
    }

    public function getCate4($id): Cate4|null
    {
        return Cate4::find($id);
    }

    public function getCate5($id): Cate5|null
    {
        return Cate5::find($id);
    }
}
