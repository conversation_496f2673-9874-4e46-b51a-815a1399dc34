<?php

namespace App\Services;

use App\Models\ActivityLog;
use App\Models\Product;
use App\Models\User;

class ActivityLogService
{
    protected User $user;

    public function __construct(User $user = null)
    {
        $this->user = $user;
    }

    public function createQaidInfo(Product $product, array $data): void
    {
        activity()
            ->useLog('qaid') # log_name
            ->performedOn($product) # subject
            ->causedBy($this->user) # causer
            ->withProperties($data) # set any required extra properties
            ->log($product->qaid); # description
    }

    /**
     * activity_log 에 저장된 QAID의 기록을 찾아서 새로운 정보를 추가한다.
     *
     * @param  Product  $product
     * @param  array  $data
     * @return void
     */
    public function addQaidInfo(Product $product, array $data): void
    {
        $log = ActivityLog::where('description', $product->qaid)->first();

        $properties = $log->properties;
        $properties[] = $data;

        $log->properties = $properties;

        $log->save();
    }
}
