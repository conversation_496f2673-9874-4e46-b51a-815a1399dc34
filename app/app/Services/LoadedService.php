<?php

namespace App\Services;

use App\Exceptions\ProductException;
use App\Helpers\HelperLibrary;
use App\Models\Carryout;
use App\Models\CarryoutProduct;
use App\Models\Location;
use App\Models\Pallet;
use App\Models\PalletProduct;
use App\Models\Product;
use App\Models\ProductLog;
use App\Models\RepairProduct;
use App\Models\User;
use App\Models\WorkStatus;
use App\Traits\Repair\ProductPartsTrait;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Throwable;

class LoadedService
{
    use ProductPartsTrait;

    // 캐시 만료 시간 설정 (초 단위)
    private const CACHE_TTL = 3600; // 1시간

    // 기본 캐시 키
    private const CACHE_KEY = 'loaded_pallets_data';

    // 기본 반환 데이터 설정
    private array $defaultData = [
        'isLocation' => false,
        'palletGradeCode' => '',
        'palletProdCount' => 0,
        'palletRecentProducts' => [],
    ];

    protected PalletService $palletService;
    protected WorkStatusService $workStatusService;
    private ProductLogService $logger;

    public function __construct(
        PalletService $palletService,
        WorkStatusService $workStatusService,
        ProductLogService $logger
    ) {
        $this->palletService = $palletService;
        $this->workStatusService = $workStatusService;
        $this->logger = $logger;
    }

    /**
     * 모든 팔레트 데이터를 가져와 캐시에 저장하는 메서드
     *
     * @return array 모든 팔레트 데이터
     */
    private function getAllPalletsData(): array
    {
        // 캐시에서 데이터 가져오기 시도
        return Cache::remember(self::CACHE_KEY, self::CACHE_TTL, function() {
            // 쿼리 빌더 준비
            $locations = Location::with(['pallets' => function($query) {
                $query->whereIn('status', [Pallet::STATUS_REGISTERED, Pallet::STATUS_LOADED])
                    ->with(['palletProducts' => function($query) {
                        $query->where('status', PalletProduct::STATUS_REGISTERED)
                            ->with(['product', 'repairGrade']);
                    }]);
            }])
                ->where('enable', 'Y')
                ->where('place', Location::LOCATION_COUNTRY_KR . '-' . Location::LOCATION_CITY_ESCS)
                ->where('code', 'like', 'A-1-1-%')
                ->orderBy('id', 'desc')
                ->get();

            $items = [];
            foreach ($locations as $location) {
                $codeList = explode("-", $location->code);
                $palletNo = $this->makePalletCode($codeList[3], $codeList[4]);

                $pallet = $location->pallets->first();
                $itemData = null;

                if ($pallet !== null) {
                    $palletProducts = $pallet->palletProducts;

                    if ($palletProducts->isNotEmpty()) {
                        $palletGradeCode = $palletProducts->first()->repairGrade->code ?? '';
                        $palletProdCount = $palletProducts->count();
                        $palletRecentProducts = $palletProducts->sortByDesc('id')
                            ->take(10)
                            ->map(fn($palletProduct) => $palletProduct->product->name)
                            ->reverse()
                            ->values()
                            ->all();

                        $itemData = [
                            'isLocation' => true,
                            'palletGradeCode' => $palletGradeCode,
                            'palletProdCount' => $palletProdCount,
                            'palletRecentProducts' => $palletRecentProducts
                        ];
                    }
                }

                if ($itemData === null) {
                    $itemData = [
                        'isLocation' => true,
                        'palletGradeCode' => '',
                        'palletProdCount' => 0,
                        'palletRecentProducts' => []
                    ];
                }

                $items[$palletNo] = array_merge(['pallet_no' => $palletNo], $itemData);
            }

            SimpleLogService::info('pallet', '팔레트 데이터 캐시 생성', [ 'count' => count($items) ]);

            return $items;
        });
    }

    /**
     * 요청된 팔레트 데이터를 반환하는 메서드
     *
     * @param string|null $level 레벨
     * @param string|null $column 열
     * @return array 팔레트 데이터 배열
     */
    public function getLoadedPalletsWithData(?string $level = null, ?string $column = null): array
    {
        $requestedPalletNo = $this->makePalletCode($level, $column);

        // 모든 팔레트 데이터 가져오기
        $allPallets = $this->getAllPalletsData();

        // 결과 배열 준비
        $result = [];

        // 모든 팔레트 데이터를 결과 배열에 추가
        foreach ($allPallets as $palletNo => $palletData) {
            $result[] = $palletData;
        }

        // 요청된 팔레트가 등록되지 않았으면 빈 항목 추가
        if ($requestedPalletNo !== "" && !isset($allPallets[$requestedPalletNo])) {
            $result[] = [
                'pallet_no' => $requestedPalletNo,
                'isLocation' => false,
                'palletGradeCode' => '',
                'palletProdCount' => 0,
                'palletRecentProducts' => []
            ];
        }

        return $result;
    }

    /**
     * 특정 팔레트 번호에 대한 데이터만 가져오는 메서드
     *
     * @param string $palletNo 팔레트 번호
     * @return array 해당 팔레트 데이터
     */
    public function getPalletData(string $palletNo): array
    {
        $allPallets = $this->getAllPalletsData();

        return $allPallets[$palletNo] ?? [
            'pallet_no' => $palletNo,
            'isLocation' => false,
            'palletGradeCode' => '',
            'palletProdCount' => 0,
            'palletRecentProducts' => []
        ];
    }

    public function getLoadedPallet(Location $location): object|null
    {
        $query = Pallet::with(['palletProducts' => function ($query) {
            $query->where('status', PalletProduct::STATUS_REGISTERED);
        }, 'palletProducts.product'])
            ->where('location_id', $location->id)
            ->whereIn('status', [Pallet::STATUS_REGISTERED, Pallet::STATUS_LOADED]);

        SimpleLogService::debug('pallet', '팔레트 조회 쿼리', [
            'query' => $query->toRawSql(),
            'query_data' => $query->first(),
        ]);

        return $query->first();
    }

    public function addItem(array $items, string $palletNo, array $data = null): array
    {
        if ($data === null) {
            $data = $this->defaultData;
        }

        $items[] = array_merge(['pallet_no' => $palletNo], $data);

        return $items;
    }

    public function getLoadedPalletData($pallet): array
    {
        $palletProducts = $pallet->palletProducts;

        // 팔레트 제품이 없을 경우 기본 반환 값 반환 (isLocation만 true로 변경)
        if ($palletProducts === null) {
            return array_merge($this->defaultData, ['isLocation' => true]);
        }

        // 팔레트 제품이 있는 경우 데이터 설정
        $palletGradeCode = $pallet->repairGrade?->code ?? '';
        $palletProdCount = $palletProducts->count();
        $palletRecentProducts = $palletProducts->sortByDesc('id')
            ->take(10)
            ->map(fn($palletProduct) => $palletProduct->product->name)
            ->reverse()
            ->values()
            ->all();

        return [
            'isLocation' => true,
            'palletGradeCode' => $palletGradeCode ?? '',
            'palletProdCount' => $palletProdCount ?? 0,
            'palletRecentProducts' => $palletRecentProducts ?? []
        ];
    }

    public function makePalletCode($level, $column): string
    {
        if (empty($level) || empty($column)) {
            return '';
        }

        return $level . '-' . $column;
    }

    public function getSelectedPallet($data): array
    {
        $location = Location::where([
            'place' => $data['place'],
            'code' => $data['code'],
            'enable' => 'Y'
        ])->first();

        $isLocation = false;
        $palletGradeCode = '';
        $palletProdCount = 0;
        $palletRecentProducts = [];
        if ($location) {
            $pallet = $this->getLoadedPallet($location);

            if($pallet !== null){
                list('isLocation' => $isLocation,
                    'palletGradeCode' => $palletGradeCode,
                    'palletProdCount' => $palletProdCount,
                    'palletRecentProducts' => $palletRecentProducts
                ) = $this->getLoadedPalletData($pallet);
            }
        }

        return [
            'isLocation' => $isLocation,
            'palletGradeCode' => $palletGradeCode,
            'palletProdCount' => $palletProdCount,
            'palletRecentProducts' => $palletRecentProducts,
        ];
    }

    /**
     * 팔레트 적재시 적재 가능한 상품인지 확인
     * @param  string  $qaid
     * @return Product|null
     */
    public function getLoadedProductCheck(string $qaid): ?Product
    {
        return Product::distinct()
            ->select([
                'products.*',
                'reqs.req_at',
                DB::raw("(SELECT carryout_at FROM carryouts WHERE id = carryout_products.carryout_id AND status = ".Carryout::STATUS_CARRIED_IN.") as carryout_at"),
//                DB::raw("(SELECT code FROM repair_symptoms WHERE id = carryout_products.repair_symptom_id) as symptom_code"),
//                DB::raw("(SELECT code FROM repair_processes WHERE id = carryout_products.repair_process_id) as process_code"),
//                DB::raw("(SELECT code FROM repair_grades WHERE id = carryout_products.repair_grade_id) as grade_code"),
            ])
            ->without([ // $with에 정의된 관계들 중 이 쿼리에서 불필요한 것들을 제외
                'lot',
                'vendor',
                'link',
                'user',
                'checkedUser',
                'carryoutProducts',
            ])
            ->leftJoin('reqs', 'products.req_id', '=', 'reqs.id')
            ->leftJoin('carryout_products', function($join) {
                $join->on('products.id', '=', 'carryout_products.product_id')
                    ->where('carryout_products.status', '=', CarryoutProduct::STATUS_RENOVATED);
            })
            ->where('products.qaid', $qaid)
            ->whereNotIn('products.status', [Product::STATUS_HELD, Product::STATUS_DELETED])
            ->where('products.checked_status', Product::CHECKED_STATUS_CHECKED)
            ->orderByDesc('reqs.req_at')
            ->first();
    }

    /**
     * @todo: 사용 안 할듯...추후 이 부분이 필요 없다고 확신될 때 삭제할 것
     * 점검된 상품의 중복 상품이 있는지 확인 후 중복이 있다면 중복처리까지 완료하는 메서드
     * @throws Exception
     */
    public function handleCheckedProduct($palletProduct, $product, $qaid, $user): string
    {
        $pallet = $palletProduct->pallet->where('status', '!=', Pallet::STATUS_DELETED)->first();
        if ($pallet) {
            $message = "조회된 상품(QAID=" . $qaid . ")은 이미 점검완료한 상품입니다.";

            // 같은 qaid로 등록상태 다른 상품이 있는 경우엔 해당 상품 보류로 변경
            if ($product->status == Product::STATUS_CHECKED_ON_PALLET || $product->status == Product::STATUS_EXPORTED) {
                $duplicateProduct = Product::where('qaid', $qaid)
                    ->where('status', Product::STATUS_REGISTERED)
                    ->latest('id')
                    ->first();

                //등록상태의 같은 qaid상품이 조회되는 경우, 중복상품으로 표시하고 상품상태를 보류로 전환
                if ($duplicateProduct) {
                    // QAID 히스토리 로그 기록
                    $historyLogger = new HistoryLogger();
                    $historyLogger->setProduct($product)
                        ->setUser($user)
                        ->setTitle('QAID 중복 처리')
                        ->setPreviousStatus($product->status);

                    // 상품 테이블에 중복 표시
                    $duplicateProduct->update([
                        'duplicated' => 'Y',
                        'status' => Product::STATUS_HELD,
                    ]);

                    // 수집된 히스토리 데이터를 일괄 저장
                    try {
                        $historyLogger->setCurrentStatus($product->status)
                            ->setPalletId($pallet->id)
                            ->setPalletProductId($palletProduct->id)
                            ->save();
                    } catch (Exception $e) {
                        throw new Exception($e->getMessage());
                    }

                    // 카운터 기록
                    // 중복 상품일 경우 중복(duplicated): +1, 입고검수(대기)(unchecked): -1
                    $req_id = $duplicateProduct->req_id; # 카운팅을 하기 위해 req_id를 저장해 둔다
                    $counters[$req_id]['duplicated'] = 1;
                    $counters[$req_id]['unchecked'] = -1;

                    // 실제 카운터 기록
                    $countService = new CountService();
                    $countService->multipleUpdate($counters);
                }

                $message .= " 같은 QAID=" . $qaid . "로 중복된 상품(보류처리)이 있습니다.";
            }

            return $message;
        }

        return '';
    }

    public function isAlreadyChecked(Product $product): Model|null
    {
        return $product->palletProducts()
            ->where('status', '!=', PalletProduct::STATUS_REGISTERED)
            ->whereHas('pallet', function ($query) {
                $query->where('status', '!=', Pallet::STATUS_DELETED);
            })->first();
    }

    /**
     * 출고 팔레트에 적재 되는 모든 상품은 repair_products 테이블에 있는 상품만 가능하다.<br>
     *
     * @require repair_product_id  수리/점검 상품 인덱스
     * @throws Exception|Throwable
     */
    public function storeProductOnPallet(array $data, ?User $user): void
    {
        try {
            DB::beginTransaction();

            // 위치 저장 및 정보 가져 옴
            $locationService = new LocationService();
            $location = $locationService->store($data['location_place'], $data['location_code']);

            // 팔레트 저장 및 정보 가져 옴
            $pallet = $this->palletService->store($location, $data, $user);

            SimpleLogService::debug('pallet', '팔레트 생성 여부: ', [
                'pallet' => $pallet
            ]);

            $repairProduct = RepairProduct::with([
                'product',
                'product.palletProducts'
            ])
                ->without([
                    'waitingUser',
                    'completedUser',
                    'repairProductParts',
                ])
                ->where('id', $data['repair_product_id'])
                ->first();

            // 개선 방안
            if (!$repairProduct) {
                throw new Exception("수리/점검된 상품을 찾을 수 없습니다.");
            }

            // 상품 가져 옴
            $product = $repairProduct->product;
            if ($product === null) {
                throw ProductException::notFound($data['qaid']);
            }

            if ($product->palletProducts->isNotEmpty()) {
                throw new Exception("이미 팔레트에 적재된 상품 [".$product->qaid.":".$product->name."] 입니다.");
            }

            // 1. RepairProduct 정보 업데이트 (적재 전 점검에서 변경된 내용)
            $repairProduct = $this->updateRepairProductInfo($repairProduct, $data, $user);

            // 2. 구성품(RepairProductParts) 처리(insert, update, delete)
            if (isset($data['add_parts']) || isset($data['update_parts']) || isset($data['remove_parts'])) {
                $this->updateRepairProductParts($repairProduct, $data, $user, $this->workStatusService, $this->logger);
            }

            $palletProduct = new PalletProduct();
            $palletProduct->pallet_id = $pallet->id;
            $palletProduct->product_id = $product->id;
            $palletProduct->repair_product_id = $repairProduct->id;
            $palletProduct->status = PalletProduct::STATUS_REGISTERED;
            $palletProduct->registered_at = date("Y-m-d H:i:s");
            $palletProduct->registered_user_id = $user->id;
            $palletProduct->quantity = $product->quantity;
            $palletProduct->amount = $product->amount;
            $palletProduct->repair_symptom_id = $repairProduct->repair_symptom_id;
            $palletProduct->repair_process_id = $repairProduct->repair_process_id;
            $palletProduct->repair_grade_id = $repairProduct->repair_grade_id;
            $palletProduct->invoice1 = $repairProduct->invoice1;
            $palletProduct->invoice2 = $repairProduct->invoice2;
            $palletProduct->invoice3 = $repairProduct->invoice3;
            $palletProduct->memo = $data['memo'] ?? '';
            $palletProduct->save();

            // 상품 상태 업데이트
            $product->status = Product::STATUS_CHECKED_ON_PALLET;
            $product->save();

            // 로그 기록
            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PALLET_PRODUCT_INSPECT, # 상태 변환/통계::팔레트 상품(적재중)
                WorkStatus::LINK_PALLET_PRODUCT_REGISTERED # 상태 변환/통계::상품 상태점검완료(적재중)
            ]);

            if (isset($statusIds[WorkStatus::LINK_PALLET_PRODUCT_REGISTERED])) {
                $this->logger->addLog($product, 'App\Models\PalletProduct', $palletProduct->id, $statusIds[WorkStatus::LINK_PALLET_PRODUCT_REGISTERED], $user->id, "QAID[$product->qaid]: 출고 상품 적재");
            }

            if (isset($statusIds[WorkStatus::LINK_PALLET_PRODUCT_INSPECT])) {
                $this->logger->addLog($product, 'App\Models\PalletProduct', $palletProduct->id, $statusIds[WorkStatus::LINK_PALLET_PRODUCT_INSPECT], $user->id, "QAID[$product->qaid]: 출고 상태(미검수)");
            }
            // 로그 저장
            $this->logger->save();

            /**
             * 점검완료(적재중) 카운팅<br>
             * 수리/점검완료(창고) : repaired - 1, 점검완료(적재중) : checked_out + 1
             */
            // 카운터 기록
            $countService = new CountService();
            $counters[$product->req_id] = [
                'repaired' => -1,
                'checkout' => 1
            ];
            $countService->multipleUpdate($counters);

            DB::commit();

            // 팔레트에 상품이 추가되면 관련 캐시 무효화
            HelperLibrary::forgetCache(self::CACHE_KEY);
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('pallet', '쿠팡RP상품 수리/점검완료 처리 실패', [], $e);

            throw $e;
        }
    }

    /**
     * RepairProduct 정보를 업데이트하는 메서드
     *
     * @param RepairProduct $repairProduct 업데이트할 RepairProduct 모델
     * @param array $data 업데이트할 데이터 배열
     * @param User|null $user 사용자 정보
     * @return RepairProduct 업데이트된 RepairProduct 모델
     * @throws Exception
     */
    private function updateRepairProductInfo(RepairProduct $repairProduct, array $data, ?User $user): RepairProduct
    {
        // 업데이트할 필드 매핑 정의
        $fieldMappings = [
            'symptom_id' => 'repair_symptom_id',
            'process_id' => 'repair_process_id',
            'grade_id' => 'repair_grade_id',
            'invoice1' => 'invoice1',
            'invoice2' => 'invoice2',
            'invoice3' => 'invoice3',
            'memo' => 'memo'
        ];

        // 업데이트할 데이터 수집
        $repairProductUpdates = [];

        // 일반 필드들 처리
        foreach ($fieldMappings as $inputKey => $dbField) {
            if (isset($data[$inputKey])) {
                $repairProductUpdates[$dbField] = $data[$inputKey];
            }
        }

        // 특별한 처리 필요한 필드들
        if (isset($data['os_reinstall'])) {
            $repairProductUpdates['is_os_install'] = $data['os_reinstall'] === true ? 1 : 0;
        }

        // RepairProduct 업데이트 실행
        if (!empty($repairProductUpdates)) {
            $repairProduct->update($repairProductUpdates);

            // 업데이트 기록이 있다면 로그 기록(저장은 불러 온 곳에서)
            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_REPAIR_COMPLETE, # 상태 변환/통계::수리/점검 완료
            ]);

            // 상품 가져 옴
            $product = $repairProduct->product;
            if (isset($statusIds[WorkStatus::LINK_REPAIR_COMPLETE])) {
                $this->logger->addLog($product, 'App\Models\RepairProduct', $repairProduct->id, $statusIds[WorkStatus::LINK_REPAIR_COMPLETE], $user->id, "QAID[$product->qaid]: 수리/점검 정보 업데이트 됨");
            }

            // 업데이트된 데이터로 객체 새로고침
            $repairProduct->refresh();
        }

        return $repairProduct;
    }
}
