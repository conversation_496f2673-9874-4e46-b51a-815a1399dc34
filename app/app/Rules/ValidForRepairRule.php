<?php

namespace App\Rules;

use App\Models\Product;
use App\Models\RepairProduct;
use App\Services\LoadedService;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidForRepairRule implements ValidationRule
{
    private string $message = '';
    private LoadedService $loadedService;

    public function __construct()
    {
        $this->loadedService = app(LoadedService::class);
    }

    /**
     * 유효성 검사 규칙을 실행합니다.
     *
     * @param string $attribute
     * @param mixed $value
     * @param \Closure $fail
     * @return void
     */
    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        // 1. 먼저 ID로 상품을 찾습니다.
        $product = Product::with('repairProduct')->find($value);

        // 2. 상품이 존재하지 않는 경우
        if (!$product) {
            $fail('존재하지 않는 상품입니다.');
            return;
        }

        // 2. 입고 검수 상태 확인
        if ($product->checked_status === Product::CHECKED_STATUS_UNCHECKED) {
            $fail('입고 검수를 먼저 완료해야 하는 상품입니다.');
            return;
        }

        // 3. 미입고 상태 확인
        if ($product->checked_status === Product::CHECKED_STATUS_UNDELIVERED) {
            $fail('미입고 된 상품 입니다. 관리자에게 문의해 주세요.');
            return;
        }

        // 4. 상품의 현재 상태(status)에 따라 분기 처리 (사용자 요청 사항)
        if (!in_array($product->status, [Product::STATUS_REGISTERED, Product::STATUS_WAITING])) {
            // switch 문을 사용하면 상태별로 다른 메시지를 깔끔하게 처리할 수 있습니다.
            $message = match ($product->status) {
                Product::STATUS_REPAIRED => '이미 수리/점검이 완료된 상품입니다.',
                Product::STATUS_CHECKED_ON_PALLET => '이미 출고 팔레트에 적재된 상품입니다.',
                Product::STATUS_CARRIED_OUT => '반출 중인 상품으로 수리/점검이 불가합니다.',
                Product::STATUS_EXPORTED => '이미 출고 완료된 상품으로 수리/점검이 불가합니다.',
                Product::STATUS_HELD => '출고 보류된 상품으로 수리/점검이 불가합니다.',
                Product::STATUS_DELETED => '삭제된 상품으로 수리/점검이 불가합니다.',
                default => '현재 수리/점검을 진행할 수 없는 상태의 상품입니다.',
            };
            $fail($message);
            return;
        }

        // 5. 상품이 중복이라면
        if ($product->duplicated === Product::IS_DUPLICATED_Y) {
            $fail('[QAID: ' . $product->qaid . ']는 중복된 상품입니다. 관리자에게 문의해 주세요.');
            return;
        }

        // 6. 기존 수리 내역(repairProduct) 상태 확인 (수정된 로직)
        $hasNoRepair = !$product->repairProduct;
        $hasWaitingRepair = !$hasNoRepair && $product->repairProduct->status === RepairProduct::STATUS_WAITING;

        // 수리 내역이 아예 없거나, 대기 상태가 아니면 실패 처리
        if (!($hasNoRepair || $hasWaitingRepair)) {
            $fail('이미 수리가 완료 되었거나 다른 상태의 수리 내역이 존재하여 진행할 수 없습니다.');
            return;
        }

        // 6. 최종적으로 다른 곳에서 이미 처리되었는지 확인
        if ($this->loadedService->isAlreadyChecked($product)) {
            $fail("검색된 상품(" . $product->qaid . " - " . $product->name . ")은 이미 출고처리 되어 있는 상품입니다.");
            return;
        }
    }
}
