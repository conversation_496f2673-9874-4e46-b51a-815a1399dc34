<?php

namespace App\Helpers;

use App\Services\SimpleLogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class HelperLibrary
{
    /**
     * storage 디렉토리 하위에 원하는 디렉토리를 생성
     *
     * @param string $dir 디렉토리 경로
     *
     * @return void
     */
    public static function makeDir(string $dir): void
    {
        $storagePath = storage_path($dir);

        // 디렉토리가 있는지 확인
        if (!file_exists($storagePath)) {
            // 디렉토리가 없으면 생성
            mkdir($storagePath, 0755, true);
        }
    }

    /**
     * 파일 업로드(storage/app 디렉토리 하위에 생성됨)
     *
     * @param Request $request
     * @param string $fileKey
     * @param string $dir 업로드할 경로
     * @return string
     */
    public static function uploadFile(Request $request, string $fileKey = 'file', string $dir = 'uploads'): string
    {
        Storage::makeDirectory($dir);

        $file = $request->file($fileKey);

        return $file->storeAs(
            $dir, $file->hashName()
        );
    }

    /**
     * 파일 절대 경로 리턴(storage/app 디렉토리 하위)
     *
     * @param  string  $dir
     * @param  string  $filename
     * @return string
     */
    public static function getAbsoluteFilePath(string $dir, string $filename): string
    {
        // 지정된 디렉토리가 없으면 생성
        Storage::makeDirectory($dir);

        // 절대 경로를 제공: 파일의 정확한 위치를 쉽게 파악하고 접근할 수 있게 해줌
        return Storage::path($dir . '/' . $filename);
    }

    /**
     * 토큰에 사용될 랜덤 문자열을 만든다.
     *
     * 이 함수는 주어진 문자들을 무작위로 선택하여 특정 길이의 문자열을 생성하는 역할을 합니다.
     * 함수의 파라미터는 아래와 같습니다:
     *
     * 1. $characters: 단일 문자열로, 이 문자열 안에 있는 문자들 중에서 무작위로 선택될 문자가 포함되어 있습니다.
     * 2. $length: 생성하려는 문자열의 길이입니다. 기본값으로 1이 설정되어 있습니다.
     *
     * 함수의 내부 동작은 아래와 같습니다:
     *
     * 1. collect(range(1, $length)): 주어진 길이($length)만큼의 범위를 가지는 배열을 생성하고, Laravel의 컬렉션 인스턴스로 변경합니다. 예를 들어, $length가 5라면 [1, 2, 3, 4, 5]라는 배열을 생성하고 이를 컬렉션으로 만듭니다.
     *
     * 2. ->map(function () use ($characters) {...}: 이 컬렉션의 각 항목에 대해 map 메서드를 사용하여 함수를 적용합니다. 이 함수는 $characters에서 무작위로 문자를 선택하여 반환합니다. mt_rand(0, strlen($characters) - 1)는 0과 문자열 $characters의 길이-1 사이의 임의의 수를 반환하는데, 이를 인덱스로 사용해 $characters 문자열에서 문자를 선택합니다.
     *
     * 3. ->implode(''): map 함수를 통해 생성된 각각의 문자들을 결합하여 하나의 문자열을 생성합니다.
     *
     * 따라서, 이 함수는 $characters 문자열에 포함된 문자들로 구성된 $length 길이를 가진 랜덤 문자열을 생성하고 반환하는 역할을 합니다.
     */
    public static function generateRandomString(string $characters, int $length = 1): string
    {
        return collect(range(1, $length))->map(function () use ($characters) {
            return $characters[mt_rand(0, strlen($characters) - 1)];
        })->implode('');
    }

    /**
     * 바코드 및 설명(상품명)으로 해시를 생성
     *
     * @param  string  $barcode  The barcode string.
     * @param  string  $description  The description string. (엑셀 파일에서 넘어오는 형식)
     * @return string The generated hash.
     */
    public static function makeHashForBarcode(string $barcode, string $description, string $wmsSkuId, string $externalSkuId): string
    {
        $barcode = trim($barcode);
        $description = mb_strtolower(preg_replace('/\s+/', '', trim($description)));
        $wmsSkuId = trim($wmsSkuId);
        $externalSkuId = trim($externalSkuId);

        return hash('sha256',$barcode . $description . $wmsSkuId . $externalSkuId);
    }

    /**
     * 다운로드 헤더 설정<br>
     * 생성된 엑셀 데이터를 스트리밍 형식으로 바로 다운로드
     *
     * @param  string  $filename
     * @param  int  $size
     * @param  string  $origin
     * @return void
     */
    public static function excelDownloadHeaders(string $filename, int $size, string $origin): void
    {
        header('Access-Control-Allow-Origin: ' . $origin);
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Expose-Headers: Content-Disposition'); # 클라이언트 헤더에 보이도록 처리
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        header('Content-Transfer-Encoding: binary');
        header('Content-Length: ' . $size);
        header('Cache-Control: must-revalidate');
        header('Expires: 0');
        header('Pragma: public');
    }

    /**
     * 상품명 문자열에서 크기(숫자)와 단위(cm 또는 inch)를 추출합니다.
     * 소수점 cm 값도 처리합니다.
     *
     * @param string $productName 상품명
     * @return array|null 크기 정보 ('size' => float|int, 'feeUnit' => string) 또는 null (추출 실패 시)
     */
    public static function extractSizeFromName(string $productName): ?array
    {
        // 1. 상품명에서 직접적인 크기 단위(cm) 찾기 (소수점 포함 가능, 유니코드)
        // 센치, 센티미터 같은 한글 단위 처리를 위해 'u' 추가
        if (preg_match('/(\d{2,3}(?:\.\d+)?)\s?(?:cm|센치|센티미터)/iu', $productName, $matches)) {
            return ['size' => floatval($matches[1]), 'feeUnit' => 'cm'];
        }

        // 2. 상품명에서 직접적인 크기 단위(인치/형) 찾기 (유니코드)
        // '형', '인치' 같은 한글 및 \b 처리를 위해 'u' 추가
        if (preg_match('/(\d{2,3}(?:\.\d+)?)\s?(?:inch|인치|형)\b/iu', $productName, $matches)) {
            return ['size' => floatval($matches[1]), 'feeUnit' => 'inch'];
        }

        // "ULTRON" 뒤 네자리 숫자 중 앞 두자리를 크기로 인식 (대소문자 무시)
        if (preg_match('/\bULTRON\s?(\d{2})\d{2}\b/i', $productName, $matches)) {
            $size = intval($matches[1]); // 정수로 처리
            // 추출된 크기가 합리적인지 확인 (예: 15 ~ 100)
            if ($size >= 15 && $size <= 100) {
                return ['size' => $size, 'feeUnit' => 'inch'];
            }
        }

        // 3. 모델명으로 추정되는 부분에서 크기 찾기 (대소문자 구분 없음)
        // 모델명은 주로 영문/숫자이므로 'u'는 필수는 아닐 수 있으나, 일관성을 위해 추가해도 무방
        $modelPatterns = [
            '/\b(\d{2,3})(?!Hz\b)[A-Z]+[A-Z0-9]*\b/i', // 패턴 1: 숫자 시작, 바로 뒤 문자(주사율 Hz는 제외)
            '/\b(\d{2})\d+[A-Z][A-Z0-9]*\b/i', // 패턴 1.1: 숫자 시작, 뒤 숫자 더 오고 문자
            '/\b[A-Z]{1,4}(\d{2})[A-Z0-9]*\b/i', // 패턴 2: 알파벳 시작, 중간 숫자
            '/\b[A-Z]+(\d{2})\b/i',             // 패턴 3: 문자 뒤 숫자
            '/\b(\d{2})[A-Z]+\b/i',             // 패턴 3: 숫자 뒤 문자
        ];

        $feeUnit = 'inch'; // 기본값은 inch
        foreach ($modelPatterns as $pattern) {
            if (preg_match_all($pattern, $productName, $matchesAll, PREG_PATTERN_ORDER)) {
                foreach ($matchesAll[1] as $potentialSize) {
                    $numSize = (int)$potentialSize;
                    // 합리적인 TV/모니터 크기 범위인지 확인 (예: 15인치 ~ 100인치)
                    if ($numSize >= 15 && $numSize <= 100) {
                        return ['size' => $numSize, 'feeUnit' => $feeUnit];
                    }
                }
            }
        }

        // 4. 위의 모든 패턴에 해당하지 않을 경우, 상품명 마지막에 있는 숫자를 추출 (소수 포함)
        if (preg_match_all('/(\d+(?:\.\d+)?)/', $productName, $matchesAll)) {
            // 찾은 모든 숫자 중 마지막 숫자를 사용
            $lastNumberStr = end($matchesAll[1]);
            $size = floatval($lastNumberStr);

            // 추출된 크기가 유효한지 간단히 확인 (예: 0보다 큰지)
            if ($size > 0) {
                return ['size' => $size, 'feeUnit' => $feeUnit];
            }
        }

        // 어떤 크기 정보도 찾지 못한 경우
        return null;
    }

    /**
     * 캐시를 무효화하는 메서드
     */
    public static function forgetCache($cacheKey): void
    {
        Cache::forget($cacheKey);
        SimpleLogService::info('캐시 삭제', '캐시키: ' . $cacheKey);
    }
}
