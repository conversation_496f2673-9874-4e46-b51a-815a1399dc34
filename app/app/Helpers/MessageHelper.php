<?php

namespace App\Helpers;

/**
 * 메시지 헬퍼 클래스
 * 
 * 일관된 오류 메시지와 성공 메시지를 생성하기 위한 헬퍼 클래스입니다.
 */
class MessageHelper
{
    // 공통 메시지 템플릿
    const RESOURCE_NOT_FOUND = "해당 %s [%s]이 존재하지 않습니다.";
    const RESOURCE_ALREADY_PROCESSED = "해당 %s [%s]은 이미 처리된 항목입니다.";
    const REQUIRED_FIELD_MISSING = "필수 필드가 누락되었습니다: %s";
    const INVALID_STATUS = "올바르지 않은 상태입니다: %s";
    const PERMISSION_DENIED = "해당 작업에 대한 권한이 없습니다.";
    const OPERATION_SUCCESS = "%s 처리가 성공적으로 완료되었습니다.";
    const OPERATION_FAILED = "%s 처리 중 오류가 발생했습니다.";

    // 상품 관련 메시지
    const PRODUCT_NOT_FOUND = "해당 상품 [%s]이 존재하지 않습니다.";
    const PRODUCT_INVALID_STATUS = "해당 상품 [%s]의 상태가 올바르지 않습니다.";
    const PRODUCT_ALREADY_PROCESSED = "해당 상품 [%s]은 이미 처리된 상품입니다.";

    // 외주 관련 메시지
    const CARRYOUT_NOT_FOUND = "해당 외주 반출 정보 [%s]가 존재하지 않습니다.";
    const PRODUCT_ALREADY_CARRIED_OUT = "해당 상품 [%s]은 이미 외주 반출 처리된 상품입니다.";
    const PRODUCT_NOT_CARRIED_OUT = "해당 상품 [%s]은 외주 반출 상태가 아닙니다.";
    const PRODUCT_ALREADY_IMPORTED = "해당 상품 [%s]은 이미 반입 완료된 상품입니다.";
    const CARRYOUT_EXPORT_SUCCESS = "외주 반출 처리가 성공적으로 완료되었습니다.";
    const CARRYOUT_IMPORT_SUCCESS = "외주 반입 처리가 성공적으로 완료되었습니다.";

    // 창고 관련 메시지 (확장 예시)
    const WAREHOUSE_NOT_FOUND = "해당 창고 [%s]가 존재하지 않습니다.";
    const WAREHOUSE_FULL = "해당 창고 [%s]의 용량이 부족합니다.";

    // 출고 관련 메시지 (확장 예시)
    const SHIPPING_NOT_FOUND = "해당 출고 정보 [%s]가 존재하지 않습니다.";
    const SHIPPING_ALREADY_COMPLETED = "해당 출고 [%s]는 이미 완료된 상태입니다.";

    /**
     * 메시지 템플릿에 값을 삽입하여 포맷팅합니다.
     */
    public static function format(string $template, ...$args): string
    {
        return sprintf($template, ...$args);
    }

    /**
     * 리소스를 찾을 수 없을 때 메시지를 생성합니다.
     */
    public static function resourceNotFound(string $resource, string $identifier): string
    {
        return self::format(self::RESOURCE_NOT_FOUND, $resource, $identifier);
    }

    /**
     * 이미 처리된 리소스에 대한 메시지를 생성합니다.
     */
    public static function alreadyProcessed(string $resource, string $identifier): string
    {
        return self::format(self::RESOURCE_ALREADY_PROCESSED, $resource, $identifier);
    }

    /**
     * 상품을 찾을 수 없을 때 메시지를 생성합니다.
     */
    public static function productNotFound(string $qaid): string
    {
        return self::format(self::PRODUCT_NOT_FOUND, $qaid);
    }

    /**
     * 상품 상태가 올바르지 않을 때 메시지를 생성합니다.
     */
    public static function productInvalidStatus(string $qaid): string
    {
        return self::format(self::PRODUCT_INVALID_STATUS, $qaid);
    }

    /**
     * 외주 정보를 찾을 수 없을 때 메시지를 생성합니다.
     */
    public static function carryoutNotFound(string $carryoutId): string
    {
        return self::format(self::CARRYOUT_NOT_FOUND, $carryoutId);
    }

    /**
     * 이미 외주 반출된 상품에 대한 메시지를 생성합니다.
     */
    public static function productAlreadyCarriedOut(string $qaid): string
    {
        return self::format(self::PRODUCT_ALREADY_CARRIED_OUT, $qaid);
    }

    /**
     * 외주 반출되지 않은 상품에 대한 메시지를 생성합니다.
     */
    public static function productNotCarriedOut(string $qaid): string
    {
        return self::format(self::PRODUCT_NOT_CARRIED_OUT, $qaid);
    }

    /**
     * 이미 반입 완료된 상품에 대한 메시지를 생성합니다.
     */
    public static function productAlreadyImported(string $qaid): string
    {
        return self::format(self::PRODUCT_ALREADY_IMPORTED, $qaid);
    }

    /**
     * 필수 필드 누락 메시지를 생성합니다.
     */
    public static function requiredFieldMissing(string $field): string
    {
        return self::format(self::REQUIRED_FIELD_MISSING, $field);
    }

    /**
     * 권한 부족 메시지를 생성합니다.
     */
    public static function permissionDenied(): string
    {
        return self::PERMISSION_DENIED;
    }

    /**
     * 작업 성공 메시지를 생성합니다.
     */
    public static function operationSuccess(string $operation): string
    {
        return self::format(self::OPERATION_SUCCESS, $operation);
    }

    /**
     * 작업 실패 메시지를 생성합니다.
     */
    public static function operationFailed(string $operation): string
    {
        return self::format(self::OPERATION_FAILED, $operation);
    }

    /**
     * 외주 반출 성공 메시지를 생성합니다.
     */
    public static function carryoutExportSuccess(): string
    {
        return self::CARRYOUT_EXPORT_SUCCESS;
    }

    /**
     * 외주 반입 성공 메시지를 생성합니다.
     */
    public static function carryoutImportSuccess(): string
    {
        return self::CARRYOUT_IMPORT_SUCCESS;
    }

    /**
     * 사용자 정의 메시지 템플릿을 사용하여 메시지를 생성합니다.
     */
    public static function custom(string $template, array $replacements = []): string
    {
        $message = $template;
        foreach ($replacements as $key => $value) {
            $message = str_replace("{{$key}}", $value, $message);
        }
        return $message;
    }

    /**
     * 다국어 지원을 위한 메시지 키 반환 (향후 확장용)
     */
    public static function getMessageKey(string $type, string $action): string
    {
        return "messages.{$type}.{$action}";
    }
}