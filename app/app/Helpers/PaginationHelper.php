<?php

namespace App\Helpers;

use Illuminate\Pagination\LengthAwarePaginator;

class PaginationHelper
{
    /**
     * 페이지네이션 정보를 최적화된 형태로 변환
     */
    public static function optimize(LengthAwarePaginator $paginator): array
    {
        return [
            'total' => $paginator->total(),
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'per_page' => $paginator->perPage(),
            'from' => $paginator->firstItem() ?? 0,
            'to' => $paginator->lastItem() ?? 0,
            'has_prev' => $paginator->previousPageUrl() !== null,
            'has_next' => $paginator->nextPageUrl() !== null,
        ];
    }

    /**
     * 페이지네이션 응답 구조를 최적화된 형태로 변환
     */
    public static function response(LengthAwarePaginator $paginator, array $additionalData = []): array
    {
        return [
            'success' => true,
            'data' => array_merge([
                'items' => $paginator->items(),
                'pagination' => self::optimize($paginator),
            ], $additionalData)
        ];
    }
}
