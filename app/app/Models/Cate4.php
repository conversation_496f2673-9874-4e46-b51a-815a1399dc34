<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cate4 extends Model
{
    use HasFactory;

    protected $table = 'cate4';

    public $timestamps = false;

    protected $fillable = ['cate4_no', 'name'];

    protected $hidden = [];
    protected $casts = [];

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'cate4_id', 'id');
    }

    public function cate5(): HasMany
    {
        return $this->hasMany(Cate5::class, 'cate4_id', 'id');
    }

    public function partsLogs(): HasMany
    {
        return $this->hasMany(RepairPartsLog::class, 'cate4_id', 'id');
    }
}
