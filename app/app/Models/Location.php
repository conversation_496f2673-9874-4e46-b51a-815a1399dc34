<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Location extends Model
{
    use HasFactory;

    const LOCATION_COUNTRY_KR = "KR";
    const LOCATION_COUNTRY_US = "US";

    public static $LOCATION_COUNTRY_NAMES = [
        self::LOCATION_COUNTRY_KR => "한국",
        self::LOCATION_COUNTRY_US => "미국",
    ];

    const LOCATION_CITY_GS = "GS";
    const LOCATION_CITY_PJ = "PJ";
    const LOCATION_CITY_LA = "LA";
    const LOCATION_CITY_ESCS = "ESCS";

    public static $LOCATION_CITY_NAMES = [
        self::LOCATION_CITY_GS => "고산(주덕)",
        self::LOCATION_CITY_PJ => "파주",
        self::LOCATION_CITY_LA => "Los Angeles",
        self::LOCATION_CITY_ESCS => "음성나동(코너스톤)",
    ];

    const F_ENABLE_Y = 'Y';
    const F_ENABLE_N = 'N';

    public static $F_ENABLE_NAMES = [
        self::F_ENABLE_Y => "적재가능",
        self::F_ENABLE_N => "사용불가"
    ];

    protected $fillable = [
        'place', 'code', 'name', 'enable'
    ];

    protected $hidden = [];
    protected $casts = [];

    public function pallets(): HasMany
    {
        return $this->hasMany(Pallet::class);
    }
}
