<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class RepairProduct extends Model
{
    use SoftDeletes;

    const STATUS_WAITING = 10;
    const STATUS_REPAIRED = 30;
    const STATUS_DELETED = 90;

    public static array $STATUS_NAME = [
        self::STATUS_WAITING => "구성품 신청",
        self::STATUS_REPAIRED => "수리/점검 완료",
        self::STATUS_DELETED => "삭제",
    ];

    protected $fillable = [
        'product_id',
        'status',
        'waiting_user_id',
        'waiting_at',
        'completed_user_id',
        'completed_at',
        'amount',
        'repair_symptom_id',
        'repair_process_id',
        'repair_grade_id',
        'invoice1',
        'invoice2',
        'invoice3',
        'is_os_install',
        'fee_type',
        'fee_unit',
        'is_default_fee',
        'repair_fee_range_id',
        'default_fee_range_id',
        'memo',
    ];

    protected $with = [
        'repairSymptom:id,name,code',
        'repairProcess:id,name,code',
        'repairGrade:id,name,code',
        'waitingUser:id,name,username',
        'completedUser:id,name,username',
        'repairProductParts',
    ];

    /**
     * 캐스팅 설정
     */
    protected $casts = [
        'status' => 'integer',
        'amount' => 'integer',
        'invoice1' => 'integer',
        'invoice2' => 'integer',
        'invoice3' => 'integer',
        'waiting_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * 상품과의 관계
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    /**
     * 수리 증상과의 관계
     */
    public function repairSymptom(): BelongsTo
    {
        return $this->belongsTo(RepairSymptom::class, 'repair_symptom_id', 'id');
    }

    /**
     * 수리 처리와의 관계
     */
    public function repairProcess(): BelongsTo
    {
        return $this->belongsTo(RepairProcess::class, 'repair_process_id', 'id');
    }

    /**
     * 수리 등급과의 관계
     */
    public function repairGrade(): BelongsTo
    {
        return $this->belongsTo(RepairGrade::class, 'repair_grade_id', 'id');
    }

    /**
     * 팔레트 제품들과의 관계
     */
    public function palletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'repair_product_id', 'id');
    }

    /**
     * 대기 사용자와의 관계
     */
    public function waitingUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'waiting_user_id', 'id');
    }

    /**
     * 완료 사용자와의 관계
     */
    public function completedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'completed_user_id', 'id');
    }

    /**
     * 수리 제품 구성품들과의 관계
     */
    public function repairProductParts(): HasMany
    {
        return $this->hasMany(RepairProductParts::class, 'repair_product_id', 'id');
    }

    /**
     * 수리 구성품들과의 관계
     */
    public function repairParts(): BelongsToMany
    {
        return $this->belongsToMany(RepairParts::class, 'repair_product_parts',
            'repair_product_id', 'repair_parts_id')
            ->withPivot('quantity')
            ->withTimestamps();
    }

    /**
     * 수리비 범위와의 관계
     */
    public function repairFeeRange(): BelongsTo
    {
        return $this->belongsTo(RepairFeeRange::class, 'repair_fee_range_id', 'id');
    }

    /**
     * 기본값 사용 여부 확인
     * @todo: 컬럼과 이 메서드가 충돌하므로 주석 처리
     */
    // public function getIsDefaultFeeAttribute(): bool
    // {
    //     return $this->is_default_fee === 'Y';
    // }

    /**
     * 기본값 정보 가져오기
     */
    public function getDefaultFeeInfoAttribute(): ?array
    {
        if (!$this->is_default_fee || !$this->default_fee_range_code) {
            return null;
        }

        $defaultId = RepairFee::DEFAULT_FEE_RANGE_IDS[$this->default_fee_range_code] ?? null;
        if (!$defaultId) {
            return null;
        }

        return RepairFee::getDefaultFeeInfo($defaultId);
    }

    /**
     * 수리비 범위 텍스트 가져오기 (기본값 포함)
     */
    public function getFeeRangeTextAttribute(): string
    {
        // 기본값 사용 시
        if ($this->is_default_fee) {
            $defaultInfo = $this->default_fee_info;
            if ($defaultInfo) {
                $unitName = RepairFee::$FEE_UNIT_NAME[$defaultInfo['feeUnit']] ?? $defaultInfo['feeUnit'];
                $minValue = number_format($defaultInfo['min_value']);
                $maxValue = $defaultInfo['max_value'] >= 100000000 ? '∞' : number_format($defaultInfo['max_value']);

                return "기본값: {$minValue}{$unitName} 초과 ~ {$maxValue}{$unitName} 이하";
            }
            return '기본값 (상세 정보 없음)';
        }

        // DB 값 사용 시 (기존 로직)
        if (!$this->repairFeeRange) {
            return '범위 없음';
        }

        $range = $this->repairFeeRange;
        $unitName = RepairFee::$FEE_UNIT_NAME[$range->fee_unit] ?? $range->fee_unit;

        if ($range->max_value >= 100000000) {
            return $range->min_value . $unitName . ' 초과 ~ ∞' . $unitName . ' 이하';
        }

        return $range->min_value . $unitName . ' 초과 ~ ' . $range->max_value . $unitName . ' 이하';
    }

    /**
     * 수리비 타입 이름 가져오기
     */
    public function getFeeTypeNameAttribute(): string
    {
        return RepairFee::$FEE_TYPE_NAME[$this->fee_type] ?? $this->fee_type;
    }

    /**
     * 최소값 가져오기 (기본값 포함)
     */
    public function getMinValueAttribute(): ?float
    {
        if ($this->is_default_fee) {
            $defaultInfo = $this->default_fee_info;
            return $defaultInfo ? $defaultInfo['min_value'] : null;
        }

        return $this->repairFeeRange?->min_value;
    }

    /**
     * 최대값 가져오기 (기본값 포함)
     */
    public function getMaxValueAttribute(): ?float
    {
        if ($this->is_default_fee) {
            $defaultInfo = $this->default_fee_info;
            return $defaultInfo ? $defaultInfo['max_value'] : null;
        }

        return $this->repairFeeRange?->max_value;
    }

    /**
     * 수리비 금액 가져오기 (기본값 포함)
     */
    public function getFeeAmountAttribute(): ?int
    {
        if ($this->is_default_fee) {
            $defaultInfo = $this->default_fee_info;
            return $defaultInfo ? $defaultInfo['amount'] : null;
        }

        return $this->invoice1;
    }

    /**
     * 수리비 금액 포맷팅 (기본값 포함)
     */
    public function getFormattedFeeAmountAttribute(): string
    {
        $amount = $this->fee_amount;
        if ($amount === null) {
            return '0원';
        }

        $prefix = $this->is_default_fee ? '기본값: ' : '';
        return $prefix . number_format($amount) . '원';
    }

    /**
     * 수리비 상세 정보 가져오기 (기본값 포함)
     */
    public function getFeeDetailAttribute(): array
    {
        if ($this->is_default_fee) {
            $defaultInfo = $this->default_fee_info;
            return [
                'is_default' => true,
                'fee_type' => $this->fee_type,
                'fee_unit' => $this->fee_unit,
                'amount' => $defaultInfo ? $defaultInfo['amount'] : 0,
                'min_value' => $defaultInfo ? $defaultInfo['min_value'] : 0,
                'max_value' => $defaultInfo ? $defaultInfo['max_value'] : 0,
                'range_text' => $this->fee_range_text,
                'formatted_amount' => $this->formatted_fee_amount,
            ];
        }

        return [
            'is_default' => false,
            'fee_type' => $this->fee_type,
            'fee_unit' => $this->fee_unit,
            'amount' => $this->invoice1,
            'min_value' => $this->min_value,
            'max_value' => $this->max_value,
            'range_text' => $this->fee_range_text,
            'formatted_amount' => $this->formatted_fee_amount,
        ];
    }

    /**
     * 수리비 단위 이름 가져오기
     */
    public function getFeeUnitNameAttribute(): string
    {
        return RepairFee::$FEE_UNIT_NAME[$this->fee_unit] ?? $this->fee_unit;
    }

    /**
     * 상태 이름 가져오기
     */
    public function getStatusNameAttribute(): string
    {
        return self::$STATUS_NAME[$this->status] ?? '알 수 없음';
    }

    /**
     * 판매가 포맷팅
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount) . '원';
    }

    /**
     * 총 청구금액 계산
     */
    public function getTotalInvoiceAttribute(): int
    {
        return $this->invoice1 + $this->invoice2 + $this->invoice3;
    }

    /**
     * 총 청구금액 포맷팅
     */
    public function getFormattedTotalInvoiceAttribute(): string
    {
        return number_format($this->total_invoice) . '원';
    }

    /**
     * 대기 상태인지 확인
     */
    public function getIsWaitingAttribute(): bool
    {
        return $this->status === self::STATUS_WAITING;
    }

    /**
     * 완료 상태인지 확인
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === self::STATUS_REPAIRED;
    }

    /**
     * 삭제 상태인지 확인
     */
    public function getIsDeletedAttribute(): bool
    {
        return $this->status === self::STATUS_DELETED;
    }

    /**
     * 구성품 총 비용 계산
     */
    public function getPartsTotalCostAttribute(): int
    {
        return $this->repairProductParts->sum(function ($part) {
            return $part->price * $part->quantity;
        });
    }

    /**
     * 구성품 총 비용 포맷팅
     */
    public function getFormattedPartsTotalCostAttribute(): string
    {
        return number_format($this->parts_total_cost) . '원';
    }
}
