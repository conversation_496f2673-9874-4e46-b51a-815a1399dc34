<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairFee extends Model
{
    use HasFactory;

    const REPAIR_TYPE = [
        'CHECK' => 'CHECK',
        'REPAIR' => 'REPAIR',
        'OS' => 'OS reinstall',
    ];

    public static array $REPAIR_TYPE_NAME = [
        self::REPAIR_TYPE['CHECK'] => "점검",
        self::REPAIR_TYPE['REPAIR'] => "수리",
        self::REPAIR_TYPE['OS'] => "OS 재설치",
    ];

    const FEE_TYPE = [
        'SIZE' => 'size',
        'PRICE' => 'price',
        'NONE' => 'none',
    ];

    public static array $FEE_TYPE_NAME = [
        self::FEE_TYPE['SIZE'] => "크기",
        self::FEE_TYPE['PRICE'] => "판매가",
        self::FEE_TYPE['NONE'] => "공통",
    ];

    const FEE_UNIT = [
        'CM' => 'cm',
        'INCH' => 'inch',
        'WON' => 'won',
    ];

    public static array $FEE_UNIT_NAME = [
        self::FEE_UNIT['CM'] => "cm",
        self::FEE_UNIT['INCH'] => "인치",
        self::FEE_UNIT['WON'] => "원",
    ];

    const DEFAULT_FEE_RANGE_IDS = [
        'GENERAL_CHECK_LOW' => -1001,    // 일반 제품 점검 (5만원 이하)
        'GENERAL_CHECK_HIGH' => -1002,   // 일반 제품 점검 (5만원 초과)
        'GENERAL_REPAIR_LOW' => -1101,   // 일반 제품 수리 (5만원 이하)
        'GENERAL_REPAIR_HIGH' => -1102,  // 일반 제품 수리 (5만원 초과)
        'APPLE_CHECK_1' => -2001,        // 애플 제품 점검 (0~5만원)
        'APPLE_CHECK_2' => -2002,        // 애플 제품 점검 (5~10만원)
        'APPLE_CHECK_3' => -2003,        // 애플 제품 점검 (10~20만원)
        'APPLE_CHECK_4' => -2004,        // 애플 제품 점검 (20만원 초과)
        'MONITOR_CHECK_SMALL' => -3001,  // 모니터 점검 (소형)
        'MONITOR_CHECK_MEDIUM' => -3002, // 모니터 점검 (중형)
        'MONITOR_CHECK_LARGE' => -3003,  // 모니터 점검 (대형)
    ];

// 기본값 정보
    const DEFAULT_FEE_RANGES = [
        // 일반 제품 기본값
        -1001 => ['min_value' => 0, 'max_value' => 50000, 'amount' => 5800, 'fee_type' => 'price', 'fee_unit' => 'won'],
        -1002 => ['min_value' => 50000, 'max_value' => 100000000, 'amount' => 5800, 'fee_type' => 'price', 'fee_unit' => 'won'],
        -1101 => ['min_value' => 0, 'max_value' => 50000, 'amount' => 8200, 'fee_type' => 'price', 'fee_unit' => 'won'],
        -1102 => ['min_value' => 50000, 'max_value' => 100000000, 'amount' => 8200, 'fee_type' => 'price', 'fee_unit' => 'won'],

        // 애플 제품 기본값
        -2001 => ['min_value' => 0, 'max_value' => 50000, 'amount' => 6000, 'fee_type' => 'price', 'fee_unit' => 'won'],
        -2002 => ['min_value' => 50000, 'max_value' => 100000, 'amount' => 10000, 'fee_type' => 'price', 'fee_unit' => 'won'],
        -2003 => ['min_value' => 100000, 'max_value' => 200000, 'amount' => 17000, 'fee_type' => 'price', 'fee_unit' => 'won'],
        -2004 => ['min_value' => 200000, 'max_value' => 1000000000, 'amount' => 25000, 'fee_type' => 'price', 'fee_unit' => 'won'],
    ];

    protected $fillable = [
        'repair_fee_range_id',
        'repair_type',
        'amount',
    ];

    /**
     * 캐스팅 설정
     */
    protected $casts = [
        'amount' => 'integer',
    ];

    /**
     * 범위와의 관계
     */
    public function range(): BelongsTo
    {
        return $this->belongsTo(RepairFeeRange::class, 'repair_fee_range_id', 'id');
    }

    /**
     * 수리 타입 이름 가져오기
     */
    public function getRepairTypeNameAttribute(): string
    {
        return self::$REPAIR_TYPE_NAME[$this->repair_type] ?? $this->repair_type;
    }

    /**
     * 금액 포맷팅
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount) . '원';
    }

    /**
     * 수리 타입이 점검인지 확인
     */
    public function getIsCheckAttribute(): bool
    {
        return $this->repair_type === self::REPAIR_TYPE['CHECK'];
    }

    /**
     * 수리 타입이 수리인지 확인
     */
    public function getIsRepairAttribute(): bool
    {
        return $this->repair_type === self::REPAIR_TYPE['REPAIR'];
    }

    /**
     * 수리 타입이 OS 재설치인지 확인
     */
    public function getIsOsInstallAttribute(): bool
    {
        return $this->repair_type === self::REPAIR_TYPE['OS'];
    }

    /**
     * 기본값 ID인지 확인
     */
    public static function isDefaultFeeRangeId(?int $id): bool
    {
        return $id !== null && $id < 0;
    }

    /**
     * 기본값 정보 가져오기
     */
    public static function getDefaultFeeRangeInfo(int $id): ?array
    {
        return self::DEFAULT_FEE_RANGES[$id] ?? null;
    }

    /**
     * 기본값 ID로 수리비 정보 가져오기
     */
    public static function getDefaultFeeInfo(int $id): array
    {
        $defaultInfo = self::getDefaultFeeRangeInfo($id);

        if (!$defaultInfo) {
            return [
                'feeType' => 'none',
                'feeUnit' => 'won',
                'amount' => 0,
                'min_value' => 0,
                'max_value' => 0,
            ];
        }

        return [
            'feeType' => $defaultInfo['fee_type'],
            'feeUnit' => $defaultInfo['fee_unit'],
            'amount' => $defaultInfo['amount'],
            'min_value' => $defaultInfo['min_value'],
            'max_value' => $defaultInfo['max_value'],
        ];
    }

    /**
     * 특정 제품 타입의 모든 기본값 범위 리스트 생성
     * @param string $productType 'apple', 'general', 'monitor'
     * @return array
     */
    public static function getDefaultFeeRangeList(string $productType): array
    {
        $feeRangeList = [];

        // 제품 타입별 ID 범위 정의
        $idRanges = [
            'general' => [-1001, -1102], // -1001 ~ -1102
            'apple' => [-2001, -2004],   // -2001 ~ -2004
            'monitor' => [-3001, -3003], // -3001 ~ -3003
        ];

        if (!isset($idRanges[$productType])) {
            return $feeRangeList;
        }

        [$startId, $endId] = $idRanges[$productType];

        for ($id = $startId; $id <= $endId; $id++) {
            $feeInfo = self::getDefaultFeeRangeInfo($id);
            if ($feeInfo) {
                $unitName = self::$FEE_UNIT_NAME[$feeInfo['fee_unit']] ?? '원';
                $minValue = number_format($feeInfo['min_value']);
                $maxValue = $feeInfo['max_value'] >= 100000000 ? '∞' : number_format($feeInfo['max_value']);

                $feeRangeList[] = [
                    'id' => $id,
                    'min_value' => $feeInfo['min_value'],
                    'max_value' => $feeInfo['max_value'],
                    'amount' => $feeInfo['amount'],
                    'formatted_amount' => number_format($feeInfo['amount']) . '원',
                    'range' => " {$minValue}{$unitName} 초과 ~ {$maxValue}{$unitName} 이하",
                ];
            }
        }

        return $feeRangeList;
    }
}
