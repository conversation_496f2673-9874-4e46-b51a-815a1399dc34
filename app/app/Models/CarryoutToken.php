<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CarryoutToken extends Model
{
    use HasFactory;

    protected $fillable = [
        'token',
    ];

    public $timestamps = false;

    protected $casts = [];
    protected $hidden = [];

    public function carryouts(): HasMany
    {
        return $this->hasMany(Carryout::class, 'token_id', 'id');
    }

    public function carryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'token_id', 'id');
    }
}
