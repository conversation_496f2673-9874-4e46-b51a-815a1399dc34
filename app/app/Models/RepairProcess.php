<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RepairProcess extends Model
{
    protected $fillable = ['name', 'code'];

    public function carryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'repair_process_id', 'id');
    }

    public function palletProducts(): Has<PERSON>any
    {
        return $this->hasMany(PalletProduct::class, 'repair_process_id', 'id');
    }

    public function repairProducts(): HasMany
    {
        return $this->hasMany(RepairProduct::class, 'repair_process_id', 'id');
    }

    public function repairGrades(): BelongsToMany
    {
        return $this->belongsToMany(RepairGrade::class, 'repair_grade_repair_process');
    }

    public function repairSymptoms(): BelongsToMany
    {
        return $this->belongsToMany(RepairSymptom::class, 'repair_process_repair_symptom');
    }
}
