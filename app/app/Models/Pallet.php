<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class Pallet extends Model
{
    use HasFactory;

    const STATUS_REGISTERED = 10;
    const STATUS_LOADED = 20;
    const STATUS_CLOSED = 30;
    const STATUS_EXPORTED = 40;
    const STATUS_DELETED = 90;

    public static array $STATUS_NAME = [
        self::STATUS_REGISTERED => "등록",
        self::STATUS_LOADED => "적재중",
        self::STATUS_CLOSED => "적재마감(출고대기)",
        self::STATUS_EXPORTED => "출고완료",
        self::STATUS_DELETED => "삭제",
    ];

    protected $fillable = [
        'location_id',
        'repair_grade_id',
        'status',
        'registered_at', 'registered_user_id',
        'checked_at', 'checked_user_id',
        'exported_at', 'exported_user_id',
        'memo',
    ];

    protected $hidden = [];

    protected $casts = [
        'registered_at' => 'datetime',
        'checked_at' => 'datetime',
        'exported_at' => 'datetime',
    ];

    protected $with = [
        'location',
        'repairGrade:id,name,code',
        'registeredUser:id,name',
        'checkedUser:id,name',
        'exportedUser:id,name',
    ];

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function repairGrade(): BelongsTo
    {
        return $this->belongsTo(RepairGrade::class, 'repair_grade_id', 'id');
    }

    public function registeredUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'registered_user_id', 'id')->withTrashed();
    }

    public function checkedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'checked_user_id', 'id')->withTrashed();
    }

    public function exportedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'exported_user_id', 'id')->withTrashed();
    }

    public function palletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class);
    }

    public function deleteLog(): MorphOne
    {
        return $this->morphOne(DeleteLog::class, 'deletable', 'deletable_type', 'deletable_id');
    }
}
