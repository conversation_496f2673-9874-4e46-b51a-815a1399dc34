<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class PalletProduct extends Model
{
    use HasFactory;

    const STATUS_REGISTERED = 10;
    const STATUS_EXPORTED = 30;
    const STATUS_DELETED = 90;

    public static $STATUS_NAME = [
        self::STATUS_REGISTERED => "적재",
        self::STATUS_EXPORTED => "출고",
        self::STATUS_DELETED => "삭제",
    ];

    const CHECK_STATUS_ON_PALLET = 10;
    const CHECK_STATUS_CHECKED = 20;

    public static $CHECK_STATUS_NAME = [
        self::CHECK_STATUS_ON_PALLET => "미검수",
        self::CHECK_STATUS_CHECKED => "검수완료",
    ];

    protected $fillable = [
        'pallet_id', 'product_id', 'repair_product_id', 'status',
        'registered_user_id', 'registered_at',
        'quantity', 'amount',
        'repair_symptom_id', 'repair_process_id', 'repair_grade_id',
        'invoice1', 'invoice2', 'invoice3',
        'checked_user_id', 'checked_status', 'checked_at',
        'deleted_at',
        'memo',
    ];

    protected $casts = [
        'registered_at' => 'datetime',
        'checked_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $hidden = [];

    protected $with = [
        'pallet',
        'repairSymptom',
        'repairProcess',
        'repairGrade',
        'registeredUser:id,name',
        'checkedUser:id,name',
    ];

    public function pallet(): BelongsTo
    {
        return $this->belongsTo(Pallet::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function repairProduct(): BelongsTo
    {
        return $this->belongsTo(RepairProduct::class, 'repair_product_id', 'id');
    }

    public function registeredUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'registered_user_id')->withTrashed();
    }

    public function checkedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'checked_user_id')->withTrashed();
    }

    public function repairSymptom(): BelongsTo
    {
        return $this->belongsTo(RepairSymptom::class, 'repair_symptom_id');
    }

    public function repairProcess(): BelongsTo
    {
        return $this->belongsTo(RepairProcess::class, 'repair_process_id');
    }

    public function repairGrade(): BelongsTo
    {
        return $this->belongsTo(RepairGrade::class, 'repair_grade_id');
    }

    public function deleteLog(): MorphOne
    {
        return $this->morphOne(DeleteLog::class, 'deletable', 'deletable_type', 'deletable_id');
    }
}
