<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class WarehousePalletItem extends Model
{
    use SoftDeletes;

    const STATUS_STORED = 'stored';
    const STATUS_INTERNAL_USE = 'internal_use';
    const STATUS_INTERNAL_PARTS = 'internal_parts';
    const STATUS_EXPORTED = 'exported';
    const STATUS_RETURNED = 'returned';
    const STATUS_DISCARDED = 'discarded';

    public static array $STATUS_NAME = [
        self::STATUS_STORED => "보관중",
        self::STATUS_INTERNAL_USE => "내부사용",
        self::STATUS_INTERNAL_PARTS => "내부부품",
        self::STATUS_EXPORTED => "출고",
        self::STATUS_RETURNED => "반납(쿠팡)",
        self::STATUS_DISCARDED => "폐기",
    ];

    protected $fillable = [
        'warehouse_pallet_id',
        'product_id',
        'quantity',
        'status',
        'changed_by',
        'description',
    ];

    protected function casts(): array
    {
        return [
            'quantity' => 'integer',
        ];
    }

    public function warehousePallet(): BelongsTo
    {
        return $this->belongsTo(WarehousePallet::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function changer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'changed_by');
    }

    public function memos(): HasMany
    {
        return $this->hasMany(WarehousePalletItemMemo::class);
    }
}
