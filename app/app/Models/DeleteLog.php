<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class DeleteLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'deletable_type', 'deletable_id',
        'content', 'ip',
    ];

    protected $hidden = [];

    protected $casts = [];

    /**
     * 다형성 관계를 검색
     *
     * @return MorphTo
     */
    public function deletable(): MorphTo
    {
        return $this->morphTo();
    }
}
