<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairGradeRepairProcess extends Model
{
    protected $table = 'repair_grade_repair_process';
    protected $primaryKey = ['repair_grade_id', 'repair_process_id'];
    public $incrementing = false;

    protected $fillable = [
        'repair_grade_id',
        'repair_process_id',
    ];

    /**
     * 수리 등급과의 관계
     */
    public function repairGrade(): BelongsTo
    {
        return $this->belongsTo(RepairGrade::class, 'repair_grade_id', 'id');
    }

    /**
     * 수리 처리와의 관계
     */
    public function repairProcess(): BelongsTo
    {
        return $this->belongsTo(RepairProcess::class, 'repair_process_id', 'id');
    }
}
