<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class CarryoutProduct extends Model
{
    use HasFactory;

    const STATUS_ONBOARD = 10;
    const STATUS_RENOVATED = 20;
    const STATUS_CANCELED = 90;

    public static array $STATUS_NAME = [
        self::STATUS_ONBOARD => "수리대기",
        self::STATUS_RENOVATED => "수리완료",
        self::STATUS_CANCELED => "취소"
    ];

    protected $fillable = [
        'carryout_id', 'req_id', 'product_id',
        'status',
        'checked_user_id', 'checked_at',
        'repair_symptom_id', 'repair_process_id', 'repair_grade_id',
        'invoice2',
        'token_id',
        'renovator_id', 'renovate_at',
        'carryin_user_id', 'carryin_at',
        'memo',
    ];

    protected $hidden = [];

    protected $casts = [
        'checked_at' => 'datetime',
        'carryin_at' => 'datetime',
        'renovate_at' => 'datetime',
    ];

    protected $with = [
        'carryout',
        'req:id,req_at',
        'checkedUser:id,name',
        'carryinUser:id,name',
        'repairSymptom:id,code,name',
        'repairProcess:id,code,name',
        'repairGrade:id,code,name',
    ];

    public function carryout(): BelongsTo
    {
        return $this->belongsTo(Carryout::class, 'carryout_id', 'id');
    }

    public function req(): BelongsTo
    {
        return $this->belongsTo(Req::class, 'req_id', 'id');
    }

    public function token(): BelongsTo
    {
        return $this->belongsTo(CarryoutToken::class, 'token_id', 'id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function checkedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'checked_user_id', 'id')->withTrashed();
    }

    public function carryinUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'carryin_user_id', 'id')->withTrashed();
    }

    public function repairSymptom(): BelongsTo
    {
        return $this->belongsTo(RepairSymptom::class, 'repair_symptom_id');
    }

    public function repairProcess(): BelongsTo
    {
        return $this->belongsTo(RepairProcess::class, 'repair_process_id');
    }

    public function repairGrade(): BelongsTo
    {
        return $this->belongsTo(RepairGrade::class, 'repair_grade_id');
    }

    public function deleteLog(): MorphOne
    {
        return $this->morphOne(DeleteLog::class, 'deletable', 'deletable_type', 'deletable_id');
    }
}
