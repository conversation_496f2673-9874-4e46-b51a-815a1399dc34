<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserAttendance extends Model
{
    use HasFactory;

    const DAY_TYPE_WORKDAY = '평일';
    const DAY_TYPE_HOLIDAY = '휴일';

    protected $fillable = [
        'user_id',
        'work_date',
        'day_type',
        'clock_in',
        'clock_out',
        'is_late',
        'late_hours',
        'is_early_leave',
        'early_leave_hours',
        'regular_hours',
        'overtime_hours',
        'total_hours',
    ];

    protected $casts = [
        'work_date' => 'date',
        'clock_in' => 'string',
        'clock_out' => 'string',
        'is_late' => 'boolean',
        'late_hours' => 'string',
        'is_early_leave' => 'boolean',
        'early_leave_hours' => 'string',
        'regular_hours' => 'string',
        'overtime_hours' => 'string',
        'total_hours' => 'string',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }
}
