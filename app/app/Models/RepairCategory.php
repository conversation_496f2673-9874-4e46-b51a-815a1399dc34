<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairCategory extends Model
{
    use HasFactory;
    public $timestamps = false;

    protected $fillable = [
        'cate4_id',
        'cate5_id',
    ];

    /**
     * 수리비 범위들과의 관계
     */
    public function ranges(): HasMany
    {
        return $this->hasMany(RepairFeeRange::class, 'repair_category_id', 'id');
    }

    /**
     * 4차 카테고리와의 관계
     */
    public function cate4(): BelongsTo
    {
        return $this->belongsTo(Cate4::class, 'cate4_id', 'id');
    }

    /**
     * 5차 카테고리와의 관계
     */
    public function cate5(): BelongsTo
    {
        return $this->belongsTo(Cate5::class, 'cate5_id', 'id');
    }
}
