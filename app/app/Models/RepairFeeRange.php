<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Repair<PERSON>ee<PERSON>ange extends Model
{
    use HasFactory;
    public $timestamps = false;

    const REPAIR_RANGE_TYPE = [
        'GENERAL' => 'general',
        'MONITOR' => 'monitor',
        'APPLE' => 'apple',
    ];

    public static array $REPAIR_RANGE_TYPE_NAME = [
        self::REPAIR_RANGE_TYPE['GENERAL'] => '일반',
        self::REPAIR_RANGE_TYPE['MONITOR'] => '모니터',
        self::REPAIR_RANGE_TYPE['APPLE'] => '애플',
    ];

    const REPAIR_RANGE_MODEL = [
        'BRAND' => 'brand',
        'MACBOOK' => '맥북',
        'IMAC' => '아이맥',
        'ETC' => 'etc',
    ];

    public static array $REPAIR_RANGE_MODEL_NAME = [
        self::REPAIR_RANGE_MODEL['BRAND'] => '(모니터)삼성, 엘지',
        self::REPAIR_RANGE_MODEL['MACBOOK'] => '(애플)맥북',
        self::REPAIR_RANGE_MODEL['IMAC'] => '(애플)아이맥',
        self::REPAIR_RANGE_MODEL['ETC'] => '그 외 모든 상품',
    ];

    const REPAIR_RANGE_FEE_TYPE = [
        'SIZE' => 'size',
        'PRICE' => 'price',
        'NONE' => 'none',
    ];

    public static array $REPAIR_RANGE_FEE_TYPE_NAME = [
        self::REPAIR_RANGE_FEE_TYPE['SIZE'] => '크기',
        self::REPAIR_RANGE_FEE_TYPE['PRICE'] => '판매가',
        self::REPAIR_RANGE_FEE_TYPE['NONE'] => '공통',
    ];

    const REPAIR_RANGE_FEE_UNIT = [
        'WON' => 'won',
        'CM' => 'cm',
        'INCH' => 'inch',
    ];

    public static array $REPAIR_RANGE_FEE_UNIT_NAME = [
        self::REPAIR_RANGE_FEE_UNIT['WON'] => '원',
        self::REPAIR_RANGE_FEE_UNIT['CM'] => 'cm',
        self::REPAIR_RANGE_FEE_UNIT['INCH'] => '인치',
    ];

    protected $fillable = [
        'repair_category_id',
        'type',
        'model',
        'fee_type',
        'fee_unit',
        'min_value',
        'max_value',
        'memo',
    ];

    /**
     * 캐스팅 설정
     */
    protected $casts = [
        'min_value' => 'decimal:2',
        'max_value' => 'decimal:2',
    ];

    /**
     * 카테고리와의 관계
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(RepairCategory::class, 'repair_category_id', 'id');
    }

    /**
     * 수리비들과의 관계
     */
    public function fees(): HasMany
    {
        return $this->hasMany(RepairFee::class, 'repair_fee_range_id', 'id');
    }

    public function repairProducts(): HasMany
    {
        return $this->hasMany(RepairProduct::class, 'repair_fee_range_id', 'id');
    }

    /**
     * 타입 이름 가져오기
     */
    public function getTypeNameAttribute(): string
    {
        return self::$REPAIR_RANGE_TYPE_NAME[$this->type] ?? $this->type;
    }

    /**
     * 모델 이름 가져오기
     */
    public function getModelNameAttribute(): string
    {
        return self::$REPAIR_RANGE_MODEL_NAME[$this->model] ?? $this->model;
    }

    /**
     * 수수료 타입 이름 가져오기
     */
    public function getFeeTypeNameAttribute(): string
    {
        return self::$REPAIR_RANGE_FEE_TYPE_NAME[$this->fee_type] ?? $this->fee_type;
    }

    /**
     * 수수료 단위 이름 가져오기
     */
    public function getFeeUnitNameAttribute(): string
    {
        return self::$REPAIR_RANGE_FEE_UNIT_NAME[$this->fee_unit] ?? $this->fee_unit;
    }

    /**
     * 범위 값 문자열 가져오기
     */
    public function getValueRangeAttribute(): string
    {
        if ($this->min_value == $this->max_value) {
            return number_format($this->min_value) . $this->fee_unit_name;
        }

        return number_format($this->min_value) . ' ~ ' . number_format($this->max_value) . $this->fee_unit_name;
    }
}
