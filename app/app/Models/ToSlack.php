<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\Notification;

class ToSlack
{
    use Notifiable;

    protected string $channel;

    public function __construct($channel)
    {
        $this->channel = $channel;
    }

    /**
     * Slack 웹훅 URL을 반환
     */
    public function routeNotificationForSlack(Notification $notification): mixed
    {
        $default = config('services.slack.product');

        return config('services.slack.' . strtolower($this->channel), $default);
    }
}
