<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ReturnReasonB extends Model
{
    protected $table = 'return_reason_b';
    public $timestamps = false;

    protected $fillable = [
        'name',
    ];

    protected $casts = [];
    protected $hidden = [];

    /**
     * 상품들과의 관계
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'return_reason_b_id', 'id');
    }

    /**
     * 이름으로 조회
     */
    public function scopeByName($query, string $name)
    {
        return $query->where('name', $name);
    }
}
