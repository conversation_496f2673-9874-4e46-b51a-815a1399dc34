<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class WarehousePalletHistory extends Model
{
    const ACTION_TYPE_CREATED = 'created';
    const ACTION_TYPE_MOVED = 'moved';
    const ACTION_TYPE_UPDATED = 'updated';
    const ACTION_TYPE_DELETED = 'deleted';
    const ACTION_TYPE_RESTORED = 'restored';

    public static array $ACTION_TYPE_NAME = [
        self::ACTION_TYPE_CREATED => "생성",
        self::ACTION_TYPE_MOVED => "이동",
        self::ACTION_TYPE_UPDATED => "수정",
        self::ACTION_TYPE_DELETED => "삭제",
        self::ACTION_TYPE_RESTORED => "복구",
    ];

    protected $fillable = [
        'warehouse_pallet_item_id',
        'position_id',
        'action_type',
        'action_details',
        'performed_by',
    ];

    public function warehousePalletItem(): BelongsTo
    {
        return $this->belongsTo(WarehousePalletItem::class, 'warehouse_pallet_item_id');
    }

    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'performed_by');
    }

}
