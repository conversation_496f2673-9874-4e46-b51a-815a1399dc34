<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductVendor extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $fillable = [
        'name'
    ];

    protected $hidden = [];

    protected $casts = [];

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'product_vendor_id', 'id');
    }
}
