<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairProductParts extends Model
{
    protected $table = 'repair_product_parts';

    protected $fillable = [
        'repair_product_id',
        'repair_parts_id',
        'quantity',
        'price'
    ];

    protected $with = [
        'repairPart:id,name',
    ];

    /**
     * 캐스팅 설정
     */
    protected $casts = [
        'quantity' => 'integer',
        'price' => 'integer',
    ];

    /**
     * 수리 제품과의 관계
     */
    public function repairProduct(): BelongsTo
    {
        return $this->belongsTo(RepairProduct::class, 'repair_product_id');
    }

    /**
     * 수리 구성품과의 관계
     */
    public function repairPart(): BelongsTo
    {
        return $this->belongsTo(RepairParts::class, 'repair_parts_id');
    }

    /**
     * 총 비용 계산
     */
    public function getTotalCostAttribute(): int
    {
        return $this->price * $this->quantity;
    }

    /**
     * 총 비용 포맷팅
     */
    public function getFormattedTotalCostAttribute(): string
    {
        return number_format($this->total_cost) . '원';
    }

    /**
     * 단가 포맷팅
     */
    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->price) . '원';
    }
}
