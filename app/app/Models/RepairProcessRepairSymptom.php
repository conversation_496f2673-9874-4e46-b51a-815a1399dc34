<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairProcessRepairSymptom extends Model
{
    protected $table = 'repair_process_repair_symptom';
    protected $primaryKey = ['repair_symptom_id', 'repair_process_id'];
    public $incrementing = false;

    protected $fillable = [
        'repair_symptom_id',
        'repair_process_id',
    ];

    /**
     * 수리 증상과의 관계
     */
    public function repairSymptom(): BelongsTo
    {
        return $this->belongsTo(RepairSymptom::class, 'repair_symptom_id', 'id');
    }

    /**
     * 수리 처리와의 관계
     */
    public function repairProcess(): BelongsTo
    {
        return $this->belongsTo(RepairProcess::class, 'repair_process_id', 'id');
    }
}
