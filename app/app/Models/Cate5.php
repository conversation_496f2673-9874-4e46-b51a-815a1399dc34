<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cate5 extends Model
{
    use HasFactory;

    protected $table = 'cate5';

    public $timestamps = false;

    protected $fillable = ['cate5_no', 'cate4_id', 'name'];

    protected $hidden = [];
    protected $casts = [];

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'cate5_id', 'id');
    }

    public function cate4(): BelongsTo
    {
        return $this->belongsTo(Cate4::class, 'cate4_id', 'id');
    }

    public function partsLogs(): HasMany
    {
        return $this->hasMany(RepairPartsLog::class, 'cate5_id', 'id');
    }
}
