<?php

namespace App\Http\Requests\Carryout;

use Illuminate\Foundation\Http\FormRequest;

class ImportRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'carryoutId' => 'required|integer|exists:carryouts,id',
        ];
    }

    public function messages(): array
    {
        return [
            'carryoutId.required' => '외주 반출 ID는 필수 입력 항목입니다.',
            'carryoutId.integer' => '외주 반출 ID는 숫자여야 합니다.',
            'carryoutId.exists' => '존재하지 않는 외주 반출 정보입니다.',
        ];
    }
}