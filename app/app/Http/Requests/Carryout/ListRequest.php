<?php

namespace App\Http\Requests\Carryout;

use Illuminate\Foundation\Http\FormRequest;

class ListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'beginAt' => 'nullable|date',
            'endAt' => 'nullable|date|after_or_equal:beginAt',
            'pageSize' => 'nullable|integer|min:1|max:500',
        ];
    }

    public function messages(): array
    {
        return [
            'beginAt.date' => '시작 날짜는 올바른 날짜 형식 이어야 합니다.',
            'endAt.date' => '종료 날짜는 올바른 날짜 형식 이어야 합니다.',
            'endAt.after_or_equal' => '종료 날짜는 시작 날짜보다 늦어야 합니다.',
            'pageSize.integer' => '페이지 크기는 숫자여야 합니다.',
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'pageSize' => $this->pageSize ?? 15,
        ]);
    }
}
