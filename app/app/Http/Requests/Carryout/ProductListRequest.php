<?php

namespace App\Http\Requests\Carryout;

use Illuminate\Foundation\Http\FormRequest;

class ProductListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'carryoutId' => 'nullable|integer|exists:carryouts,id',
            'beginAt' => 'nullable|date',
            'endAt' => 'nullable|date|after_or_equal:beginAt',
            'repairStatus' => 'nullable|string',
            'productCarryinYN' => 'nullable|string|in:Y,N',
            'searchType' => 'nullable|string|in:qaid,product_name',
            'keyword' => 'nullable|string|max:100',
            'pageSize' => 'nullable|integer|min:1|max:500',
        ];
    }

    public function messages(): array
    {
        return [
            'carryoutId.integer' => '외주 반출 ID는 숫자여야 합니다.',
            'carryoutId.exists' => '존재하지 않는 외주 반출 정보입니다.',
            'beginAt.date' => '시작 날짜는 올바른 날짜 형식이어야 합니다.',
            'endAt.date' => '종료 날짜는 올바른 날짜 형식이어야 합니다.',
            'endAt.after_or_equal' => '종료 날짜는 시작 날짜보다 늦어야 합니다.',
            'productCarryinYN.in' => '반입 여부는 Y 또는 N이어야 합니다.',
            'searchType.in' => '검색 타입은 qaid 또는 product_name이어야 합니다.',
            'keyword.max' => '검색 키워드는 100자를 초과할 수 없습니다.',
            'pageSize.integer' => '페이지 크기는 숫자여야 합니다.',
            'pageSize.min' => '페이지 크기는 최소 1이어야 합니다.',
            'pageSize.max' => '페이지 크기는 최대 500이어야 합니다.',
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'pageSize' => $this->pageSize ?? 15,
        ]);
    }
}
