<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WorkStatusTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // 권한 체크는 미들웨어에서 처리
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $templateId = $this->route('template'); // 라우트 파라미터에서 ID 가져오기

        return [
            'category_id' => 'required|exists:work_categories,id',
            'action_id' => [
                'required',
                'exists:work_actions,id',
                function ($attribute, $value, $fail) {
                    // 액션이 해당 카테고리에 속하는지 확인
                    $categoryId = $this->input('category_id');
                    $action = \App\Models\WorkAction::where('id', $value)
                        ->where('category_id', $categoryId)
                        ->first();

                    if (!$action) {
                        $fail('선택한 액션이 해당 카테고리에 속하지 않습니다.');
                    }
                },
            ],
            'template_code' => 'required|string|max:100',
            'name' => 'required|string|max:200',
            'description' => 'nullable|string',
            'conditions' => 'nullable|array',
            'conditions.*' => 'nullable|string', // 조건의 값들은 문자열이어야 함
            'is_active' => 'boolean',
            'created_by' => 'nullable|exists:users,id',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateConditions($validator);
        });
    }

    /**
     * 조건부 생성 규칙 검증
     */
    private function validateConditions($validator): void
    {
        $conditions = $this->input('conditions');

        if (!$conditions || !is_array($conditions)) {
            return;
        }

        foreach ($conditions as $key => $value) {
            // 키가 문자열이고 비어있지 않은지 확인
            if (!is_string($key) || empty(trim($key))) {
                $validator->errors()->add('conditions', '조건 키는 비어있지 않은 문자열이어야 합니다.');
                return;
            }

            // 값이 문자열이거나 null인지 확인
            if ($value !== null && !is_string($value)) {
                $validator->errors()->add('conditions', '조건 값은 문자열이거나 null이어야 합니다.');
                return;
            }
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'category_id.required' => '카테고리는 필수입니다.',
            'category_id.exists' => '존재하지 않는 카테고리입니다.',
            'action_id.required' => '액션은 필수입니다.',
            'action_id.exists' => '존재하지 않는 액션입니다.',
            'template_code.required' => '템플릿 코드는 필수입니다.',
            'template_code.string' => '템플릿 코드는 문자열이어야 합니다.',
            'template_code.max' => '템플릿 코드는 최대 100자까지 가능합니다.',
            'name.required' => '템플릿 이름은 필수입니다.',
            'name.string' => '템플릿 이름은 문자열이어야 합니다.',
            'name.max' => '템플릿 이름은 최대 200자까지 가능합니다.',
            'description.string' => '템플릿 설명은 문자열이어야 합니다.',
            'conditions.array' => '조건은 배열 형태여야 합니다.',
            'conditions.*.string' => '조건 값은 문자열이어야 합니다.',
            'is_active.boolean' => '활성 상태는 true 또는 false여야 합니다.',
            'created_by.exists' => '존재하지 않는 사용자입니다.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'category_id' => '카테고리',
            'action_id' => '액션',
            'template_code' => '템플릿 코드',
            'name' => '템플릿 이름',
            'description' => '템플릿 설명',
            'conditions' => '조건부 생성 규칙',
            'is_active' => '활성 상태',
            'created_by' => '생성자',
        ];
    }
}
