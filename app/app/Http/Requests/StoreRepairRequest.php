<?php

namespace App\Http\Requests;

use App\Rules\ValidForRepairRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreRepairRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'qaid' => 'required|string',
            'product_id' => ['required', 'integer', new ValidForRepairRule()],
            'repair_product_id' => 'nullable|integer',
            'status' => 'required|in:waiting,complete',
            'symptom_code' => 'required',
            'process_code' => 'required',
            'grade_code' => 'nullable',
            'os_reinstall' => 'boolean',
            'add_parts' => 'nullable|array',
            'update_parts' => 'nullable|array',
            'remove_parts' => 'nullable|array',
            'memo' => 'nullable|string',
        ];
    }

    public function messages(): array
    {
        return [
            'qaid.required' => 'QAID를 입력해 주세요.',
            'product_id.required' => '상품을 선택해 주세요.',
            'status.in' => '수리 상태를 확인할 수 없습니다.',
            'symptom_code.required' => '증상을 선택해 주세요.',
            'process_code.required' => '처리 내용을 선택해 주세요.',
        ];
    }

}
