<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MemberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && ($user->role == 'Admin' || $user->role == 'Super-Admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        if ($this->getMethod() == 'POST') {
            // 등록시 규칙
            $rules = [
                'username' => ['bail', 'required', 'unique:users', 'max:50'],
                'name' => ['bail', 'required', 'max:50'],
                'email' => ['bail', 'unique:users', 'email', 'max:100'],
                'cellphone' => ['bail', 'required', 'unique:users'],
            ];
        } else {
            // 수정시 규칙
            $rules = [
                'username' => ['bail', 'required', 'unique:users,username,' . $this->route('id'), 'max:50'],
                'name' => ['bail', 'required', 'max:50'],
                'email' => ['bail', 'unique:users,email,' . $this->route('id'), 'email', 'max:100'],
                'cellphone' => ['bail', 'required', 'unique:users,cellphone,' . $this->route('id')],
            ];
        }

        if ($this->filled('password')) {
            $rules['password'] =  [
                'bail', 'required',
                function ($attribute, $value, $fail) {
                    if (!preg_match('/^.{8,}$/', $value)) {
                        $fail('비밀번호는 최소 8자 이상이어야 합니다.');
                    } elseif (!preg_match('/[a-zA-Z]/', $value)) {
                        $fail('비밀번호에는 문자가 하나 이상 포함되어야 합니다.');
                    } elseif (!preg_match('/(?=.*[a-z])(?=.*[A-Z])/', $value)) {
                        $fail('비밀번호에는 대소문자가 모두 포함되어야 합니다.');
                    } elseif (!preg_match('/\d/', $value)) {
                        $fail('비밀번호에는 숫자가 하나 이상 포함되어야 합니다.');
                    } elseif (!preg_match('/[~`!@#$%^&*(){}\[\];:,<.>\/?\"\'|_+=-]/', $value)) {
                        $fail('비밀번호에는 특수 문자가 하나 이상 포함되어야 합니다.');
                    }
                },
            ];
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'username.require' => "로그인에 사용할 ID를 입력해 주세요.",
            'username.unique' => "이미 등록되어 있는 ID 입니다.",
            'username.max' => "ID는 :max자 이하여야 합니다.",
            'name.require' => "이름을 입력해 주세요.",
            'name.max' => "이름은 :max자 이하여야 합니다.",
            'email.require' => "메일주소를 입력해 주세요.",
            'email.unique' => "이미 등록되어 있는 메일 주소입니다.",
            'email.email' => "메일주소가 email 형식과 다릅니다. 확인 부탁 드립니다.",
            'email.max' => "메일주소는 :max자리 이하여야 합니다.",
            'cellphone.require' => "연락처(휴대폰 번호)를 입력해 주세요.",
            'cellphone.unique' => "이미 등록되어 있는 연락처(휴대폰 번호) 입니다.",
            'password.require' => "로그인에 사용할 비밀번호를 입력해 주세요.",
            'password.min' => "비밀번호는 :min자리 이상이어야 합니다.",
            'password.letters' => "비밀번호에는 문자가 하나 이상 포함되어야 합니다.",
            'password.mixed_case' => "비밀번호에는 대소문자가 모두 포함되어야 합니다.",
            'password.numbers' => "비밀번호에는 숫자가 하나 이상 포함되어야 합니다.",
            'password.symbols' => "비밀번호에는 특수 문자가 하나 이상 포함되어야 합니다.",
            'password.uncompromised' => "이 비밀번호는 유출된 적이 있는 비밀번호로 안전하지 않습니다.(해킹 당할 수 있음)",
        ];
    }
}
