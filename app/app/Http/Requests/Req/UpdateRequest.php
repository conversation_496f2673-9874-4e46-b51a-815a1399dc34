<?php

namespace App\Http\Requests\Req;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'req_at' => 'required|date|date_format:Y-m-d',
            'req_type' => 'required|integer|min:1|max:10',
            'status' => 'nullable|integer|min:1|max:100',
            'memo' => 'nullable|string|max:500',
            'excel' => 'nullable|file|mimes:xlsx,xls|max:2048', // 2MB 제한
            'redirect' => 'nullable|string|max:255',
            'start_row' => 'nullable|integer|min:2|max:3',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'req_at.required' => '입고일은 필수 입력 항목입니다.',
            'req_at.date' => '입고일은 유효한 날짜 형식이어야 합니다.',
            'req_at.date_format' => '입고일은 YYYY-MM-DD 형식이어야 합니다.',
            'req_type.required' => '요청 타입은 필수 입력 항목입니다.',
            'req_type.integer' => '요청 타입은 숫자여야 합니다.',
            'req_type.min' => '요청 타입은 1 이상이어야 합니다.',
            'req_type.max' => '요청 타입은 10 이하여야 합니다.',
            'status.integer' => '상태는 숫자여야 합니다.',
            'status.min' => '상태는 1 이상이어야 합니다.',
            'status.max' => '상태는 100 이하여야 합니다.',
            'memo.max' => '메모는 500자를 초과할 수 없습니다.',
            'excel.file' => '업로드된 파일이 유효하지 않습니다.',
            'excel.mimes' => '엑셀 파일만 업로드 가능합니다. (.xlsx, .xls)',
            'excel.max' => '파일 크기는 2MB를 초과할 수 없습니다.',
            'redirect.max' => '리다이렉트 URL은 255자를 초과할 수 없습니다.',
            'start_row.integer' => '시작 행은 숫자여야 합니다.',
            'start_row.min' => '시작 행은 1 이상이어야 합니다.',
            'start_row.max' => '시작 행은 1000 이하여야 합니다.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    public function prepareForValidation(): void
    {
        // 시작 행이 없으면 기본값 3 설정
        if (!$this->has('start_row')) {
            $this->merge(['start_row' => 3]);
        }
    }
}
