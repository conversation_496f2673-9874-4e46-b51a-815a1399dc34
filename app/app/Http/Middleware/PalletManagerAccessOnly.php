<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PalletManagerAccessOnly
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        $allowedRoles = ['Super-Admin', 'Admin', 'Pallet-Manager'];

        if (!in_array($user->role, $allowedRoles)) {
            abort(403, 'Access denied');
        }

        return $next($request);
    }
}
