<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Auth;

class SimpleLogViewerController extends Controller
{
    // 로그 디렉토리 경로
    protected string $logPath;

    public function __construct()
    {
        $this->logPath = storage_path('logs');
        // 시스템 관리자만 접근 가능하도록 미들웨어 설정
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            // 시스템 관리자 권한 체크 로직
            $user = Auth::user();
            if (Auth::user()->role === 'Super-Admin') {
                abort(403, '접근 권한이 없습니다.');
            }
            return $next($request);
        });
    }

    /**
     * 로그 뷰어 메인 화면
     */
    public function index(Request $request)
    {
        // 사용 가능한 로그 채널 목록 (디렉토리 기반)
        $channels = ['daily']; // 기본 채널

        // logs 디렉토리의 하위 디렉토리를 채널로 간주
        if (File::isDirectory($this->logPath)) {
            foreach (File::directories($this->logPath) as $dir) {
                $channels[] = basename($dir);
            }
        }

        // 요청에서 채널과 날짜 파라미터 가져오기
        $channel = $request->get('channel', 'daily');
        $date = $request->get('date', now()->format('Y-m-d'));

        // 선택된 채널의 로그 파일 경로 결정
        $logFile = $this->getLogFilePath($channel, $date);

        // 사용 가능한 날짜 목록 (로그 파일 기반)
        $dates = $this->getAvailableDates($channel);

        // 로그 파일 내용 읽기
        $content = File::exists($logFile) ? File::get($logFile) : '로그 파일이 없습니다.';

        // 로그 내용 파싱
        $logEntries = $this->parseLogContent($content);

        return view('admin.logs.simple-viewer', [
            'channels' => $channels,
            'currentChannel' => $channel,
            'dates' => $dates,
            'currentDate' => $date,
            'logEntries' => $logEntries,
        ]);
    }

    /**
     * 채널별 사용 가능한 날짜 목록 가져오기
     */
    private function getAvailableDates($channel)
    {
        $dates = [];
        $pattern = $this->getLogFilePattern($channel);

        // 파일 패턴에 맞는 모든 로그 파일 찾기
        foreach (File::glob($pattern) as $file) {
            $filename = basename($file);
            // 파일 이름에서 날짜 추출
            if (preg_match('/laravel-(\d{4}-\d{2}-\d{2})/', $filename, $matches)) {
                $dates[] = $matches[1];
            }
        }

        // 날짜 내림차순 정렬
        rsort($dates);

        return $dates;
    }

    /**
     * 로그 파일 경로 가져오기
     */
    private function getLogFilePath($channel, $date)
    {
        // 채널이 daily인 경우 루트 logs 디렉토리
        if ($channel === 'daily') {
            return storage_path("logs/laravel-{$date}.log");
        }

        // 그 외의 경우 해당 채널 하위 디렉토리
        return storage_path("logs/{$channel}/laravel-{$date}.log");
    }

    /**
     * 로그 파일 패턴 가져오기
     */
    private function getLogFilePattern($channel)
    {
        if ($channel === 'daily') {
            return storage_path('logs/laravel-*.log');
        }

        return storage_path("logs/{$channel}/laravel-*.log");
    }

    /**
     * 로그 내용 파싱
     */
    private function parseLogContent($content)
    {
        $entries = [];

        // 로그 라인 분리
        $lines = explode("\n", $content);

        foreach ($lines as $line) {
            // 비어있는 라인 건너뛰기
            if (empty(trim($line))) {
                continue;
            }

            // 로그 타임스탬프와 로그 레벨 추출
            if (preg_match('/^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] (\w+)\.(\w+): (.+)$/', $line, $matches)) {
                $timestamp = $matches[1];
                $channel = $matches[2];
                $level = $matches[3];
                $message = $matches[4];

                $entries[] = [
                    'timestamp' => $timestamp,
                    'channel' => $channel,
                    'level' => $level,
                    'message' => $message,
                    'raw' => $line,
                ];
            } else {
                // 정규 표현식에 맞지 않는 라인은 이전 항목의 연속으로 처리
                if (!empty($entries)) {
                    $lastIndex = count($entries) - 1;
                    $entries[$lastIndex]['raw'] .= "\n" . $line;
                }
            }
        }

        return $entries;
    }
}
