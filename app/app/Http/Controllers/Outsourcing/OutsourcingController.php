<?php

namespace App\Http\Controllers\Outsourcing;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Models\Carryout;
use App\Models\CarryoutProduct;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class OutsourcingController extends Controller
{
    public function login(Request $request): JsonResponse
    {
        $token = $request->input('token');
        $carryout = Carryout::join('carryout_tokens', 'carryout_tokens.id', '=', 'carryouts.token_id')
            ->where('carryout_tokens.token', $token)
            ->first();

        if (!$carryout) {
            return $this->errorResponse([
                'message' => '접속할 수 없습니다.'
            ], null, 'outsourcing', 401);
        }

        return $this->successResponse([
            'message' => '로그인 성공',
            'carryout_id' => $carryout->id,
        ]);
    }

    public function getList(Request $request, int $id): JsonResponse
    {
        $builder = CarryoutProduct::where('carryout_id', $id);
        $paginatedResult = $builder->paginate()->withQueryString();

        return $this->successResponse([
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult)
        ]);
    }

    public function checkProduct(string $qaid): JsonResponse
    {
        return $this->successResponse([
            'qaid' => $qaid,
        ]);
    }
}
