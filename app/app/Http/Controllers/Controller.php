<?php

namespace App\Http\Controllers;

use App\Services\SimpleLogService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller as BaseController;
use Throwable;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;

    protected function unauthorizedResponse(): JsonResponse
    {
        return response()->json([
            'success' => false,
            'status' => 401,
            'message' => '401 Unauthorized',
        ], 401);
    }

    /**
     * "요청한 리소스를 찾을 수 없음"을 나타내는 JSON 응답을 반환
     * HTTP 상태 코드 404
     *
     * @return JsonResponse The JSON response with the following structure:
     *         - success: Set to false
     *         - status: HTTP status code 404
     *         - message: Error message indicating "404 Not Found"
     */
    protected function notFoundResponse(): JsonResponse
    {
        return response()->json([
            'success' => false,
            'status' => 404,
            'message' => '404 Not Found',
        ], 404);
    }

    /**
     * @param array $data An array of data to include in the response. Defaults to an empty array.
     * @return JsonResponse The success response as a JSON object.
     */
    protected function successResponse(array $data = [], ?string $message = null, int $status = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message ?? 'Success',
            'data' => $data,
        ], $status);
    }

    /**
     * 오류 응답을 생성하는 메서드
     *
     * 데이터 배열과 예외 객체를 받아 로깅한 후 일관된 JSON 응답을 반환합니다.
     * 로그에 기록될 메시지와 클라이언트에 전송될 메시지를 별도로 설정할 수 있습니다.
     *
     * @param array $data 오류 관련 데이터 배열 (message, log_message, errors 등)
     * @param \Throwable|null $error 예외 객체 (있는 경우)
     * @param string|null $channel 로그 채널(모듈명)
     * @param int $status HTTP 상태 코드
     * @return \Illuminate\Http\JsonResponse
     *
     * @example 기본 사용법:
     *   return $this->errorResponse(['message' => '로그인에 실패했습니다.'], null, 'login', 401);
     *
     * @example 예외 객체와 함께 사용:
     *   try {
     *       // 코드 실행
     *   } catch (\Exception $e) {
     *       return $this->errorResponse(
     *           ['message' => '처리 중 오류가 발생했습니다.'],
     *           $e,
     *           'product',
     *           500
     *       );
     *   }
     *
     * @example 클라이언트 메시지와 로그 메시지를 다르게 설정:
     *   return $this->errorResponse([
     *       'message' => '로그인에 실패했습니다.',  // 클라이언트에 보여질 메시지
     *       'log_message' => '사용자 ID: ' . $request->username . ' - 패스워드 불일치'  // 로그에만 기록될 상세 메시지
     *   ], null, 'login', 401);
     *
     * @example 유효성 검사 오류와 함께 사용:
     *   $validator = Validator::make($request->all(), $rules);
     *   if ($validator->fails()) {
     *       return $this->errorResponse([
     *           'message' => '입력값 검증에 실패했습니다.',
     *           'log_message' => '입력값 검증 실패: ' . json_encode($validator->errors()->toArray()),
     *           'errors' => $validator->errors()->toArray()
     *       ], null, 'login', 422);
     *   }
     */
    protected function errorResponse(
        array      $data = [],
        ?Throwable $error = null,
        ?string    $channel = null,
        int        $status = 404
    ): JsonResponse {
        // 기본값 설정
        $clientMessage = $data['message'] ?? '오류가 발생했습니다';
        $logMessage = $data['log_message'] ?? $clientMessage;

        // 로그 채널 결정
        $logChannelList = ['req', 'product', 'pallet', 'carryout', 'setting', 'login', 'logout', 'board'];
        $logChannel = in_array($channel, $logChannelList)
            ? $channel
            : 'daily';

        // 로그 컨텍스트 구성
        $logContext = [
            'client_message' => $clientMessage, # 클라이언트에 보내는 메시지도 기록
            'request_data' => request()->except(['password', 'token', '_token']),
            'status_code' => $status,
        ];

        // 추가 컨텍스트 정보가 있으면 포함
        if (isset($data['context']) && is_array($data['context'])) {
            $logContext = array_merge($logContext, $data['context']);
        }

        // 로그 레벨 결정 (4xx는 warning, 5xx는 error)
        $logLevel = $status >= 500 ? 'error' : 'warning';

        // SimpleLogService를 통해 로깅
        SimpleLogService::log(
            $logChannel,
            $logMessage,
            $logLevel,
            $logContext,
            $error
        );

        // 응답 데이터 구성
        $responseData = [
            'success' => false,
            'message' => $clientMessage,
        ];

        // 개발 환경에서만 추가 디버그 정보 제공
        if (config('app.debug')) {
            // 추가 오류 데이터가 있으면 포함
            if (isset($data['errors'])) {
                $responseData['errors'] = $data['errors'];
            }

            // 예외 정보 추가
            if ($error) {
                $responseData['debug'] = [
                    'exception' => get_class($error),
                    'file' => $error->getFile() . ':' . $error->getLine(),
                    'trace' => config('app.env') === 'local' ? $error->getTraceAsString() : null,
                ];
            }
        }

        return response()->json($responseData, $status);
    }
}
