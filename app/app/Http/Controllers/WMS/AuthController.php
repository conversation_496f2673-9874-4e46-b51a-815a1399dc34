<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\SimpleLogService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    private array $errorMessages = [
        'name.require' => "이름을 입력해 주세요.",
        'name.max' => "이름은 :max자 이하여야 합니다.",
        'email.require' => "메일주소를 입력해 주세요.",
        'email.unique' => "이미 등록되어 있는 메일 주소입니다.",
        'email.email' => "메일주소가 email 형식과 다릅니다. 확인 부탁 드립니다.",
        'email.max' => "메일주소는 :max자리 이하여야 합니다.",
        'cellphone.max' => "휴대폰번호는 :max자리 이하여야 합니다.",
        'current_password.require' => "현재 비밀번호를 입력해 주세요.",
        'password.require' => "로그인에 사용할 비밀번호를 입력해 주세요.",
        'password.confirmed' => "비밀번호와 확인란의 비밀번호가 같지 않습니다.",
        'password.min' => "비밀번호는 :min자리 이상이어야 합니다.",
        'password.letters' => "비밀번호에는 문자가 하나 이상 포함되어야 합니다.",
        'password.mixed_case' => "비밀번호에는 대소문자가 모두 포함되어야 합니다.",
        'password.numbers' => "비밀번호에는 숫자가 하나 이상 포함되어야 합니다.",
        'password.symbols' => "비밀번호에는 특수 문자가 하나 이상 포함되어야 합니다.",
        'password.uncompromised' => "이 비밀번호는 유출된 적이 있는 비밀번호로 안전하지 않습니다.(해킹 당할 수 있음)",
    ];

    public function login(Request $request): JsonResponse
    {
        $credentials = $request->only('username', 'password');
        $clientIP = $request->getClientIp();

        // 먼저 사용자가 입력한 자격증명이 맞는지 확인하고 로그인 시도
        if (Auth::attempt($credentials)) {
            $user = Auth::guard('web')->user();

            // 사용자가 소프트 삭제되지 않았는지 확인
            if ($user->status === User::MEMBER_STATUS_ACTIVE && !$user->trashed()) {
                activity()
                    ->useLog('login')
                    ->causedBy($user)
                    ->withProperties([
                        'IP' => $clientIP
                    ]) # set any required extra properties
                    ->log('로그인');

                SimpleLogService::info('login', "로그인 성공", [
                    'ID' => $user->username,
                    'Name' => $user->name,
                    'IP' => $clientIP
                ]);

                // 사용자가 소프트 삭제되지 않았으므로 로그인이 성공했습니다.
                return response()->json([
                    'authenticated' => true,
                ]);
            } else {
                SimpleLogService::info('login', "이용 제한중인 아이디로 로그인 시도", [
                    'ID' => $request->username,
                    'IP' => $clientIP
                ]);

                // 사용자가 소프트 삭제되었으므로 로그인을 허용하지 않습니다.
                Auth::logout();

                return $this->errorResponse([
                    'message' => '이용 제한중인 아이디입니다.',
                    'log_message' => '이용 제한중인 아이디로 로그인 시도'
                ], null,'login', 401);
            }
        } else {
            return $this->errorResponse([
                'message' => '아이디 또는 비밀번호가 일치하지 않습니다.',
                'log_message' => '로그인 시도 실패'
            ], null, 'login', 401);
        }
    }

    public function logout(Request $request): JsonResponse
    {
        $clientIP = $request->ip();

        $user = Auth::guard('web')->user();

        activity()
            ->useLog('logout')
            ->causedBy($user)
            ->withProperties([
                'IP' => $clientIP
            ]) # set any required extra properties
            ->log('로그아웃');

        SimpleLogService::info('logout', "로그아웃", [
            'ID' => $user->username,
            'Name' => $user->name,
            'IP' => $clientIP
        ]);

        unset($user);

        // 로그아웃
        Auth::guard('web')->logout();

        Session::invalidate();
        Session::regenerateToken();

        return response()->json([
            'authenticated' => Auth::check()
        ]);
    }

    /**
     * 사용자 정보 가져오기
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function me(Request $request): JsonResponse
    {
        // 레벨과 권한을 뽑기 위해 준비
        $userData = Auth::guard('web')->user();

        $now = Carbon::now();
        $clientIP = $request->ip();

        $user = User::find($userData->id);

        $user->login_at = $now->format("Y-m-d H:i:s");
        $user->login_ip = $clientIP;
        $user->login_os = $request->userAgent();

        $user->save();

        SimpleLogService::info('login', "개인정보 전송", [
            'ID' => $user->username,
            'Name' => $user->name,
            'IP' => $clientIP
        ]);

        return $this->successResponse([
            'user' => [
                'id' => $user->id,
                'company_id' => $user->company_id,
                'role' => $user->role,
                'username' => $user->username,
                'name' => $user->name,
                'part' => $user->part,
                'position' => $user->position,
                'email' => $user->email,
                'cellphone' => $user->cellphone,
                'status' => $user->status,
                'menu' => json_decode($user->menu, true),
                'login_at' => $user->login_at,
                'login_ip' => $user->login_ip,
                'login_os' => $user->login_os,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
                'deleted_at' => $user->deleted_at,
            ],
        ]);
    }

    /**
     * 개인 정보 수정 (이름, 이메일, 휴대폰번호만 수정 가능)
     */
    public function changeMyInfo(Request $request): JsonResponse
    {
        $clientIP = $request->ip();
        $userData = Auth::guard('web')->user();

        // 본인 정보만 수정 가능하도록 검증
        if ($userData->id !== $request->input('id')) {
            return $this->unauthorizedResponse();
        }

        // 개인정보 수정용 검증 규칙 (비밀번호 제외)
        $validationRules = [
            'name' => ['bail', 'required', 'max:50'],
            'email' => ['bail', 'required', 'unique:users,email,'.$userData->id, 'email', 'max:100'],
            'cellphone' => ['bail', 'nullable', 'max:20'],
        ];

        $validator = Validator::make($request->all(), $validationRules, $this->errorMessages);

        // 입력시 문제가 발생된 첫 번째 항목에서 멈춤
        if ($validator->stopOnFirstFailure()->fails()) {
            return $this->errorResponse([
                'errors' => $validator->errors()->toArray()
            ]);
        }

        // 검증된 입력값 사용하기
        $validated = $validator->validated();

        $user = User::find($userData->id);

        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->cellphone = $validated['cellphone'] ?? null;

        $user->save();

        SimpleLogService::info('profile', "개인정보 수정", [
            'ID' => $user->username,
            'Name' => $user->name,
            'IP' => $clientIP
        ]);

        return $this->successResponse([
            'message' => '개인정보가 성공적으로 수정되었습니다.'
        ]);
    }

    /**
     * 비밀번호 변경
     */
    public function changeMyPassword(Request $request): JsonResponse
    {
        $clientIP = $request->ip();
        $userData = Auth::guard('web')->user();

        // 본인 비밀번호만 변경 가능하도록 검증
        if ($userData->id !== $request->input('id')) {
            return $this->unauthorizedResponse();
        }

        // 비밀번호 변경용 검증 규칙
        $validationRules = [
            'current_password' => ['bail', 'required', function ($attribute, $value, $fail) use ($userData) {
                if (!Hash::check($value, $userData->password)) {
                    $fail('현재 비밀번호가 일치하지 않습니다.');
                }
            }],
            'password' => [
                'bail', 'required', 'confirmed',
                function ($attribute, $value, $fail) {
                    if (!preg_match('/^.{8,}$/', $value)) {
                        $fail('비밀번호는 최소 8자 이상이어야 합니다.');
                    } elseif (!preg_match('/[a-zA-Z]/', $value)) {
                        $fail('비밀번호에는 문자가 하나 이상 포함되어야 합니다.');
                    } elseif (!preg_match('/(?=.*[a-z])(?=.*[A-Z])/', $value)) {
                        $fail('비밀번호에는 대소문자가 모두 포함되어야 합니다.');
                    } elseif (!preg_match('/\d/', $value)) {
                        $fail('비밀번호에는 숫자가 하나 이상 포함되어야 합니다.');
                    } elseif (!preg_match('/[~`!@#$%^&*(){}\[\];:,<.>\/?\"\'|_+=-]/', $value)) {
                        $fail('비밀번호에는 특수 문자가 하나 이상 포함되어야 합니다.');
                    }
                },
            ],
        ];

        $validator = Validator::make($request->all(), $validationRules, $this->errorMessages);

        // 입력시 문제가 발생된 첫 번째 항목에서 멈춤
        if ($validator->stopOnFirstFailure()->fails()) {
            return $this->errorResponse([
                'errors' => $validator->errors()->toArray()
            ]);
        }

        // 검증된 입력값 사용하기
        $validated = $validator->validated();

        $user = User::find($userData->id);
        $user->password = Hash::make($validated['password']);
        $user->save();

        SimpleLogService::info('password', "비밀번호 변경", [
            'ID' => $user->username,
            'Name' => $user->name,
            'IP' => $clientIP
        ]);

        return $this->successResponse([
            'message' => '비밀번호가 성공적으로 변경되었습니다.'
        ]);
    }

    /**
     * Bearer 토큰 생성
     *
     * @param User $user
     * @return string
     */
    protected function createToken(User $user): string
    {
        $expiresAt = Carbon::now()->addHours(12);

        return $user->createToken('authToken', ['*'], $expiresAt)->plainTextToken;
    }
}
