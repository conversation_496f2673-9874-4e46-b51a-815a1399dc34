<?php

namespace App\Http\Controllers\WMS\Products;

use App\Helpers\DateHelper;
use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Services\CategoryService;
use App\Services\ProductQueryBuilderService;
use App\Services\ProductService;
use App\Services\ReqService;
use App\Services\SimpleLogService;
use App\Traits\Product\SearchTrait as ProductSearchTrait;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Throwable;

class ProductController extends Controller
{
    use ProductSearchTrait;

    protected ReqService $reqService;
    protected ProductService $productService;
    protected ProductQueryBuilderService $queryBuilderService;

    public function __construct(
        ReqService $reqService,
        ProductService $productService,
        ProductQueryBuilderService $queryBuilderService
    )
    {
        $this->reqService = $reqService;
        $this->productService = $productService;
        $this->queryBuilderService = $queryBuilderService;
    }

    /**
     * 작업목록 리스트 - 미등록 및 삭제 상품을 제외한 모든 상품 리스트
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        list(, $beginAt, $endAt) = DateHelper::getBetweenDate(
            $request->input('beginAt'),
            $request->input('endAt')
        );

        $data = [
            'reqId' => $request->input('reqId'),
            'reqType' => $request->input('reqType'),
            'beginAt' => $beginAt,
            'endAt' => $endAt,
            'checkedStatus' => $request->input('checkedStatus'),
            'productStatus' => $request->input('productStatus'),
            'processCd' => $request->input('processCd'),
            'palletStatus' => $request->input('palletStatus'),
            'cate4' => $request->input('cate4'),
            'cate5' => $request->input('cate5'),
            'searchType' => $request->input('searchType'),
            'keyword' => $request->input('keyword'),
            'isRg' => $request->input('isRg'),
            'isAg' => $request->input('isAg'),
            'pageSize' => $request->input('pageSize', 15),
        ];

        $categoryService = new CategoryService();
        $category = $categoryService->getAllCategories();

        $requestAt = $this->reqService->getReqAt((int) $data['reqId']);

        $builder = $this->queryBuilderService->taskPage($data);
        $paginatedResult = $builder->paginate($data['pageSize'])->withQueryString();

        // 로그 기록해 보기
        SimpleLogService::debug('product', '상품 리스트 쿼리', [
            'data' => $data,
            'query' => $builder->toRawSql(),
        ]);

        return $this->successResponse([
            'requestAt' => $requestAt,
            'cate4' => $category,
            'products' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult)
        ]);
    }

    /**
     * 중복등록 상품 리스트
     */
    public function duplicateList(Request $request): JsonResponse
    {
        $data = [
            'reqId' => $request->input('reqId'),
            'reqType' => $request->input('reqType'),
            'productStatus' => $request->input('productStatus'),
            'processCd' => $request->input('processCd'),
            'cate4' => $request->input('cate4'),
            'cate5' => $request->input('cate5'),
            'searchType' => $request->input('searchType'),
            'keyword' => $request->input('keyword'),
            'pageSize' => $request->input('pageSize', 15),
        ];

        $requestAt = $this->reqService->getReqAt((int) $data['reqId']);

        $query = $this->queryBuilderService->duplicatedPage($data);

        $categoryService = new CategoryService();
        $category = $categoryService->getAllCategories();

        $paginatedResult = $query->paginate($data['pageSize'])->withQueryString();

        return $this->successResponse([
            'requestAt' => $requestAt,
            'cate4' => $category,
            'products' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult)
        ]);
    }

    /**
     * 상품의 상세정보(id)
     *
     * @param string $id
     * @return JsonResponse
     */
    public function showProductById(string $id): JsonResponse
    {
        try {
            $product = $this->findProductById((int) $id);

            return $this->successResponse([
                'product' => $product,
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'product', 500);
        }
    }

    /**
     * 상품의 상세정보(QAID)
     *
     * @param string $qaid
     * @return JsonResponse
     */
    public function showProductByQaid(string $qaid): JsonResponse
    {
        $product = $this->productService->getProductByQaid($qaid);

        return $this->successResponse([
            'product' => $product,
        ]);
    }

    /**
     * 상품 검색 리스트<br>
     * (직원) qaid, 바코드, 상품 이름, 로트번호로 검색
     * (외부업체) qaid로만 검색
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function searchProducts(Request $request): JsonResponse
    {
        $data = [
            'searchType' => $request->input('searchType'),
            'keyword1' => $request->input('keyword1'),
            'keyword2' => $request->input('keyword2'),
            'pageSize' => $request->input('pageSize', 15),
        ];

        $query = $this->queryBuilderService->searchPage($data);

        if (!$data['keyword1'] && !$data['keyword2']) {
            return $this->errorResponse([
                'message' => '검색할 검색어를 입력해 주세요.'
            ]);
        }

        $paginatedResult = $query->paginate($data['pageSize'])->withQueryString();

        return $this->successResponse([
            'products' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 선택된 상품 id에 대해 삭제
     * method: post
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Exception|Throwable
     */
    public function destroyProducts(Request $request): JsonResponse
    {
        $user = Auth::user();

        try {
            $idArray = $request->input('ids');
            if (empty($idArray)) {
                return $this->errorResponse([
                    'message' => '삭제할 상품이 선택되지 않았습니다.',
                ]);
            }

            $this->productService->destroyProducts($idArray, $user);

            return $this->successResponse([
                'data' => $idArray,
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '선택된 상품들의 삭제에 실패했습니다.',
                'log_message' => "선택된 상품 삭제 실패: " . $e->getMessage(),
            ], $e, 'product', 500);
        }
    }
}
