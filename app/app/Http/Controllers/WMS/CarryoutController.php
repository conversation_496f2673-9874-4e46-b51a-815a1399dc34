<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Carryout\ExportRequest;
use App\Http\Requests\Carryout\ImportRequest;
use App\Http\Requests\Carryout\ListRequest;
use App\Http\Requests\Carryout\StoreRequest;
use App\Services\CarryoutQueryBuilderService;
use App\Services\CarryoutService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Throwable;

class CarryoutController extends Controller
{
    protected CarryoutQueryBuilderService $queryBuilderService;
    protected CarryoutService $carryoutService;

    public function __construct(
        CarryoutQueryBuilderService $queryBuilderService,
        CarryoutService $carryoutService,
    )
    {
        $this->queryBuilderService = $queryBuilderService;
        $this->carryoutService = $carryoutService;
    }

    public function list(ListRequest $request): JsonResponse
    {
        $data = $request->validated();

        $builder = $this->queryBuilderService->getCarryoutList($data);
        $paginatedResult = $builder->paginate($data['pageSize'])->withQueryString();

        return $this->successResponse([
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 외주 반출 목록(입력/수정): 같은 메서드를 이용
     * method: post
     *
     * @throws Exception|Throwable
     */
    public function store(StoreRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            $user = Auth::user();
            $this->carryoutService->storeCarryout($data, $user);

            return $this->successResponse([
                'message' => '외주반출 정보등록 성공'
            ]);
        } catch (Exception $e) {
            // 외주 반출 정보 등록에 실패한 경우
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'carryout', $e->getCode());
        }
    }

    /**
     * 반출
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function export(ExportRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            $user = Auth::user();
            $this->carryoutService->exportCarryout($data, $user);

            return $this->successResponse([
                'message' => 'RP상품 외주 반출처리 성공'
            ]);
        } catch (Exception $e) {
            // 외주 반출 처리에 실패한 경우
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'carryout', $e->getCode());
        }
    }

    /**
     * 반입
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function import(ImportRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            $user = Auth::user();
            $this->carryoutService->importCarryout($data, $user);

            return $this->successResponse([
                'message' => 'RP상품 반입 처리 성공'
            ]);
        } catch (Exception $e) {
            // RP상품 반입 처리에 실패한 경우
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'carryout', $e->getCode());
        }
    }

    /**
     * 취소
     * method: delete
     *
     * @throws Exception|Throwable
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $this->carryoutService->destroyCarryout($id, $user);

            return $this->successResponse([
                'message' => 'RP상품 외주 반출정보 삭제 성공'
            ]);
        } catch (Exception $e) {
            // RP상품 외주 반출정보 삭제에 실패한 경우
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'carryout', $e->getCode());
        }
    }
}
