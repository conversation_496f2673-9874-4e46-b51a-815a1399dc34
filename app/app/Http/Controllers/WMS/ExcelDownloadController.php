<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Services\Export\Registry;
use App\Services\SimpleLogService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use InvalidArgumentException;

/**
 * 다운로드 받을 Export를 생성 한 후,
 * app/app/Services/Export/Registry 의 initialize() 메서드에 등록하면 다운로드 받을 수 있습니다.
 * 기본은 app/app/Exports/BaseExport.php 에 있으며, 이것을 확장해 사용합니다.
 */
class ExcelDownloadController extends Controller
{
    /**
     * 통합 엑셀 다운로드 엔드포인트
     */
    public function downloadAsExcel(Request $request): void
    {
        // exportType 파라미터 기본값은 'products'
        $type = $request->input('exportType', 'products');

        try {
            $export = Registry::create($type, $request);
            $export->export();
        } catch (InvalidArgumentException $e) {
            abort(400, $e->getMessage());
        } catch (Exception $e) {
            SimpleLogService::error('excel', '엑셀 다운로드 실패', [
                'type' => $type,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ], $e);
            abort(500, '엑셀 다운로드 중 오류가 발생했습니다.');
        }
    }

    /**
     * 사용 가능한 Export 타입 목록 반환
     */
    public function getAvailableExports(): JsonResponse
    {
        $types = Registry::getRegisteredTypes();

        return response()->json([
            'available_exports' => $types,
            'message' => '사용 가능한 Export 타입 목록입니다.'
        ]);
    }
}
