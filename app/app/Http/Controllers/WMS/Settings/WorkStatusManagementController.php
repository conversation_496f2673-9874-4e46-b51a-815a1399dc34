<?php

namespace App\Http\Controllers\WMS\Settings;

use App\Http\Controllers\Controller;
use App\Http\Requests\WorkCategoryRequest;
use App\Http\Requests\WorkActionRequest;
use App\Http\Requests\WorkStatusTemplateRequest;
use App\Services\WorkCategoryService;
use App\Services\WorkActionService;
use App\Services\WorkStatusTemplateService;
use App\Services\WorkStatusService;
use App\Facades\DynamicWorkStatus;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class WorkStatusManagementController extends Controller
{
    public function __construct(
        private readonly WorkCategoryService $workCategoryService,
        private readonly WorkActionService $workActionService,
        private readonly WorkStatusTemplateService $workStatusTemplateService,
        private readonly WorkStatusService $workStatusService
    ) {}

    // ============================================
    // 카테고리 관리 API
    // ============================================

    /**
     * 카테고리 목록 조회
     */
    public function getCategories(): JsonResponse
    {
        try {
            $categories = $this->workCategoryService->getCategoriesWithRelations();

            return $this->successResponse(['categories' => $categories]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '카테고리 조회에 실패했습니다.',
                'log_message' => '카테고리 조회 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 카테고리 생성
     * @throws Exception|Throwable
     */
    public function createCategory(WorkCategoryRequest $request): JsonResponse
    {
        try {
            $category = $this->workCategoryService->createCategory($request->validated());

            return $this->successResponse([
                'category' => $category
            ], '카테고리가 생성되었습니다.', 201);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '카테고리 생성에 실패했습니다.',
                'log_message' => '카테고리 생성 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 카테고리 수정
     * @throws Exception|Throwable
     */
    public function updateCategory(WorkCategoryRequest $request, int $id): JsonResponse
    {
        try {
            $category = $this->workCategoryService->updateCategory($id, $request->validated());

            return $this->successResponse([
                'category' => $category
            ], '카테고리가 수정되었습니다.', 200);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '카테고리 수정에 실패했습니다.',
                'log_message' => '카테고리 수정 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 카테고리 삭제
     * @throws Exception|Throwable
     */
    public function deleteCategory(int $id): JsonResponse
    {
        try {
            $this->workCategoryService->deleteCategory($id);

            return $this->successResponse([], '카테고리가 삭제되었습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '카테고리 삭제에 실패했습니다.',
                'log_message' => '카테고리 삭제 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    // ============================================
    // 액션 관리 API
    // ============================================

    /**
     * 액션 목록 조회
     * @throws Exception|Throwable
     */
    public function getActions(int $categoryId): JsonResponse
    {
        try {
            $actions = $this->workActionService->getActionsByCategory($categoryId);

            return $this->successResponse(['actions' => $actions]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '액션 조회에 실패했습니다.',
                'log_message' => '액션 조회 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 액션 생성
     * @throws Exception|Throwable
     */
    public function createAction(WorkActionRequest $request): JsonResponse
    {
        try {
            $action = $this->workActionService->createAction($request->validated());

            return $this->successResponse([
                'action' => $action
            ], '액션이 생성되었습니다.', 201);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '액션 생성에 실패했습니다.',
                'log_message' => '액션 생성 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 액션 수정
     * @throws Exception|Throwable
     */
    public function updateAction(WorkActionRequest $request, int $id): JsonResponse
    {
        try {
            $action = $this->workActionService->updateAction($id, $request->validated());

            return $this->successResponse([
                'action' => $action
            ], '액션이 수정되었습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '액션 수정에 실패했습니다.',
                'log_message' => '액션 수정 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 액션 삭제
     * @throws Exception|Throwable
     */
    public function deleteAction(int $id): JsonResponse
    {
        try {
            $this->workActionService->deleteAction($id);

            return $this->successResponse([], '액션이 삭제되었습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '액션 삭제에 실패했습니다.',
                'log_message' => '액션 삭제 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    // ============================================
    // 템플릿 관리 API
    // ============================================

    /**
     * 템플릿 목록 조회
     * @throws Exception|Throwable
     */
    public function getTemplates(int $categoryId, int $actionId): JsonResponse
    {
        try {
            $templates = $this->workStatusTemplateService->getTemplatesByCategoryAndAction($categoryId, $actionId);

            return $this->successResponse(['templates' => $templates]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '템플릿 조회에 실패했습니다.',
                'log_message' => '템플릿 조회 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 템플릿 생성
     * @throws Exception|Throwable
     */
    public function createTemplate(WorkStatusTemplateRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $data['created_by'] = auth()->id();

            $template = $this->workStatusTemplateService->createTemplate($data);

            return $this->successResponse([
                'template' => $template
            ], '상태 템플릿이 생성되었습니다.', 201);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '템플릿 생성에 실패했습니다.',
                'log_message' => '템플릿 생성 실패: ' . $e->getMessage(),
                'error_message' => $e->getMessage()
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 템플릿 수정
     * @throws Exception|Throwable
     */
    public function updateTemplate(WorkStatusTemplateRequest $request, int $id): JsonResponse
    {
        try {
            $template = $this->workStatusTemplateService->updateTemplate($id, $request->validated());

            return $this->successResponse([
                'template' => $template
            ], '템플릿이 수정되었습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '템플릿 수정에 실패했습니다.',
                'log_message' => '템플릿 수정 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 템플릿 삭제
     * @throws Exception|Throwable
     */
    public function deleteTemplate(int $id): JsonResponse
    {
        try {
            $this->workStatusTemplateService->deleteTemplate($id);

            return $this->successResponse([], '템플릿이 삭제되었습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '템플릿 삭제에 실패했습니다.',
                'log_message' => '템플릿 삭제 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    // ============================================
    // 생성된 상태 관리 API
    // ============================================

    /**
     * 현재 등록된 상태 조회
     * @param Request $request
     * @return JsonResponse
     * @throws Exception|Throwable
     */
    public function getGeneratedStatuses(Request $request): JsonResponse
    {
        try {
            $filters = [
                'category_id' => $request->filled('category_id') ? $request->category_id : null,
                'action_id' => $request->filled('action_id') ? $request->action_id : null,
                'template_id' => $request->filled('template_id') ? $request->template_id : null,
                'auto_generated' => true
            ];

            $statuses = $this->workStatusService->getFilteredStatuses($filters, 20);

            return $this->successResponse(['statuses' => $statuses]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '생성된 상태 조회에 실패했습니다.',
                'log_message' => '생성된 상태 조회 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 상태 생성 통계 조회
     * @throws Exception|Throwable
     */
    public function getGenerationStats(): JsonResponse
    {
        try {
            $stats = DynamicWorkStatus::getGenerationStats();

            return $this->successResponse($stats);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '상태 생성 통계 조회에 실패했습니다.',
                'log_message' => '상태 생성 통계 조회 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 최근 생성된 상태 조회
     * @throws Exception|Throwable
     */
    public function getRecentStatuses(Request $request): JsonResponse
    {
        try {
            $limit = $request->get('limit', 10);
            $statuses = DynamicWorkStatus::getRecentlyGeneratedStatuses($limit);

            return $this->successResponse(['statuses' => $statuses]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '최근 상태 조회에 실패했습니다.',
                'log_message' => '최근 상태 조회 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 자주 사용되는 상태 조회
     * @throws Exception|Throwable
     */
    public function getFrequentStatuses(Request $request): JsonResponse
    {
        try {
            $limit = $request->get('limit', 10);
            $statuses = DynamicWorkStatus::getFrequentlyUsedStatuses($limit);

            return $this->successResponse(['statuses' => $statuses]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '자주 사용되는 상태 조회에 실패했습니다.',
                'log_message' => '자주 사용되는 상태 조회 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 사용되지 않는 템플릿 조회
     * @throws Exception|Throwable
     */
    public function getUnusedTemplates(): JsonResponse
    {
        try {
            $templates = DynamicWorkStatus::getUnusedTemplates();

            return $this->successResponse(['templates' => $templates]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '사용되지 않는 템플릿 조회에 실패했습니다.',
                'log_message' => '사용되지 않는 템플릿 조회 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 레거시 상수 변환
     * @throws Exception|Throwable
     */
    public function convertLegacyConstants(): JsonResponse
    {
        try {
            $result = DynamicWorkStatus::convertLegacyConstants();

            return $this->successResponse($result, '레거시 상수 변환이 완료되었습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '레거시 상수 변환에 실패했습니다.',
                'log_message' => '레거시 상수 변환 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 캐시 초기화
     * @throws Exception|Throwable
     */
    public function clearCache(): JsonResponse
    {
        try {
            DynamicWorkStatus::clearCache();

            return $this->successResponse([], '캐시가 초기화되었습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '캐시 초기화에 실패했습니다.',
                'log_message' => '캐시 초기화 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * 특정 카테고리의 상태 통계 조회
     * @throws Exception|Throwable
     */
    public function getCategoryStats(string $categoryCode): JsonResponse
    {
        try {
            $stats = DynamicWorkStatus::getGeneratedStatusStats($categoryCode);

            return $this->successResponse($stats);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '카테고리 통계 조회에 실패했습니다.',
                'log_message' => '카테고리 통계 조회 실패: ' . $e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }
}
