<?php

namespace App\Http\Controllers\WMS\Settings;

use App\DTOs\UserFilterDto;
use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\MemberRequest;
use App\Imports\AttendanceImport;
use App\Models\User;
use App\Models\UserAttendance;
use App\Services\SimpleLogService;
use App\Services\UserService;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

class MemberController extends Controller
{
    private int $userId;
    private array|User|Collection|Model $user;

    public function __construct(Request $request)
    {
        $this->userId = (int) $request->input('userId');
    }

    private function isAdmin(): bool
    {
        $admin = Auth::user();

        return $admin['role'] === 'Super-Admin' || $admin['role'] === 'Admin';
    }

    /**
     * (전체)관리자가 관리하는 멤버 리스트
     */
    public function list(Request $request): JsonResponse
    {
        /** 한 페이지당 보여질 게시물 수 */
        $pageSize = intval($request->input('pageSize', 15));

        $role = $request->input('role');
        $status = is_null($request->input('status')) ? null : intval($request->input('status'));
        $keyword = $request->input('keyword');

        $filter = new UserFilterDto($keyword, $role, $status);

        $userService = new UserService();
        $members = $userService->getList($filter);
        $paginatedResult = $members->paginate($pageSize)->withQueryString();

        return $this->successResponse([
            'members' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 직원 등록
     *
     * Method: POST
     *
     * @param MemberRequest $request
     * @return JsonResponse
     */
    public function store(MemberRequest $request): JsonResponse
    {
        // 입력값 검증
        $validated = $request->validated();
        $validated['role'] = $request->input('role');
        $validated['caps_id'] = $request->input('caps_id');
        $validated['status'] = $request->input('status');
        $validated['position'] = $request->input('position');
        $validated['part'] = $request->input('part');
        $validated['menu'] = $request->input('menu');

        try {
            $userService = new UserService();
            $userService->storeUser($validated);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => "직원 등록에 실패했습니다.",
                'log_message' => "직원 등록 실패: " . $e->getMessage(),
            ], $e, 'setting', 500);
        }
    }

    /**
     * 직원 정보 보기
     *
     * Method: GET
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function show(Request $request, string $id): JsonResponse
    {
        $userService = new UserService();
        $member = $userService->getUserById($id);

        return $this->successResponse([
            'member' => $member
        ]);
    }

    /**
     * 직원 정보 수정
     *
     * Method: PUT
     *
     * @param MemberRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(MemberRequest $request, string $id): JsonResponse
    {
        // 입력값 검증
        $validated = $request->validated();
        $validated['role'] = $request->input('role');
        $validated['caps_id'] = $request->input('caps_id');
        $validated['status'] = $request->input('status');
        $validated['position'] = $request->input('position');
        $validated['part'] = $request->input('part');
        $validated['menu'] = $request->input('menu');

        try {
            $userService = new UserService();
            $userService->updateUser($id, $validated);

            return $this->successResponse([
                'response' => $validated
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => "직원 정보 수정에 실패했습니다.",
                'log_message' => "직원 정보 수정 실패: " . $e->getMessage(),
                'context' => [
                    'validated' => $validated
                ]
            ], $e, 'setting', 500);
        }
    }

    /**
     * 직원 소프트 삭제
     *
     * Method: DELETE
     *
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $userService = new UserService();
            $userService->deleteUser($id);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => "직원 삭제에 실패했습니다.",
                'log_message' => "직원 삭제 실패: " . $e->getMessage(),
            ], $e, 'setting', 500);
        }
    }

    /**
     * 직원 소프트 삭제 - 복구
     *
     * Method: PUT
     *
     * @param string $id
     * @return JsonResponse
     */
    public function restore(string $id): JsonResponse
    {
        $userService = new UserService();
        $userService->restoreUser($id);

        return $this->successResponse();
    }

    /**
     * 근태 관리: 엑셀 업로드 후 근태 데이터 입력
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function attendanceStore(Request $request): JsonResponse
    {
        try {
            // 업로드 한 파일이 있다면 DB에 입력
            if ($request->hasFile('excel')) {
                $file = $request->file('excel');
                $extension = $file->guessExtension(); # 확장자

                $allowedExtensions = ['xlsx', 'xls'];
                if (!in_array($extension, $allowedExtensions)) {
                    throw new Exception('업로드된 파일은 엑셀 파일이 아닙니다.(엑셀 파일 오류)', Response::HTTP_BAD_REQUEST);
                }

                $maxFileSize = 2 * 1024 * 1024; // 파일 크기 체크 (2MB = 2 * 1024 * 1024 = 2097152 바이트)
                if ($file->getSize() > $maxFileSize) {
                    throw new Exception('파일 크기는 2MB를 초과할 수 없습니다.', Response::HTTP_BAD_REQUEST);
                }

                if (!$file->isValid()) {
                    throw new Exception('파일 업로드 실패(서버 오류)', Response::HTTP_INTERNAL_SERVER_ERROR);
                }

                $import = new AttendanceImport();

                try {
                    Excel::import($import, $file);
                } catch (Exception $importException) {
                    SimpleLogService::error('setting', '근태관리 엑셀파일 가져오기 실패', [], $importException);

                    throw $importException;
                }

                // 성공 메시지와 함께 응답
                return $this->successResponse([
                    'message' => '근태 데이터가 성공적으로 저장되었습니다.'
                ]);
            } else {
                throw new Exception('업로드할 엑셀 파일이 없습니다.', Response::HTTP_BAD_REQUEST);
            }
        } catch (Exception $e) {
            SimpleLogService::error('setting', '근태 관리 저장 실패', [], $e);

            // SQL 오류 코드를 HTTP 상태 코드로 사용하지 않도록 수정
            // 23000은 SQL 오류 코드로 중복 키 또는 외래 키 제약 조건 위반을 나타냄
            $errorCode = $e->getCode();
            if (is_numeric($errorCode) && $errorCode >= 1000) {
                // SQL 오류 코드인 경우 기본 HTTP 오류 코드 사용
                $httpCode = Response::HTTP_INTERNAL_SERVER_ERROR;

                // 오류 메시지 상세화
                $errorMessage = $e->getMessage();
                if ($errorCode == 23000) {
                    $errorMessage = '데이터베이스 제약 조건 위반: ' . $errorMessage;
                }
            } else {
                // 일반 예외인 경우 전달된 코드 사용
                $httpCode = $errorCode ?: Response::HTTP_INTERNAL_SERVER_ERROR;
                $errorMessage = $e->getMessage();
            }

            return $this->errorResponse([
                'message' => $errorMessage,
            ], $e, 'setting' , $httpCode);
        }
    }

    /**
     * 근태 관리: 근태 데이터 목록 조회
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function attendanceList(Request $request): JsonResponse
    {
        try {
            $pageSize = intval($request->input('pageSize', 15));
            $userId = $request->input('user_id');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $query = UserAttendance::with('user');

            // 필터링 적용
            if ($userId) {
                $query->where('user_id', $userId);
            }

            if ($startDate) {
                $query->where('work_date', '>=', $startDate);
            }

            if ($endDate) {
                $query->where('work_date', '<=', $endDate);
            }

            // 정렬: 근무일자 역순
            $query->orderBy('work_date', 'desc');

            $attendances = $query->paginate($pageSize)->withQueryString();

            return $this->successResponse([
                'attendances' => $attendances,
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'setting', 500);
        }
    }
}
