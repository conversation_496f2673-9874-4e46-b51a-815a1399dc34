<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Models\Faq;
use App\Models\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Throwable;

class FaqController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $keyword = $request->input('keyword');
        $pageSize = (int) $request->input('pageSize', 15);

        // 일반 공지사항
        $query = Faq::query();

        if ($keyword) {
            $query->where('subject', 'like', '%' . $keyword . '%')
                ->orWhere('content', 'like', '%' . $keyword . '%');
        }
        $query->orderBy('id', 'desc');
        $paginatedResult = $query->paginate($pageSize)->withQueryString();

        return $this->successResponse([
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * (관리자만)FAQ 저장 (미들웨어로 처리중)
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function store(Request $request): JsonResponse
    {
        $data = [
            'subject' => $request->input('subject'),
            'content' => $request->input('content'),
            'cate1' => $request->input('cate1'),
            'cate2' => $request->input('cate2'),
            'solution_code' => $request->input('solution_code'),
        ];

        try {
            DB::beginTransaction();

            Faq::create($data);

            DB::commit();

            return $this->successResponse([
                'message' => '정상 처리 되었습니다',
            ]);
        } catch (Throwable $e) {
            DB::rollback();

            return $this->errorResponse([
                'message' => 'FAQ 등록에 실패하였습니다.',
                'log_message' => 'FAQ 등록 오류: ',
            ], $e,'board', 500);
        }
    }

    public function show(int $id): JsonResponse
    {
        try {
            $article = Faq::findOrFail($id);

            return $this->successResponse([
                'article' => $article
            ]);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => 'FAQ를 찾을 수 없습니다.',
                'log_message' => "[$id]번 FAQ를 찾을 수 없습니다."
            ], $e, 'board', 404);
        }
    }

    /**
     * (관리자만)공지사항 수정<br>
     * 미들웨어로 처리중
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws Throwable
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $data = [
            'subject' => $request->input('subject'),
            'content' => $request->input('content'),
            'cate1' => $request->input('cate1'),
            'cate2' => $request->input('cate2'),
            'solution_code' => $request->input('solution_code'),
        ];

        try {
            DB::beginTransaction();

            Faq::where('id', $id)->update($data);

            DB::commit();

            return $this->successResponse([
                'message' => '정상 처리 되었습니다',
            ]);
        } catch (Throwable $e) {
            DB::rollback();

            return $this->errorResponse([
                'message' => 'FQA 수정에 실패하였습니다.',
                'log_message' => 'FAQ 수정 오류: ',
            ], $e, 'board', 500);
        }
    }

    /**
     * (관리자만)공지사항 삭제<br>
     * 미들웨어로 처리중
     * @param int $id
     * @return JsonResponse
     * @throws Throwable
     */
    public function delete(int $id): JsonResponse
    {
        $user = Auth::user();

        // 관리자 권한 검사 (Super-Admin 또는 Admin만 삭제 가능)
        if ($user->role !== User::ROLE_SUPER_ADMIN
            && $user->role !== User::ROLE_ADMIN) {
            return $this->errorResponse([
                'message' => 'FAQ를 삭제할 권한이 없습니다.',
                'log_message' => "권한 없는 사용자({$user->id}:{$user->name})가 FAQ({$id}) 삭제를 시도했습니다."
            ], null, 'board', 403);
        }

        try {
            DB::beginTransaction();

            Faq::destroy($id);

            DB::commit();

            return $this->successResponse([
                'message' => 'FAQ가 성공적으로 삭제되었습니다.',
            ]);
        } catch (Throwable $e) {
            DB::rollback();

            return $this->errorResponse([
                'message' => 'FAQ 삭제에 실패하였습니다.',
                'log_message' => "FAQ({$id}) 삭제 중 오류가 발생했습니다: ",
            ], $e, 'board', 500);
        }
    }
}
