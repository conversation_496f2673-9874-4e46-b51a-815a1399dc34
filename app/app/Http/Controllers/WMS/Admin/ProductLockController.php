<?php

namespace App\Http\Controllers\WMS\Admin;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\ProductLockRequest;
use App\Services\ProductLockService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductLockController extends Controller
{
    protected ProductLockService $productLockService;

    public function __construct(ProductLockService $productLockService)
    {
        $this->productLockService = $productLockService;
    }

    /**
     * 상품 잠금 설정
     */
    public function lock(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'product_ids' => 'required|array',
                'product_ids.*' => 'integer|exists:products,id',
                'memo' => 'nullable|string|max:500',
            ]);

            $result = $this->productLockService->lockProducts(
                $data['product_ids'],
                $data['memo'],
                $request->user()
            );

            return $this->successResponse([
                'message' => '상품이 성공적으로 잠금 처리되었습니다.',
                'locked_count' => $result['locked_count'],
                'failed_count' => $result['failed_count'],
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '상품 잠금 처리에 실패했습니다.',
                'log_message' => '상품 잠금 처리 실패(ProductLockController::lock)',
            ], $e, 'product', 500);
        }

    }

    /**
     * 상품 잠금 해제
     */
    public function unlock(Request $request): JsonResponse
    {
        $data = $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'integer|exists:products,id',
        ]);

        $result = $this->productLockService->unlockProducts(
            $data['product_ids'],
            $request->user()
        );

        return $this->successResponse([
            'message' => '상품 잠금이 해제되었습니다.',
            'unlocked_count' => $result['unlocked_count'],
        ]);
    }

    /**
     * 잠금된 상품 목록 조회
     */
    public function lockedProducts(Request $request): JsonResponse
    {
        $pageSize = $request->input('pageSize', 15);

        $products = $this->productLockService->getLockedProducts($pageSize);

        return $this->successResponse([
            'products' => $products->items(),
            'pagination' => PaginationHelper::optimize($products),
        ]);
    }
}
