<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\DateHelper;
use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Services\PalletQueryBuilderService;
use App\Services\PalletService;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class PalletController extends Controller
{
    protected PalletQueryBuilderService $queryBuilderService;
    protected palletService $palletService;

    public function __construct(PalletQueryBuilderService $queryBuilderService, palletService $palletService)
    {
        $this->queryBuilderService = $queryBuilderService;
        $this->palletService = $palletService;
    }

    /**
     * 팔레트 리스트
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $data = [
            'status' => $request->input('status'),
            'beginAt' => $request->input('beginAt'),
            'endAt' => $request->input('endAt'),
            'keyword' => $request->input('keyword'),
            'pageSize' => $request->input('pageSize', 15),
        ];

        if (!empty($data['beginAt']) && !empty($data['endAt'])) {
            list(, $data['beginAt'], $data['endAt']) = DateHelper::getBetweenDate(
                $request->input('beginAt'),
                $request->input('endAt')
            );
        }

        $query = $this->queryBuilderService->getPalletList($data);
        $paginatedResult = $query->paginate($data['pageSize'] ?? 15)->withQueryString();

        return $this->successResponse([
            'pallets' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 출고
     * method: post
     *
     * @throws Exception|Throwable
     */
    public function exportPallets(Request $request): JsonResponse
    {
        try {
            $palletIds = $request->input('palletIds');
            if (!is_array($palletIds) || count($palletIds) < 1) {
                throw new Exception('출고할 팔레트가 없습니다.');
            }

            $user = Auth::user();

            $this->palletService->updateExportPallets($palletIds, $user);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다.',
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => "쿠팡PL팔레트 출고에 실패하였습니다.",
            ], $e, 'pallet', 500);
        }
    }

    /**
     * 출고 취소
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function rollbackExportPallets(Request $request): JsonResponse
    {
        try {
            $palletIds = $request->input('palletIds');
            if (!is_array($palletIds) || count($palletIds) < 1) {
                throw new Exception('취소할 팔레트가 없습니다.');
            }

            $user = Auth::user();

            $this->palletService->updateRollbackExportPallets($palletIds, $user);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다.',
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => "쿠팡PL팔레트 출고에 실패하였습니다.",
            ], $e, 'pallet', 500);
        }
    }

    /**
     * 출고 날짜 저장
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function saveExportDate(Request $request): JsonResponse
    {
        try {
            $palletIds = $request->input('palletIds');
            if (!is_array($palletIds) || count($palletIds) < 1) {
                throw new Exception('출고날짜를 저장할 팔레트가 없습니다.');
            }

            $user = Auth::user();

            $this->palletService->updateSaveExportDate($palletIds, $user);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다.',
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => "쿠팡PL팔레트 출고날짜 저장에 실패하였습니다.",
            ], $e, 'pallet', 500);
        }
    }
}
