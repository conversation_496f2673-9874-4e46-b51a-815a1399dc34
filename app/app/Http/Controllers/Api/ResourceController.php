<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WorkStatus;
use App\Services\ProcessService;
use Illuminate\Http\JsonResponse;

class ResourceController extends Controller
{
    public function getProcessTypes(): JsonResponse
    {
        $service = new ProcessService();
        $items = $service->fetchTypes();

        return $this->successResponse([
            'items' => $items,
        ]);
    }

    public function getStatuses(): JsonResponse
    {
        $statuses = WorkStatus::select(['id', 'code', 'name', 'description'])
            ->orderBy('code')
            ->get();

        return $this->successResponse([
            'items' => $statuses
        ]);

    }
}
