<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ClearOpcache extends Command
{
    protected $signature = 'opcache:clear';
    protected $description = 'Clear OPCache';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (function_exists('opcache_reset')) {
            opcache_reset();
            $this->info('OPCache is cleared.');
        } else {
            $this->info('OPCache is not enabled.');
        }
    }
}
