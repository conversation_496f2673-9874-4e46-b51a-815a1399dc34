<?php

namespace App\Console\Commands;

use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\RepairCategory;
use App\Models\RepairFeeRange;
use App\Models\RepairFee;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RepairCategoryRebuildCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:rebuild-categories
                            {--dry-run : 실제 변경사항을 적용하지 않고 미리보기만 실행}
                            {--force : 확인 없이 바로 실행}
                            {--strategy=name : 매칭 전략 (name: 이름으로 매칭, id: ID로 매칭, auto: 자동 선택)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'cate4, cate5에 맞게 repair_categories를 새로 작성하고 repair_fee_ranges의 repair_category_id를 함께 업데이트합니다';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $isForce = $this->option('force');
        $strategy = $this->option('strategy');

        $this->info('🔧 수리 카테고리 재구성을 시작합니다...');

        if ($isDryRun) {
            $this->warn('⚠️  DRY RUN 모드: 실제 변경사항이 적용되지 않습니다.');
        }

        $this->info("📋 사용 전략: {$strategy}");

        // 1. 현재 상황 분석
        $this->analyzeCurrentSituation();

        // 2. 매핑 계획 생성
        $mappingPlan = $this->createMappingPlan($strategy);

        if ($mappingPlan->isEmpty()) {
            $this->error('❌ 매핑할 수 있는 카테고리가 없습니다.');
            return 1;
        }

        // 3. 매핑 계획 표시
        $this->displayMappingPlan($mappingPlan);

        if (!$isForce && !$isDryRun) {
            if (!$this->confirm('위 계획대로 재구성을 진행하시겠습니까?')) {
                $this->info('❌ 재구성이 취소되었습니다.');
                return 0;
            }
        }

        // 4. 재구성 실행
        $this->executeRebuild($mappingPlan, $isDryRun);

        $this->info('✅ 수리 카테고리 재구성이 완료되었습니다!');
        return 0;
    }

    /**
     * 현재 상황 분석
     */
    private function analyzeCurrentSituation(): void
    {
        $this->info('📊 현재 상황 분석 중...');

        $cate4Count = Cate4::count();
        $cate5Count = Cate5::count();
        $repairCategoryCount = RepairCategory::count();
        $repairFeeRangeCount = RepairFeeRange::count();

        $this->table(
            ['항목', '개수'],
            [
                ['cate4', $cate4Count],
                ['cate5', $cate5Count],
                ['repair_categories', $repairCategoryCount],
                ['repair_fee_ranges', $repairFeeRangeCount],
            ]
        );

        // 깨진 관계 확인
        $brokenRelations = DB::table('repair_categories as rc')
            ->leftJoin('cate4', 'rc.cate4_id', '=', 'cate4.id')
            ->leftJoin('cate5', 'rc.cate5_id', '=', 'cate5.id')
            ->where(function ($query) {
                $query->whereNull('cate4.id')
                      ->orWhereNull('cate5.id');
            })
            ->count();

        if ($brokenRelations > 0) {
            $this->warn("⚠️  깨진 관계: {$brokenRelations}개");
        } else {
            $this->info('✅ 모든 관계가 정상입니다.');
        }
    }

    /**
     * 매핑 계획 생성
     */
    private function createMappingPlan($strategy): Collection
    {
        $this->info('🗺️  매핑 계획 생성 중...');

        $mappingPlan = collect();

        // 현재 repair_categories 조회
        $currentCategories = DB::table('repair_categories as rc')
            ->leftJoin('cate4', 'rc.cate4_id', '=', 'cate4.id')
            ->leftJoin('cate5', 'rc.cate5_id', '=', 'cate5.id')
            ->select([
                'rc.id as repair_category_id',
                'rc.cate4_id as old_cate4_id',
                'rc.cate5_id as old_cate5_id',
                'cate4.name as old_cate4_name',
                'cate5.name as old_cate5_name'
            ])
            ->get();

        // cate4_id별로 그룹화
        $groupedByCate4 = $currentCategories->groupBy('old_cate4_id');

        foreach ($groupedByCate4 as $oldCate4Id => $categories) {
            $oldCate4Name = $categories->first()->old_cate4_name;

            // 1단계: 현재 cate4 테이블에서 매칭되는 cate4 찾기
            $newCate4Id = null;
            $matchMethod = '';

            if ($oldCate4Id) {
                // ID로 먼저 시도
                $currentCate4 = Cate4::find($oldCate4Id);
                if ($currentCate4) {
                    $newCate4Id = $currentCate4->id;
                    $matchMethod = 'cate4_id로 매칭됨';
                }
            }

            // ID 매칭이 실패한 경우 이름으로 시도
            if (!$newCate4Id && $oldCate4Name) {
                $newCate4Id = $this->findMatchingCate4($oldCate4Name, $strategy);
                if ($newCate4Id) {
                    $matchMethod = 'cate4 이름으로 매칭됨';
                }
            }

            if ($newCate4Id) {
                // 2단계: 매칭된 cate4의 모든 cate5 조회
                $newCate5s = Cate5::where('cate4_id', $newCate4Id)->get();

                if ($newCate5s->isNotEmpty()) {
                    // 3단계: 기존 cate5들과 새로운 cate5들을 1:1로 매칭
                    $usedCate5Ids = collect(); // 이미 사용된 cate5_id 추적

                    foreach ($categories as $category) {
                        $newCate5Id = null;
                        $cate5MatchMethod = '';

                        if ($category->old_cate5_name) {
                            // 기존 cate5 이름으로 매칭 시도
                            $newCate5Id = $this->findMatchingCate5($category->old_cate5_name, $newCate4Id, $strategy);
                            if ($newCate5Id && !$usedCate5Ids->contains($newCate5Id)) {
                                $cate5MatchMethod = 'cate5 이름으로 매칭됨';
                                $usedCate5Ids->push($newCate5Id);
                            } else {
                                $newCate5Id = null; // 이미 사용된 경우 null로 리셋
                            }
                        }

                        // cate5 매칭이 실패한 경우, 사용되지 않은 첫 번째 cate5 사용
                        if (!$newCate5Id) {
                            $availableCate5 = $newCate5s->first(function ($cate5) use ($usedCate5Ids) {
                                return !$usedCate5Ids->contains($cate5->id);
                            });

                            if ($availableCate5) {
                                $newCate5Id = $availableCate5->id;
                                $cate5MatchMethod = '사용되지 않은 cate5 할당';
                                $usedCate5Ids->push($newCate5Id);
                            } else {
                                // 모든 cate5가 사용된 경우, 첫 번째 cate5 재사용
                                $newCate5Id = $newCate5s->first()->id;
                                $cate5MatchMethod = '모든 cate5 사용됨, 첫 번째 재사용';
                            }
                        }

                        $mappingPlan->push([
                            'repair_category_id' => $category->repair_category_id,
                            'old_cate4_id' => $category->old_cate4_id,
                            'old_cate5_id' => $category->old_cate5_id,
                            'old_cate4_name' => $category->old_cate4_name,
                            'old_cate5_name' => $category->old_cate5_name,
                            'new_cate4_id' => $newCate4Id,
                            'new_cate5_id' => $newCate5Id,
                            'match_method' => $matchMethod . ', ' . $cate5MatchMethod
                        ]);
                    }
                } else {
                    // cate4는 매칭되었지만 하위 cate5가 없는 경우
                    foreach ($categories as $category) {
                        $mappingPlan->push([
                            'repair_category_id' => $category->repair_category_id,
                            'old_cate4_id' => $category->old_cate4_id,
                            'old_cate5_id' => $category->old_cate5_id,
                            'old_cate4_name' => $category->old_cate4_name,
                            'old_cate5_name' => $category->old_cate5_name,
                            'new_cate4_id' => $newCate4Id,
                            'new_cate5_id' => null,
                            'match_method' => $matchMethod . ', cate5 없음'
                        ]);
                    }
                }
            } else {
                // cate4 매칭이 실패한 경우
                foreach ($categories as $category) {
                    $mappingPlan->push([
                        'repair_category_id' => $category->repair_category_id,
                        'old_cate4_id' => $category->old_cate4_id,
                        'old_cate5_id' => $category->old_cate5_id,
                        'old_cate4_name' => $category->old_cate4_name,
                        'old_cate5_name' => $category->old_cate5_name,
                        'new_cate4_id' => null,
                        'new_cate5_id' => null,
                        'match_method' => '수동 확인 필요'
                    ]);
                }
            }
        }

        return $mappingPlan;
    }

    /**
     * cate4 매칭 찾기 (개선된 버전)
     */
    private function findMatchingCate4($oldName, $strategy)
    {
        if ($strategy === 'name') {
            // 이름으로 정확히 매칭
            $cate4 = Cate4::where('name', $oldName)->first();
            return $cate4 ? $cate4->id : null;
        } elseif ($strategy === 'id') {
            // ID로 매칭 (이름이 변경된 경우)
            $cate4 = Cate4::where('name', $oldName)->first();
            return $cate4 ? $cate4->id : null;
        } else {
            // auto: 이름으로 먼저 시도, 실패하면 유사한 이름 찾기
            $cate4 = Cate4::where('name', $oldName)->first();
            if ($cate4) {
                return $cate4->id;
            }

            // 유사한 이름 찾기 (부분 매칭)
            $cate4 = Cate4::where('name', 'like', "%{$oldName}%")
                ->orWhere('name', 'like', "%" . substr($oldName, 0, 3) . "%")
                ->first();
            return $cate4 ? $cate4->id : null;
        }
    }

    /**
     * cate5 매칭 찾기 (개선된 버전)
     */
    private function findMatchingCate5($oldName, $cate4Id, $strategy)
    {
        if ($strategy === 'name') {
            // 이름으로 정확히 매칭
            $cate5 = Cate5::where('name', $oldName)
                ->where('cate4_id', $cate4Id)
                ->first();
            return $cate5 ? $cate5->id : null;
        } elseif ($strategy === 'id') {
            // ID로 매칭
            $cate5 = Cate5::where('name', $oldName)
                ->where('cate4_id', $cate4Id)
                ->first();
            return $cate5 ? $cate5->id : null;
        } else {
            // auto: 이름으로 먼저 시도, 실패하면 유사한 이름 찾기
            $cate5 = Cate5::where('name', $oldName)
                ->where('cate4_id', $cate4Id)
                ->first();
            if ($cate5) {
                return $cate5->id;
            }

            // 유사한 이름 찾기
            $cate5 = Cate5::where('cate4_id', $cate4Id)
                ->where(function ($query) use ($oldName) {
                    $query->where('name', 'like', "%{$oldName}%")
                          ->orWhere('name', 'like', "%" . substr($oldName, 0, 3) . "%");
                })
                ->first();
            return $cate5 ? $cate5->id : null;
        }
    }

    /**
     * cate4 없이 cate5만으로 매칭 찾기
     */
    private function findMatchingCate5WithoutCate4($oldName, $strategy): ?array
    {
        if ($strategy === 'name') {
            // 이름으로 정확히 매칭
            $cate5 = Cate5::where('name', $oldName)->first();
            return $cate5 ? ['cate4_id' => $cate5->cate4_id, 'cate5_id' => $cate5->id] : null;
        } elseif ($strategy === 'id') {
            // ID로 매칭
            $cate5 = Cate5::where('name', $oldName)->first();
            return $cate5 ? ['cate4_id' => $cate5->cate4_id, 'cate5_id' => $cate5->id] : null;
        } else {
            // auto: 이름으로 먼저 시도, 실패하면 유사한 이름 찾기
            $cate5 = Cate5::where('name', $oldName)->first();
            if ($cate5) {
                return ['cate4_id' => $cate5->cate4_id, 'cate5_id' => $cate5->id];
            }

            // 유사한 이름 찾기
            $cate5 = Cate5::where('name', 'like', "%{$oldName}%")
                ->orWhere('name', 'like', "%" . substr($oldName, 0, 3) . "%")
                ->first();
            return $cate5 ? ['cate4_id' => $cate5->cate4_id, 'cate5_id' => $cate5->id] : null;
        }
    }

    /**
     * 매핑 계획 표시
     */
    private function displayMappingPlan($mappingPlan): void
    {
        $this->info("📋 매핑 계획 ({$mappingPlan->count()}개)");

        $this->table(
            ['repair_category_id', '기존 cate4', '기존 cate5', '새 cate4_id', '새 cate5_id', '매칭 방법'],
            $mappingPlan->map(function ($item) {
                return [
                    $item['repair_category_id'],
                    $item['old_cate4_name'] ?? 'NULL',
                    $item['old_cate5_name'] ?? 'NULL',
                    $item['new_cate4_id'] ?? 'NULL',
                    $item['new_cate5_id'] ?? 'NULL',
                    $item['match_method']
                ];
            })->toArray()
        );
    }

    /**
     * 재구성 실행
     */
    private function executeRebuild($mappingPlan, $isDryRun): void
    {
        $this->info('🔨 재구성 실행 중...');

        $updatedCount = 0;
        $feeRangeUpdatedCount = 0;

        DB::transaction(function () use ($mappingPlan, $isDryRun, &$updatedCount, &$feeRangeUpdatedCount) {
            foreach ($mappingPlan as $mapping) {
                try {
                    if (!$isDryRun) {
                        // 1. repair_categories 업데이트
                        DB::table('repair_categories')
                            ->where('id', $mapping['repair_category_id'])
                            ->update([
                                'cate4_id' => $mapping['new_cate4_id'],
                                'cate5_id' => $mapping['new_cate5_id']
                            ]);

                        // 2. repair_fee_ranges의 repair_category_id는 그대로 유지
                        // (repair_category_id는 변경하지 않고, repair_categories의 cate4_id, cate5_id만 업데이트)
                    }

                    $updatedCount++;
                    $this->line("🔄 수정: repair_category_id {$mapping['repair_category_id']} (cate4: {$mapping['old_cate4_id']} → {$mapping['new_cate4_id']}, cate5: {$mapping['old_cate5_id']} → {$mapping['new_cate5_id']})");

                } catch (\Exception $e) {
                    $this->error("❌ 오류 발생 (repair_category_id {$mapping['repair_category_id']}): " . $e->getMessage());
                    Log::error("RepairCategoryRebuild 오류", [
                        'repair_category_id' => $mapping['repair_category_id'],
                        'error' => $e->getMessage()
                    ]);
                    throw $e; // 트랜잭션 롤백
                }
            }
        });

        $this->info("📊 재구성 결과: {$updatedCount}개 repair_categories 수정");
    }
}
