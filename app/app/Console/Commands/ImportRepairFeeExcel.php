<?php

namespace App\Console\Commands;

use App\Models\RepairCategory;
use App\Models\RepairFee;
use App\Models\RepairFeeRange;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * 먼저 모니터/애플제품/공통 금액을 수동으로 입력하고,
 * 엑셀에서 그 부분을 지우고,
 * 그 다음에 이 커맨드를 실행하면 됨<br>
 * docker compose exec laravel php artisan repair:import-excel repair_fees.xlsx
 *
 * 실행 후 아래 쿼리를 실행해 줘야 정상적으로 됨
 *
 * -- min_value가 0이고 max_value가 100000인 레코드 → min_value를 50000으로 업데이트
 * UPDATE repair_fee_ranges
 * SET min_value = 50000
 * WHERE min_value = 0 AND max_value = 100000;
 *
 * -- min_value가 0이고 max_value가 200000인 레코드 → min_value를 100000으로 업데이트
 * UPDATE repair_fee_ranges
 * SET min_value = 100000
 * WHERE min_value = 0 AND max_value = 200000;
 *
 * -- min_value가 200000이고 max_value가 0인 레코드 → max_value를 1000000000으로 업데이트
 * UPDATE repair_fee_ranges
 * SET max_value = 1000000000
 * WHERE min_value = 200000 AND max_value = 0;
 */
class ImportRepairFeeExcel extends Command
{
    /**
     * 커맨드 이름과 설명
     */
    protected $signature = 'repair:import-excel {file : 파일 경로 (storage/ 기준)}';
    protected $description = 'storage 폴더에 있는 엑셀 파일을 읽어 수리비 데이터를 가져옵니다.';

    /**
     * 커맨드 실행
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        $this->info("파일 '{$filePath}'로부터 데이터를 가져오는 중...");

        // 파일이 storage에 있는지 확인
        if (!Storage::exists($filePath)) {
            $this->error("파일을 찾을 수 없습니다: {$filePath}");
            return 1;
        }

        try {
            // 엑셀 파일 읽기
            $fullPath = Storage::path($filePath);
            $spreadsheet = IOFactory::load($fullPath);
            $worksheet = $spreadsheet->getActiveSheet();
            $data = $worksheet->toArray();

            // 헤더 제거
            array_shift($data);

            // 진행 상황 표시용 프로그레스 바
            $progressBar = $this->output->createProgressBar(count($data));
            $progressBar->start();

            $importCount = 0;
            $errorCount = 0;

            DB::beginTransaction();

            foreach ($data as $row) {
                // 빈 행 건너뛰기
                if (empty($row[0]) || count($row) < 5) {
                    $progressBar->advance();
                    continue;
                }

                try {
                    // 엑셀 데이터 매핑
                    $cate4Name = trim($row[0]); // 4차 카테고리 이름
                    $cate5Name = trim($row[1]); // 5차 카테고리 이름
                    $priceRange = trim($row[2]); // 상품단가구간
                    $repairAmount = $this->cleanNumberString($row[3]); // 리퍼완료
                    $checkAmount = $this->cleanNumberString($row[4]); // 점검완료(중)

                    // 카테고리 ID 조회
                    $category = DB::table('repair_categories')
                        ->join('cate4', 'repair_categories.cate4_id', '=', 'cate4.id')
                        ->join('cate5', 'repair_categories.cate5_id', '=', 'cate5.id')
                        ->where('cate4.name', $cate4Name)
                        ->where('cate5.name', $cate5Name)
                        ->select('repair_categories.id')
                        ->first();

                    if (!$category) {
                        continue;
                        // // 카테고리를 찾을 수 없는 경우 cate4와 cate5의 ID 직접 조회
                        // $cate4 = DB::table('cate4')->where('name', $cate4Name)->first();
                        // $cate5 = DB::table('cate5')->where('name', $cate5Name)->first();
                        //
                        // if (!$cate4 || !$cate5) {
                        //     $this->warn("\n카테고리를 찾을 수 없습니다: {$cate4Name}, {$cate5Name}");
                        //     $errorCount++;
                        //     $progressBar->advance();
                        //     continue;
                        // }
                        //
                        // // 카테고리가 없으면 새로 생성
                        // $categoryId = DB::table('repair_categories')->insertGetId([
                        //     'cate4_id' => $cate4->id,
                        //     'cate5_id' => $cate5->id
                        // ]);
                    } else {
                        $categoryId = $category->id;
                    }

                    // 상품단가구간에서 min, max 값 추출
                    $minValue = 0;
                    $maxValue = 0;

                    if (strpos($priceRange, '이상') !== false) {
                        $minValue = (int) $this->cleanNumberString(str_replace('이상', '', $priceRange));
                    } elseif (strpos($priceRange, '미만') !== false) {
                        $maxValue = (int) $this->cleanNumberString(str_replace('미만', '', $priceRange));
                    }

                    // RepairFeeRange 생성 또는 가져오기
                    $feeRange = RepairFeeRange::firstOrCreate([
                        'repair_category_id' => $categoryId,
                        'type' => RepairFeeRange::REPAIR_RANGE_TYPE['GENERAL'],
                        'model' => RepairFeeRange::REPAIR_RANGE_MODEL['ETC'],
                        'fee_type' => RepairFeeRange::REPAIR_RANGE_FEE_TYPE['PRICE'],
                        'fee_unit' => RepairFeeRange::REPAIR_RANGE_FEE_UNIT['WON'],
                        'min_value' => $minValue,
                        'max_value' => $maxValue,
                    ]);

                    // RepairFee 생성 (점검완료(중) 데이터)
                    if (!empty($checkAmount)) {
                        RepairFee::updateOrCreate(
                            [
                                'repair_fee_range_id' => $feeRange->id,
                                'repair_type' => RepairFee::REPAIR_TYPE['CHECK'],
                            ],
                            [
                                'amount' => $checkAmount,
                                'created_at' => now(),
                                'updated_at' => now()
                            ]
                        );
                    }

                    // RepairFee 생성 (리퍼완료 데이터)
                    if (!empty($repairAmount)) {
                        RepairFee::updateOrCreate(
                            [
                                'repair_fee_range_id' => $feeRange->id,
                                'repair_type' => RepairFee::REPAIR_TYPE['REPAIR'],
                            ],
                            [
                                'amount' => $repairAmount,
                                'created_at' => now(),
                                'updated_at' => now()
                            ]
                        );
                    }

                    $importCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $this->error("\n행 처리 중 오류 발생: " . $e->getMessage());
                }

                $progressBar->advance();
            }

            DB::commit();
            $progressBar->finish();

            $this->newLine();
            $this->info("가져오기 완료! {$importCount}개 행 처리됨, {$errorCount}개 오류 발생.");

            return 0;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("엑셀 처리 중 오류 발생: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * 숫자 문자열에서 콤마나 공백 제거
     */
    private function cleanNumberString($str)
    {
        if (empty($str)) {
            return 0;
        }
        return (int) preg_replace('/[^0-9]/', '', $str);
    }
}
