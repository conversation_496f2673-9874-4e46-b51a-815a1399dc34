<?php

namespace App\Console\Commands;

use App\Models\Product;
use Illuminate\Console\Command;

/**
 * scout 커맨드<br>
 * docker compose exec laravel php artisan scout -h
 * docker compose exec laravel php artisan scout:delete-all-indexes
 * docker compose exec laravel php artisan scout:import "App\Models\Product"
 * docker compose exec laravel php artisan scout:sync-index-settings
 *
 * command 명령어 실행<br>
 * - 기본 명령어: php artisan product:meilisearch-sync<br>
 * - 옵션:<br>
 * --설정만 동기화: --index-settings<br>
 * --데이터만 인덱싱: --index-data<br>
 * --청크 크기 지정: --chunk-size=500<br>
 * - 예제: php artisan product:meilisearch-sync --index-data --chunk-size=1000
 *
 * @return int
 */
class ProductMeilisearchSync extends Command
{
    /**
     * 명령어 이름과 설명
     *
     * @var string
     */
    protected $signature = 'product:meilisearch-sync
                            {--index-settings : Meilisearch 인덱스 설정만 동기화}
                            {--index-data : 데이터만 인덱싱}
                            {--chunk-size=500 : 한 번에 처리할 레코드 수}';

    /**
     * 명령어 설명
     *
     * @var string
     */
    protected $description = 'Product 모델의 Meilisearch 인덱스 설정 및 데이터를 동기화합니다.';

    /**
     * 명령어 실행
     *
     * @return int
     */
    public function handle()
    {
        try {
            // 옵션이 지정되지 않았으면 모두 실행
            $syncIndexSettings = $this->option('index-settings') || (!$this->option('index-settings') && !$this->option('index-data'));
            $syncIndexData = $this->option('index-data') || (!$this->option('index-settings') && !$this->option('index-data'));
            $chunkSize = (int) $this->option('chunk-size');

            if ($syncIndexSettings) {
                $this->info('Meilisearch 인덱스 설정을 동기화하는 중...');
                Product::syncMeilisearchIndexSettings();
                $this->info('Meilisearch 인덱스 설정 동기화 완료!');
            }

            if ($syncIndexData) {
                $this->info("Product 데이터를 Meilisearch에 인덱싱하는 중... (청크 크기: {$chunkSize})");
                Product::indexAllToMeilisearch($chunkSize);
                $this->info('Product 데이터 인덱싱 완료!');
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('오류 발생: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
