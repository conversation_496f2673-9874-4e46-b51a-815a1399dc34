<?php

namespace App\Traits\Repair;

use App\Models\RepairGrade;

trait GradeTrait
{
    use ProcessTrait;

    public function getGrades(int $processId): array
    {
        $process = $this->findProcess($processId);
        return $process->repairGrades->toArray();
    }

    public function findGradeIdByCode(string $code): int
    {
        return RepairGrade::where('code', $code)->first()->id;
    }

    public function findGradeByCode(string $code): RepairGrade
    {
        return RepairGrade::where('code', $code)->first();
    }
}
