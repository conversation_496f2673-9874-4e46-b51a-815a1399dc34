<?php

namespace App\Traits\Repair;

use App\Models\RepairSymptom;
use Exception;
use Illuminate\Support\Collection;

trait SymptomTrait
{
    /**
     * @throws Exception
     */
    public function findSymptom(int $id): RepairSymptom
    {
        $symptom = RepairSymptom::find($id);

        if ($symptom === null) {
            throw new Exception("증상 [{$id}]을 찾을 수 없습니다.");
        }

        return $symptom;
    }

    /**
     *
     */
    public function getSymptomsByType(string $type): Collection
    {
        return RepairSymptom::where('type', $type)->get();
    }

    public function findSymptomIdByCode(string $code): int
    {
        return RepairSymptom::where('code', $code)->first()->id;
    }

    /**
     * 타입에 따른 상품의 증상 리스트
     * @param int $reqType
     * @return array
     */
    public function getSymptoms(int $reqType): array
    {
        $symptomTypeMap = [1 => 'general', 2 => 'apple'];
        $symptomType = $symptomTypeMap[$reqType] ?? 'general';

        $symptoms = $this->getSymptomsByType($symptomType);

        return $symptoms->toArray();
    }
}
