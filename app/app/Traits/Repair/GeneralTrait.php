<?php

namespace App\Traits\Repair;

use App\Models\Product;
use App\Models\RepairFee;
use App\Services\SimpleLogService;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

trait GeneralTrait
{
    // 상수 추출
    const CHECK_FEE = 5800;
    const REPAIR_FEE = 8200;
    const LOW_MAX_VALUE = 50000;
    const HIGH_MAX_VALUE = 100000000;
    const MIN_VALUE = 0;

    /**
     * 일반 제품의 기본 수리비 범위 ID 가져오기
     * @param Product $product
     * @param string $repairType
     * @return int
     */
    public function getDefaultGeneralFeeRangeId(Product $product, string $repairType): int
    {
        $isLowPrice = $product->amount < 50000;

        if ($repairType === RepairFee::REPAIR_TYPE['CHECK']) {
            return $isLowPrice
                ? RepairFee::DEFAULT_FEE_RANGE_IDS['GENERAL_CHECK_LOW']
                : RepairFee::DEFAULT_FEE_RANGE_IDS['GENERAL_CHECK_HIGH'];
        } else {
            return $isLowPrice
                ? RepairFee::DEFAULT_FEE_RANGE_IDS['GENERAL_REPAIR_LOW']
                : RepairFee::DEFAULT_FEE_RANGE_IDS['GENERAL_REPAIR_HIGH'];
        }
    }

    /**
     * 일반 제품 기본 가격 정보 가져오기 (기존 메서드 개선)
     */
    public function getDefaultGeneralFeeList(Product $product, string $repairType = 'CHECK'): array
    {
        try {
            $defaultRangeId = $this->getDefaultGeneralFeeRangeId($product, $repairType);
            $defaultInfo = RepairFee::getDefaultFeeRangeInfo($defaultRangeId);

            // defaultInfo가 null인 경우 안전한 기본값 사용
            if (!$defaultInfo) {
                SimpleLogService::error('repair', '기본값 정보를 찾을 수 없음', [
                    'defaultRangeId' => $defaultRangeId,
                    'repairType' => $repairType,
                    'product_amount' => $product->amount,
                ]);

                // 안전한 기본값 설정
                $defaultInfo = [
                    'amount' => $repairType === 'CHECK' ? self::CHECK_FEE : self::REPAIR_FEE,
                    'min_value' => 0,
                    'max_value' => 100000000,
                ];
            }

            // 수리 타입에 따른 금액 및 ID 결정
            $fee = $repairType === 'CHECK' ? self::CHECK_FEE : self::REPAIR_FEE;
            $lowRangeId = $repairType === 'CHECK'
                ? RepairFee::DEFAULT_FEE_RANGE_IDS['GENERAL_CHECK_LOW']
                : RepairFee::DEFAULT_FEE_RANGE_IDS['GENERAL_REPAIR_LOW'];
            $highRangeId = $repairType === 'CHECK'
                ? RepairFee::DEFAULT_FEE_RANGE_IDS['GENERAL_CHECK_HIGH']
                : RepairFee::DEFAULT_FEE_RANGE_IDS['GENERAL_REPAIR_HIGH'];

            // 범위 생성 메서드 호출
            $feeRangeList = $this->createFeeRangeList($lowRangeId, $highRangeId, $fee);

            SimpleLogService::debug('repair', 'getDefaultGeneralFeeList 성공', [
                'defaultRangeId' => $defaultRangeId,
                'defaultInfo' => $defaultInfo,
                'feeRangeList_count' => count($feeRangeList),
            ]);

            return [
                'feeRangeList' => $feeRangeList,
                'invoiceAmount' => $defaultInfo['amount'],
                'minValue' => $defaultInfo['min_value'],
                'maxValue' => $defaultInfo['max_value'],
                'defaultRangeId' => $defaultRangeId,
            ];
        } catch (Exception $e) {
            SimpleLogService::error('repair', 'getDefaultGeneralFeeList 오류', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'repairType' => $repairType,
            ]);

            // 오류 발생 시 빈 배열 반환
            return [
                'feeRangeList' => [],
                'invoiceAmount' => 0,
                'minValue' => 0,
                'maxValue' => 0,
                'defaultRangeId' => null,
            ];
        }
    }

    /**
     * 요금 범위 리스트 생성
     * @param int $lowRangeId 낮은 범위 ID
     * @param int $highRangeId 높은 범위 ID
     * @param int $fee 요금
     * @return array
     */
    private function createFeeRangeList(int $lowRangeId, int $highRangeId, int $fee): array
    {

        $formattedFee = number_format($fee) . '원';

        return [
            [
                'id' => $lowRangeId,
                'min_value' => self::MIN_VALUE,
                'max_value' => self::LOW_MAX_VALUE,
                'amount' => $fee,
                'formatted_amount' => $formattedFee,
                'range' => ' 0원 초과 ~ 50,000원 이하',
            ],
            [
                'id' => $highRangeId,
                'min_value' => self::LOW_MAX_VALUE,
                'max_value' => self::HIGH_MAX_VALUE,
                'amount' => $fee,
                'formatted_amount' => $formattedFee,
                'range' => ' 50,000원 초과 ~ 100000000원 이하',
            ],
        ];
    }

    /**
     * 일반제품 수리 요금 쿼리 빌더
     */
    public function generalFeeListQuery($product, $repairType): Builder
    {
        return DB::table('repair_categories')
            ->join('repair_fee_ranges', 'repair_categories.id', '=', 'repair_fee_ranges.repair_category_id')
            ->join('repair_fees', 'repair_fee_ranges.id', '=', 'repair_fees.repair_fee_range_id')
            ->where('repair_categories.cate4_id', $product->cate4_id)
            ->where('repair_categories.cate5_id', $product->cate5_id)
            ->where('repair_fee_ranges.type', 'general')
            ->where('repair_fees.repair_type', RepairFee::REPAIR_TYPE[$repairType])
            ->select(
                'repair_fee_ranges.id',
                'repair_fee_ranges.fee_type',
                'repair_fee_ranges.fee_unit',
                'repair_fee_ranges.min_value',
                'repair_fee_ranges.max_value',
                'repair_fees.amount'
            );
    }
}
