<?php

namespace App\Traits\Repair;

trait FeeTypeTrait
{
    use GradeTrait;

    /**
     * 수리 등급 코드를 기반으로 수리비를 결정할 수리 유형을 리턴.
     * repair_fees 테이블 검색에 사용.
     *
     * @param string $code
     * @return string|null 수리 유형 ('REFURB', 'CHECK') 또는 null
     */
    public function getRepairTypeByRepairGradeCode(string $code): ?string
    {
        $grade = $this->findGradeByCode($code);

        if (str_starts_with($grade->code, 'ST_XL')) {
            return null;
        } elseif ($grade->code === 'ST_REFURB') {
            return 'REPAIR';
        } else {
            return 'CHECK';
        }
    }
}
