<?php

namespace App\Traits\Repair;

use App\Models\RepairProduct;
use App\Models\RepairParts;
use App\Models\User;
use App\Models\WorkStatus;
use App\Services\ProductLogService;
use App\Services\SimpleLogService;
use App\Services\WorkStatusService;
use Exception;
use Illuminate\Support\Facades\Auth;

trait ProductPartsTrait
{
    /**
     * 수리 제품 구성품 업데이트 처리
     * 제품 수리 로거는 배열에 넣어서 원래 호출했던 곳에서 저장함(여기서는 배열에 넣기만 하면 됨)
     *
     * @param RepairProduct $repairProduct
     * @param array $data
     * @param User|null $user
     * @param WorkStatusService $workStatusService
     * @param ProductLogService $logger
     * @return void
     * @throws Exception
     */
    public function updateRepairProductParts(
        RepairProduct $repairProduct,
        array $data,
        ?User $user,
        WorkStatusService $workStatusService,
        ProductLogService $logger
    ): void {
        $statusIds = $workStatusService->getIds([
            WorkStatus::LINK_REPAIR_ADD_PARTS,
        ]);

        // 상품 정보 미리 변수에 저장
        $productQaid = $repairProduct->product->qaid ?? '';
        $productName = $repairProduct->product->name ?? '';

        // 추가할 구성품 처리
        $this->addRepairProductParts($repairProduct, $data, $user, $statusIds, $productQaid, $productName, $logger);

        // 수정할 구성품 처리
        $this->updateExistingRepairProductParts($repairProduct, $data, $user, $statusIds, $productQaid, $productName, $logger);

        // 삭제할 구성품 처리
        $this->removeRepairProductParts($repairProduct, $data, $user, $statusIds, $productQaid, $productName, $logger);

        // 변경 후 관련 캐시나 계산된 값 갱신이 필요한 경우
        $repairProduct->refresh();

        // 로그 기록 (선택사항)
        SimpleLogService::debug('repair', '구성품 정보 업데이트', [
            'added' => $data['add_parts'] ?? [],
            'removed' => $data['remove_parts'] ?? [],
            'updated' => $data['update_parts'] ?? []
        ]);
    }

    /**
     * 수리 제품 구성품 추가 처리
     *
     * @param RepairProduct $repairProduct
     * @param array $data
     * @param User $user
     * @param array $statusIds
     * @param string $productQaid
     * @param string $productName
     * @param ProductLogService $logger
     * @return void
     */
    protected function addRepairProductParts(
        RepairProduct $repairProduct,
        array $data,
        User $user,
        array $statusIds,
        string $productQaid,
        string $productName,
        ProductLogService $logger
    ): void {
        $currentParts = $repairProduct->repairProductParts()->get()->keyBy('repair_parts_id');

        if (isset($data['add_parts']) && is_array($data['add_parts'])) {
            foreach ($data['add_parts'] as $partData) {
                $repairPartsId = $partData['parts_id'];
                $quantity = $partData['quantity'] ?? 1;
                $price = $partData['price'] ?? 0;

                if ($currentParts->has($repairPartsId)) {
                    // 이미 존재하는 구성품이면 수량 업데이트
                    $currentPart = $currentParts[$repairPartsId];
                    $currentPart->update([
                        'quantity' => $quantity,
                        'price' => $price,
                    ]);

                    $repairPartsName = $currentPart->repairParts->name ?? '';
                    $logger->addLog(
                        $repairProduct->product,
                        'App\Models\RepairProductParts',
                        $currentPart->id,
                        $statusIds[WorkStatus::LINK_REPAIR_ADD_PARTS],
                        $user->id,
                        "[{$productQaid}]{$productName}<br>- 수리 구성품 수량 업데이트: {$repairPartsName}<br>- 수량: {$quantity}개<br>- 단가: " . number_format($price) . "원"
                    );
                } else {
                    // 새로운 구성품 추가
                    $repairProduct->repairProductParts()->create([
                        'repair_parts_id' => $repairPartsId,
                        'quantity' => $quantity,
                        'price' => $price,
                    ]);

                    // 수리 부품 이름 가져오기
                    $repairParts = RepairParts::find($repairPartsId);
                    $repairPartsName = $repairParts ? $repairParts->name : '';

                    $logger->addLog(
                        $repairProduct->product,
                        'App\Models\RepairProductParts',
                        $repairPartsId,
                        $statusIds[WorkStatus::LINK_REPAIR_ADD_PARTS],
                        $user->id,
                        "[{$productQaid}]{$productName}<br>- 수리 구성품 추가: {$repairPartsName}<br>- 수량: {$quantity}개<br>- 단가: " . number_format($price) . "원"
                    );
                }
            }
        }
    }

    /**
     * 기존 수리 제품 구성품 업데이트 처리
     *
     * @param RepairProduct $repairProduct
     * @param array $data
     * @param User $user
     * @param array $statusIds
     * @param string $productQaid
     * @param string $productName
     * @param ProductLogService $logger
     * @return void
     */
    protected function updateExistingRepairProductParts(
        RepairProduct $repairProduct,
        array $data,
        User $user,
        array $statusIds,
        string $productQaid,
        string $productName,
        ProductLogService $logger
    ): void {
        $currentParts = $repairProduct->repairProductParts()->get()->keyBy('id');

        if (isset($data['update_parts']) && is_array($data['update_parts'])) {
            foreach ($data['update_parts'] as $partData) {
                $repairProductPartsId = $partData['id'] ?? null;

                if (!$repairProductPartsId) {
                    continue; // ID가 없으면 건너뛰기
                }

                if ($currentParts->has($repairProductPartsId)) {
                    $updateData = [];

                    if (isset($partData['quantity'])) {
                        $updateData['quantity'] = $partData['quantity'];
                    }

                    if (isset($partData['price'])) {
                        $updateData['price'] = $partData['price'];
                    }

                    if (!empty($updateData)) {
                        $currentParts[$repairProductPartsId]->update($updateData);

                        // 업데이트 후 최신 데이터를 다시 가져오기
                        $currentParts[$repairProductPartsId]->refresh();

                        $repairPartsName = $currentParts[$repairProductPartsId]->repairParts->name ?? '';
                        $quantity = $currentParts[$repairProductPartsId]->quantity;
                        $price = $currentParts[$repairProductPartsId]->price;

                        $logger->addLog(
                            $repairProduct->product,
                            'App\Models\RepairProductParts',
                            $repairProductPartsId,
                            $statusIds[WorkStatus::LINK_REPAIR_ADD_PARTS],
                            $user->id,
                            "[{$productQaid}]{$productName}<br>- 수리 구성품 수정: {$repairPartsName}<br>- 수량: {$quantity}개<br>- 단가: " . number_format($price) . "원"
                        );
                    }
                }
            }
        }
    }

    /**
     * 수리 제품 구성품 삭제 처리
     *
     * @param RepairProduct $repairProduct
     * @param array $data
     * @param User $user
     * @param array $statusIds
     * @param string $productQaid
     * @param string $productName
     * @param ProductLogService $logger
     * @return void
     */
    protected function removeRepairProductParts(
        RepairProduct $repairProduct,
        array $data,
        User $user,
        array $statusIds,
        string $productQaid,
        string $productName,
        ProductLogService $logger
    ): void {
        $currentParts = $repairProduct->repairProductParts()->get()->keyBy('id');

        if (isset($data['remove_parts']) && is_array($data['remove_parts'])) {
            $repairProduct->repairProductParts()
                ->whereIn('id', $data['remove_parts'])
                ->delete();

            foreach ($data['remove_parts'] as $repairProductPartsId) {
                if ($currentParts->has($repairProductPartsId)) {
                    $currentPart = $currentParts[$repairProductPartsId];
                    $repairPartsName = $currentPart->repairParts->name ?? '';

                    $logger->addLog(
                        $repairProduct->product,
                        'App\Models\RepairProductParts',
                        $repairProductPartsId,
                        $statusIds[WorkStatus::LINK_REPAIR_ADD_PARTS],
                        $user->id,
                        "[{$productQaid}]{$productName}<br>- 수리 구성품 삭제: {$repairPartsName}"
                    );
                }
            }
        }
    }

}
