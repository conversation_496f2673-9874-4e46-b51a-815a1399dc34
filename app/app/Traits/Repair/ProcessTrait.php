<?php

namespace App\Traits\Repair;

use App\Models\RepairProcess;
use Exception;

trait ProcessTrait
{
    use SymptomTrait;

    public function findProcess(int $id): RepairProcess
    {
        return RepairProcess::find($id);
    }

    public function findProcessIdByCode(string $code): int
    {
        return RepairProcess::where('code', $code)->first()->id;
    }

    public function findProcessNameByCode(string $code): int
    {
        return RepairProcess::where('code', $code)->first()->name;
    }

    public function getProcesses(int $symptomId): array
    {
        try {
            $symptom = $this->findSymptom($symptomId);

            // default_repair_process_id가 설정되어 있는지 확인
            if ($symptom->default_repair_process_id) {
                // defaultRepairProcess 관계를 사용하여 기본 process를 가져옴
                $defaultProcess = $symptom->defaultRepairProcess;

                // 기본 process가 존재하면 배열에 담아 반환하고, 없으면 빈 배열을 반환
                return $defaultProcess ? [$defaultProcess->toArray()] : [];
            }

            // default_repair_process_id가 null이면,
            // repairProcesses 관계를 통해 관련된 모든 process를 가져옴
            return $symptom->repairProcesses->toArray();
        } catch (Exception $e) {
            return [];
        }
    }
}
