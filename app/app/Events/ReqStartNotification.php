<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReqStartNotification implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private string $message;

    /**
     * Create a new event instance.
     */
    public function __construct(string $message)
    {
        $this->message = $message;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('cnsprowms'),
        ];
    }

    /**
     * Get the event name this event should broadcast as.
     *
     * @return string
     */
    public function broadcastAs(): string
    {
        return 'admin-notification';
    }

    /**
     * 알림을 보낼 내용을 적는 곳
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'success' => true,
            'message' => $this->message,
        ];
    }
}
