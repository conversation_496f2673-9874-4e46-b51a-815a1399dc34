<?php

namespace App\Imports;

use App\Models\User;
use App\Models\UserAttendance;
use App\Services\SimpleLogService;
use Carbon\Carbon;
use Exception;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class AttendanceImport implements ToModel, WithHeadingRow, WithValidation
{
    /**
     * 생성자에서 헤더 포맷터를 none으로 설정<br>
     * 이러면 제목줄에 한글이 있어도 사용 가능하다고 함
     */
    public function __construct()
    {
        HeadingRowFormatter::default('none');
    }

    /**
     * @param array $row
     *
     * @return UserAttendance|null
     */
    public function model(array $row): ?UserAttendance
    {
        try {
            SimpleLogService::info('daily', '근태관리 엑셀파일 가져오기 시작', [
                'row' => $row
            ]);

            // 사용자 ID로 사용자 찾기 (caps_id 필드 사용)
            $capsId = $row['사용자ID'] ?? null;
            if (!$capsId) {
                SimpleLogService::warning('daily', '현재 행의 [사용자 ID]를 찾을 수 없습니다.', [
                    'row' => $row
                ]);
                return null;
            }

            $user = User::where('caps_id', $capsId)->first();

            // 사용자를 찾지 못한 경우 해당 행 건너뛰기
            if (!$user) {
                return null;
            }

            // 날짜 형식 변환 (2025/04/25 -> 2025-04-25)
            try {
                $workDate = Carbon::createFromFormat('Y/m/d', $row['근무일자'])->format('Y-m-d');
            } catch (Exception $e) {
                SimpleLogService::error('setting', '근태관리 엑셀파일 가져오기 실패', [
                    'row' => $row
                ], $e);

                return null;
            }

            // 휴무일 여부 확인 (근무일명칭에 '휴일'가 포함되어 있는지 확인)
            $isHoliday = str_contains($row['근무일명칭'], UserAttendance::DAY_TYPE_HOLIDAY);

            // 시간 형식 변환 (HH:MM -> HH:MM:00)
            // 휴무일이거나 값이 없는 경우 처리
            // 출근, 퇴근은 휴무일에 null 가능
            $clockIn = $isHoliday ? null : (isset($row['출근']) ? $this->formatTime($this->getTimeValue($row, '출근')) : null);
            $clockOut = $isHoliday ? null : (isset($row['퇴근']) ? $this->formatTime($this->getTimeValue($row, '퇴근')) : null);

            // 나머지 필드는 기본값 '00:00:00' 사용
            // 엑셀에서 가져온 값이 정확히 처리되도록 특별히 처리
            $lateHours = $this->formatTime($this->getTimeValue($row, '지각'));
            $earlyLeaveHours = $this->formatTime($this->getTimeValue($row, '조퇴'));
            $regularHours = $this->formatTime($this->getTimeValue($row, '기본'));
            $overtimeHours = $this->formatTime($this->getTimeValue($row, '연장'));
            $totalHours = $this->formatTime($this->getTimeValue($row, '총합'));

            // 지각 및 조퇴 여부 확인
            // getTimeValue 메소드를 사용하여 시간 값을 표준화
            $lateTime = $this->getTimeValue($row, '지각');
            $earlyLeaveTime = $this->getTimeValue($row, '조퇴');

            // 표준화된 값으로 비교
            $isLate = $lateTime !== '0:00' && $lateTime !== '00:00' && !$isHoliday;
            $isEarlyLeave = $earlyLeaveTime !== '0:00' && $earlyLeaveTime !== '00:00' && !$isHoliday;

            // 중복 키 오류 방지를 위해 기존 출석 데이터 확인
            $existingAttendance = UserAttendance::where('user_id', $user->id)
                ->where('work_date', $workDate)
                ->first();

            $attendanceData = [
                'user_id' => $user->id,
                'work_date' => $workDate,
                'day_type' => $row['근무일명칭'] === UserAttendance::DAY_TYPE_WORKDAY ? UserAttendance::DAY_TYPE_WORKDAY : UserAttendance::DAY_TYPE_HOLIDAY,
                'clock_in' => $clockIn,
                'clock_out' => $clockOut,
                'is_late' => $isLate,
                'late_hours' => $lateHours,
                'is_early_leave' => $isEarlyLeave,
                'early_leave_hours' => $earlyLeaveHours,
                'regular_hours' => $regularHours,
                'overtime_hours' => $overtimeHours,
                'total_hours' => $totalHours,
            ];

            // 기존 데이터가 있으면 업데이트, 없으면 새로 생성
            if ($existingAttendance) {
                $existingAttendance->fill($attendanceData);
                $existingAttendance->save();

                // 강제로 updated_at 업데이트
                $existingAttendance->touch();

                return $existingAttendance;
            }

            // 새 UserAttendance 모델 생성
            return new UserAttendance($attendanceData);
        } catch (Exception $e) {
            // 오류 발생 시 상세 로그 기록
            SimpleLogService::error('setting', 'AttendanceImport::model 에러', [
                'row' => $row
            ], $e);

            // null 반환하여 해당 행 건너뛰기
            return null;
        }
    }

    /**
     * 시간 형식을 HH:MM:00 형태로 변환
     * null이나 비어있는 값은 '00:00:00'으로 반환
     *
     * @param string|null $time
     * @return string
     */
    private function formatTime(?string $time): string
    {
        // 시간이 null이거나 비어있는 경우 기본값 반환
        if ($time === null || trim($time) === '' || $time === '0:00' || $time === '00:00') {
            return '00:00:00';
        }

        // 엑셀에서 가져온 시간 형식이 다양할 수 있으므로 여러 패턴 처리

        // HH:MM 형식 (08:30)
        if (preg_match('/^\d{2}:\d{2}$/', $time)) {
            return $time . ':00';
        }

        // H:MM 형식 (9:00)
        if (preg_match('/^\d{1}:\d{2}$/', $time)) {
            return '0' . $time . ':00';
        }

        // 시간만 있는 형식 (9)
        if (preg_match('/^\d{1,2}$/', $time)) {
            return str_pad($time, 2, '0', STR_PAD_LEFT) . ':00:00';
        }

        return '00:00:00';
    }

    /**
     * 엑셀에서 시간 값을 추출하여 적절한 형식으로 반환
     *
     * @param array $row 엑셀 행 데이터
     * @param string $key 검색할 키
     * @return string 추출된 시간 값 또는 기본값
     */
    private function getTimeValue(array $row, string $key): string
    {
        // 키가 없는 경우 기본값 반환
        if (!isset($row[$key])) {
            return '00:00';
        }

        $value = $row[$key];

        // 값이 없거나 비어있는 경우 기본값 반환
        if (trim($value) === '') {
            return '00:00';
        }

        // 엑셀에서 시간이 소수점 형태로 저장되는 경우 처리
        if (is_numeric($value)) {
            // 엑셀에서 시간은 일(day)의 분수로 저장됨
            // 예: 0.375 = 9시간 (0.375 * 24 = 9)
            // 예: 0.0020833 = 3분 (0.0020833 * 24 * 60 = 3)
            $totalMinutes = round($value * 24 * 60);
            $hours = floor($totalMinutes / 60);
            $minutes = $totalMinutes % 60;

            return $hours . ':' . str_pad($minutes, 2, '0', STR_PAD_LEFT);
        }

        return (string)$value;
    }

    /**
     * 데이터 유효성 검사 규칙
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            '사용자ID' => 'required',
            '근무일자' => 'required',
            '근무일명칭' => 'required',
        ];
    }

    /**
     * 헤더 행 지정 (기본값은 1)
     *
     * @return int
     */
    public function headingRow(): int
    {
        return 1;
    }
}
