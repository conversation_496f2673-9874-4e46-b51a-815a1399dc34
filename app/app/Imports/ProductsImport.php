<?php

namespace App\Imports;

use App\Events\ReqFinishNotification;
use App\Exports\DuplicateQaidExport;
use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\Product;
use App\Models\ProductBarcode;
use App\Models\ProductLink;
use App\Models\ProductLog;
use App\Models\ProductLot;
use App\Models\ProductVendor;
use App\Models\Req;
use App\Models\ReturnReason;
use App\Models\ReturnReasonB;
use App\Models\ReturnReasonM;
use App\Models\WorkStatus;
use App\Models\User;
use App\Services\SimpleLogService;
use App\Services\TelegramService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\RemembersChunkOffset;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
use Maatwebsite\Excel\Events\AfterImport;
use Maatwebsite\Excel\Events\ImportFailed;
use Throwable;

/**
 * 상품 입고 엑셀 파일 임포트 클래스
 *
 * WithCalculatedFormulas를 사용하여 Excel의 수식을 자동으로 계산된 값으로 변환
 * 이를 통해 수식이 포함된 셀도 안전하게 처리할 수 있음
 */
class ProductsImport implements ShouldQueue, WithChunkReading, WithStartRow, WithEvents, ToCollection, WithCalculatedFormulas
{
    use Importable, RemembersChunkOffset;

    protected Req $req;
    protected User $user;
    protected string $excelPath;
    protected string $redirect;
    protected int $startRow;
    protected int $chunkSize = 500; // 1000에서 500으로 줄여서 메모리 사용량 감소
    protected TelegramService $telegram;
    protected string $redisDuplicatedKey;
    protected string $redisChunkOffsetKey = 'req_import:chunk_offset';

    /** @var int|null 마지막 행 번호 (진행률 계산용) */
    protected ?int $lastRow = null;

    /** @var float 처리 시작 시간 */
    protected float $startTime;

    /** @var int 메모리 사용량 모니터링 임계값 (MB) */
    protected int $memoryThreshold = 200;

    /** @var int 청크 처리 간 메모리 정리 주기 */
    protected int $memoryCleanupInterval = 5;

    /** @var array 헤더 매핑 정보 */
    protected array $headerMapping = [];

    /** @var array 헤더별 컬럼 인덱스 */
    protected array $columnIndexes = [];

    /**
     * 헤더 매핑 설정
     * 영어/한글 헤더를 내부 필드명으로 매핑
     */
    protected function setHeaderMapping(): void
    {
        $config = config('excel-headers.mappings');

        // 설정 파일에서 매핑 정보 가져오기
        $this->headerMapping = array_merge($config['required'], $config['optional']);
    }

    /**
     * 헤더에서 컬럼 인덱스 찾기
     * @throws Exception
     */
    protected function findColumnIndexes(array $headers): void
    {
        $this->columnIndexes = [];
        $config = config('excel-headers');

        SimpleLogService::info('req', "컬럼 인덱스 찾기 시작", [
            'headers' => $headers,
            'header_mapping_count' => count($this->headerMapping)
        ]);

        // 헤더 검증
        $this->validateHeaders($headers, $config);

        foreach ($this->headerMapping as $field => $possibleHeaders) {
            $found = false;

            foreach ($possibleHeaders as $header) {
                $index = array_search($header, $headers);
                if ($index !== false) {
                    $this->columnIndexes[$field] = $index;
                    $found = true;
                    SimpleLogService::info('req', "헤더 매핑 성공", [
                        'field' => $field,
                        'header' => $header,
                        'index' => $index
                    ]);
                    break;
                }
            }

            if (!$found) {
                SimpleLogService::warning('req', "헤더를 찾을 수 없음", [
                    'field' => $field,
                    'possible_headers' => $possibleHeaders,
                    'actual_headers' => $headers
                ]);
            }
        }

        // 필수 필드 검증
        $requiredFields = array_keys(config('excel-headers.mappings.required'));
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (!isset($this->columnIndexes[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            $errorMessage = str_replace(':headers', implode(', ', $missingFields), $config['error_messages']['missing_required']);
            $errorMessage .= "\n실제 헤더: " . implode(', ', $headers);
            SimpleLogService::error('req', "필수 필드 누락", [
                'missing_fields' => $missingFields,
                'actual_headers' => $headers,
                'found_fields' => array_keys($this->columnIndexes)
            ]);
            throw new Exception($errorMessage);
        }

        SimpleLogService::info('req', "헤더 매핑 완료", [
            'column_indexes' => $this->columnIndexes,
            'headers' => $headers,
            'mapping_summary' => [
                'total_headers' => count($headers),
                'mapped_fields' => count($this->columnIndexes),
                'required_fields_found' => count(array_intersect_key($this->columnIndexes, array_flip($requiredFields))),
                'optional_fields_found' => count(array_diff_key($this->columnIndexes, array_flip($requiredFields)))
            ]
        ]);
    }

    /**
     * 헤더 검증
     * @throws Exception
     */
    protected function validateHeaders(array $headers, array $config): void
    {
        // 헤더 수 검증
        if (count($headers) < $config['validation']['min_required_headers']) {
            $errorMessage = str_replace(':min', $config['validation']['min_required_headers'], $config['error_messages']['too_few_headers']);
            throw new Exception($errorMessage);
        }

        if (count($headers) > $config['validation']['max_total_headers']) {
            $errorMessage = str_replace(':max', $config['validation']['max_total_headers'], $config['error_messages']['too_many_headers']);
            throw new Exception($errorMessage);
        }

        // 헤더 길이 검증
        foreach ($headers as $header) {
            if (strlen($header) > $config['validation']['max_header_length']) {
                $errorMessage = str_replace(':header', $header, $config['error_messages']['invalid_header_format']);
                throw new Exception($errorMessage);
            }
        }
    }

    /**
     * 헤더 정보 설정
     * @throws Exception
     */
    public function setHeaders(array $headers): void
    {
        SimpleLogService::info('req', "헤더 설정 시작", [
            'headers' => $headers,
            'headers_count' => count($headers)
        ]);

        try {
            $this->setHeaderMapping();
            SimpleLogService::info('req', "헤더 매핑 설정 완료", [
                'header_mapping_count' => count($this->headerMapping)
            ]);

            $this->findColumnIndexes($headers);
            SimpleLogService::info('req', "컬럼 인덱스 찾기 완료", [
                'column_indexes_count' => count($this->columnIndexes)
            ]);

            SimpleLogService::info('req', "헤더 설정 완료", [
                'column_indexes' => $this->columnIndexes,
                'total_mapped_fields' => count($this->columnIndexes)
            ]);
        } catch (Exception $e) {
            SimpleLogService::error('req', "헤더 설정 중 오류 발생", [
                'headers' => $headers,
                'error_message' => $e->getMessage()
            ], $e);
            throw $e;
        }
    }

    /**
     * 바이트 단위를 사람이 읽기 쉬운 형태로 변환
     *
     * @param int $bytes 바이트 수
     * @return string 포맷된 문자열
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 메모리 사용량을 모니터링하고 필요시 정리하는 메서드
     */
    private function checkAndCleanMemory(): void
    {
        $memoryUsage = memory_get_usage(true);
        $memoryUsageMB = $memoryUsage / 1024 / 1024;

        // 메모리 사용량이 임계값을 초과하면 강제 가비지 컬렉션 실행
        if ($memoryUsageMB > $this->memoryThreshold) {
            gc_collect_cycles();

            SimpleLogService::warning('req', "메모리 사용량 임계값 초과로 가비지 컬렉션 실행", [
                'memory_usage_mb' => round($memoryUsageMB, 2),
                'threshold_mb' => $this->memoryThreshold,
                'after_cleanup_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
            ]);
        }
    }

    /**
     * 청크 처리 후 메모리 정리
     */
    private function cleanupAfterChunk(): void
    {
        // 가비지 컬렉션 실행
        gc_collect_cycles();

        // 메모리 사용량 로그
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);

        SimpleLogService::info('req', "청크 처리 후 메모리 정리 완료", [
            'memory_current' => $this->formatBytes($memoryUsage),
            'memory_peak' => $this->formatBytes($memoryPeak)
        ]);
    }

    /**
     * 데이터베이스 연결 상태 확인 및 재연결
     */
    private function ensureDatabaseConnection(): void
    {
        try {
            // DB 연결 상태 확인
            DB::connection()->getPdo();
        } catch (Exception $e) {
            SimpleLogService::warning('req', "데이터베이스 연결 끊김 감지, 재연결 시도", [], $e);

            // 연결 재설정
            DB::reconnect();

            SimpleLogService::info('req', "데이터베이스 재연결 완료");
        }
    }

    public function __construct(Req $req, User $user, string $excelPath, string $redirect, int $startRow = 3, ?int $lastRow = null)
    {
        $this->req = $req;
        $this->user = $user;
        $this->excelPath = $excelPath;
        $this->redirect = $redirect;
        $this->startRow = $startRow;
        $this->lastRow = $lastRow;
        $this->startTime = microtime(true); // 처리 시작 시간 기록
        $this->telegram = new TelegramService();
        $this->redisDuplicatedKey = "req_import:duplicated_qaids:{$this->req->id}";

        // 최초 딱 한 번 Redis 키 초기화
        Redis::connection(0)->command('del', [
            $this->redisChunkOffsetKey,
            $this->redisDuplicatedKey
        ]);
    }

    public function startRow(): int
    {
        return $this->startRow;
    }

    /**
     * 메모리 관리
     * 엑셀 파일이 엄청 길 때 해당 개수만큼씩만 메모리로 로드해서 처리한다.(설정된 개수만큼만 로드해서 처리)<br>
     * 엑셀 데이터가 4,000개면 4,000번 실행 되는데 이 때 (4000/$this->chunkSize) 만큼 끊어서 처리
     *
     * @return int
     */
    public function chunkSize(): int
    {
        return $this->chunkSize;
    }

    public function registerEvents(): array
    {
        return [
            ImportFailed::class => function(ImportFailed $event) {
                $this->importFailedHandler($event);
            },
            AfterImport::class => function(AfterImport $event) {
                $this->afterImportHandler($event);
            }
        ];
    }

    protected function importFailedHandler(ImportFailed $event): void
    {
        // $this->telegram->sendMessage("[{$this->req->id}]요청서 입력 오류");

        SimpleLogService::error('req', "[{$this->req->id}]요청서 입력 오류", [], $event->e);

        // event(new ReqFinishNotification("입고 등록 오류 발생"));
    }

    // 모든 작업이 끝난 후 실행
    protected function afterImportHandler(AfterImport $event): void
    {
        // lastRow 체크: 마지막 행을 넘어서 처리된 경우 오류 로그
        $redis = Redis::connection(0);
        $finalOffset = $redis->get($this->redisChunkOffsetKey) ?? $this->startRow();

        if ($this->lastRow !== null && $finalOffset > $this->lastRow) {
            SimpleLogService::error('req', "마지막 행을 초과하여 처리됨 - 이는 예상치 못한 상황입니다", [
                'final_offset' => $finalOffset,
                'last_row' => $this->lastRow,
                'exceeded_by' => $finalOffset - $this->lastRow,
                'start_row' => $this->startRow
            ]);
        }

        // 처리 시간 및 성능 계산
        $endTime = microtime(true);
        $processingTime = $endTime - $this->startTime;
        $totalRows = $this->lastRow ? $this->lastRow - $this->startRow + 1 : 0;
        $rowsPerSecond = $totalRows > 0 ? round($totalRows / $processingTime, 2) : 0;

        // 메모리 사용량
        $memoryPeak = memory_get_peak_usage(true);

        $totalCount = $this->req->products()->count();
        $duplicateCount = $this->req->products()->where('duplicated', 'Y')->count();

        $this->req->update([
            'total_count' => $totalCount,
        ]);

        $this->req->reqCount()->updateOrCreate(
            ['req_id' => $this->req->id],
            [
                'unchecked' => $totalCount - $duplicateCount,
                'duplicated' => $duplicateCount,
            ]
        );

        $message = "[{$this->req->id}]요청서 입력 완료.\n입력된 개수: {$totalCount}";
//        $this->telegram->sendMessage($message);

        SimpleLogService::info('req', $message);

        // 성능 정보 로그 출력
        SimpleLogService::info('req', "[{$this->req->id}] 성능 정보", [
            'processing_time' => round($processingTime, 2) . '초',
            'total_rows' => $totalRows,
            'rows_per_second' => $rowsPerSecond,
            'memory_peak' => $this->formatBytes($memoryPeak),
            'total_products' => $totalCount,
            'duplicate_products' => $duplicateCount
        ]);

        // 성능 정보 텔레그램 전송
        $performanceMessage = "⚡ 성능 정보\n";
        $performanceMessage .= "처리 시간: " . round($processingTime, 2) . "초\n";
        $performanceMessage .= "처리 속도: {$rowsPerSecond}행/초\n";
        $performanceMessage .= "최대 메모리: {$this->formatBytes($memoryPeak)}\n";
        $performanceMessage .= "총 상품: {$totalCount}개\n";
        $performanceMessage .= "중복 상품: {$duplicateCount}개";
//        $this->telegram->sendMessage($performanceMessage);

        // QAID가 중복되었을 경우 엑셀파일로 출력
        $redis = Redis::connection(0);
        $allDuplicates = $redis->command('lrange', [
            $this->redisDuplicatedKey, 0, -1
        ]);

        if (!empty($allDuplicates)) {
            $excel = new DuplicateQaidExport($this->req, $this->redisDuplicatedKey);
            $excel->export();
        }

        // 업로드된 파일 삭제
        if (file_exists($this->excelPath)) {
            unlink($this->excelPath);
            SimpleLogService::info('req', "업로드 된 파일 삭제");
        }

        Redis::connection(0)->command('del', [
            $this->redisChunkOffsetKey,
            $this->redisDuplicatedKey
        ]);

        event(new ReqFinishNotification("입고 등록이 완료 되었습니다.", $this->redirect));
    }

    /**
     * @param  Collection  $collection
     * @throws Throwable
     */
    public function collection(Collection $collection): void
    {
        // 메모리 사용량 체크
        $this->checkAndCleanMemory();

        // DB 연결 상태 확인
        $this->ensureDatabaseConnection();

        $redis = Redis::connection(0);
        $chunkOffset = $redis->get($this->redisChunkOffsetKey) ?? $this->startRow();

        // lastRow 체크: 청크가 마지막 행을 넘어서는지 확인
        if ($this->lastRow !== null) {
            $chunkEndRow = $chunkOffset + $collection->count() - 1;

            // 청크의 시작점이 이미 마지막 행을 넘어선 경우에만 종료
            if ($chunkOffset > $this->lastRow) {
                SimpleLogService::info('req', "[{$this->req->id}] 마지막 행({$this->lastRow})을 초과하여 프로세스 종료", [
                    'chunk_start' => $chunkOffset,
                    'chunk_end' => $chunkEndRow,
                    'last_row' => $this->lastRow,
                    'chunk_size' => $collection->count(),
                    'reason' => 'chunk_start_exceeds_last_row'
                ]);

                // Redis 오프셋을 마지막 행으로 설정하여 더 이상 청크가 호출되지 않도록 함
                $redis->set($this->redisChunkOffsetKey, $this->lastRow + 1);

                // 정상 종료 알림 전송
                event(new ReqFinishNotification("입고 등록이 완료되었습니다.", $this->redirect));

                // 프로세스를 완전히 중단하기 위해 예외 발생
                throw new Exception("마지막 행({$this->lastRow})을 초과했습니다. 전체 프로세스를 중단합니다.");
            }

            // 청크의 일부가 마지막 행을 넘어서는 경우, 해당 부분만 제외하고 처리
            if ($chunkEndRow > $this->lastRow) {
                SimpleLogService::info('req', "청크의 일부가 마지막 행을 초과함 - 마지막 행까지만 처리", [
                    'chunk_start' => $chunkOffset,
                    'chunk_end' => $chunkEndRow,
                    'last_row' => $this->lastRow,
                    'chunk_size' => $collection->count(),
                    'will_process_until' => $this->lastRow
                ]);

                // 컬렉션을 마지막 행까지만 잘라내기
                $maxRowsToProcess = $this->lastRow - $chunkOffset + 1;
                $collection = $collection->take($maxRowsToProcess);

                // Redis 오프셋을 마지막 행으로 설정하여 다음 청크가 호출되지 않도록 함
                $redis->set($this->redisChunkOffsetKey, $this->lastRow + 1);
                
                // 마지막 행까지 처리한 후 프로세스를 완전히 중단
                SimpleLogService::info('req', "[{$this->req->id}] 마지막 행({$this->lastRow})까지 처리 완료 - 프로세스 종료", [
                    'processed_until' => $this->lastRow,
                    'reason' => 'last_row_processing_complete'
                ]);
                
                // 정상 종료 알림 전송
                event(new ReqFinishNotification("입고 등록이 완료되었습니다.", $this->redirect));
                
                // 프로세스를 완전히 중단하기 위해 예외 발생
                throw new Exception("마지막 행({$this->lastRow})까지 처리 완료했습니다. 프로세스를 종료합니다.");
            }
        }

        // 진행률 계산 (마지막 행 정보가 있는 경우)
        if ($this->lastRow !== null) {
            // 전체 데이터 행 수 계산 (시작행부터 마지막행까지 포함)
            // 예: 시작행 3, 마지막행 1500 → 3,4,5,...,1500 = 1498개 행
            $totalRows = $this->lastRow - $this->startRow + 1;
            $processedRows = $chunkOffset - $this->startRow;
            $progressPercentage = $totalRows > 0 ? round(($processedRows / $totalRows) * 100, 1) : 0;

            // 10% 단위로 진행률 로그 출력
            if ($progressPercentage > 0 && $progressPercentage % 10 == 0) {
                // 메모리 사용량 모니터링
                $memoryUsage = memory_get_usage(true);
                $memoryPeak = memory_get_peak_usage(true);

                // 처리 속도 및 예상 완료 시간 계산
                $currentTime = microtime(true);
                $elapsedTime = $currentTime - $this->startTime;
                $rowsPerSecond = $processedRows > 0 ? $processedRows / $elapsedTime : 0;
                $remainingRows = $totalRows - $processedRows;
                $estimatedTime = $rowsPerSecond > 0 ? $remainingRows / $rowsPerSecond : 0;
                $estimatedCompletion = now()->addSeconds($estimatedTime);

                $progressMessage = "📈 진행률: {$progressPercentage}% ({$processedRows}/{$totalRows}행)";
                SimpleLogService::info('req', "[{$this->req->id}] {$progressMessage}", [
                    'memory_current' => $this->formatBytes($memoryUsage),
                    'memory_peak' => $this->formatBytes($memoryPeak),
                    'rows_per_second' => round($rowsPerSecond, 2),
                    'estimated_completion' => $estimatedCompletion->format('H:i:s')
                ]);

                // 텔레그램으로 진행률 전송 (20% 단위로만)
                if ($progressPercentage % 20 == 0) {
                    $telegramMessage = "[{$this->req->id}] {$progressMessage}\n";
                    $telegramMessage .= "💾 메모리: {$this->formatBytes($memoryUsage)} (최대: {$this->formatBytes($memoryPeak)})\n";
                    $telegramMessage .= "⚡ 속도: " . round($rowsPerSecond, 2) . "행/초\n";
                    $telegramMessage .= "⏰ 예상 완료: {$estimatedCompletion->format('H:i:s')}";
//                    $this->telegram->sendMessage($telegramMessage);
                }
            }
        }

        DB::transaction(function () use ($collection, $chunkOffset, $redis) {
            // 현재 청크의 모든 qaid 값 수집 (빈 행 제외)
            $qaids = $collection->map(function ($row, $index) use ($chunkOffset) {
                $currentRowNumber = $chunkOffset + $index;

                try {
                    $item = $this->prepareRow($row->toArray(), $currentRowNumber);
                    return $item['qaid'] ?? null;
                } catch (Exception $e) {
                    // 빈 행인 경우 null 반환하여 필터링
                    SimpleLogService::info('req', "빈 행 감지: {$currentRowNumber}번 행", [
                        'row_data' => $row->toArray(),
                        'error_message' => $e->getMessage()
                    ]);
                    return null;
                }
            })->filter()->toArray();

            // 현재 청크에서 한 번의 쿼리로 이미 존재하는 qaid 확인
            $existingQaids = [];
            if (!empty($qaids)) {
                $existingQaids = Product::whereIn('qaid', $qaids)
                    ->pluck('qaid')
                    ->toArray();
            }

            // 중복 QAID가 있다면
            if (!empty($existingQaids)) {
                // 🔔 중요: 이 부분에서 기존 상품들의 duplicated 상태를 'Y'로 변경
                // 등록 단계에서 기존 상품들까지 Y 로 만들어야 할 이유가 있을까?
                // 등록이 안 되어야 하는게 맞기는 하지만 등록된 후 삭제를 하기도 하는데...
//                Product::whereIn('qaid', $existingQaids)
//                    ->whereNot('status', Product::STATUS_DELETED)
//                    ->where('duplicated', Product::IS_DUPLICATED_N)
//                    ->whereNotIn('req_id', [Req::UNLINKED_ID, Req::GHOST_ID]) // 중복 마킹 제외 대상
//                    ->update(['duplicated' => 'Y']);
                foreach ($collection as $index => $row) {
                    $currentRowNumber = $chunkOffset + $index;

                    try {
                        $item = $this->prepareRow($row->toArray(), $currentRowNumber);

                        if (isset($item['qaid']) && in_array($item['qaid'], $existingQaids)) {
                            $item['req_id'] = $this->req->id;
                            $item['req_at'] = $this->req->req_at;
                            $item['is_duplicated'] = '중복상품';
                            $item['checked_status'] = Product::$STATUS_NAME[Product::STATUS_REGISTERED];
                            $item['created_at'] = $this->req->created_at;
                            $item['user_name'] = $this->user->name;
                            $item['status'] = Product::$CHECK_STATUS_NAME[Product::CHECKED_STATUS_UNCHECKED];
                            $item['is_rg'] = Product::$RG_NAME[$item['rg']];

                            // 중복된 항목 전체 ROW를 Redis에 JSON형식으로 저장
                            $redis->command('rpush', [
                                $this->redisDuplicatedKey,
                                json_encode($item, JSON_UNESCAPED_UNICODE)
                            ]);
                        }
                    } catch (Exception $e) {
                        SimpleLogService::error('req', "중복 처리 중 오류 발생: {$currentRowNumber}번 행", [
                            'row_data' => $row->toArray(),
                            'error_message' => $e->getMessage()
                        ], $e);
                    }
                }

                $message = "🔔 중복 QAID 발견\n".implode("\n", $existingQaids);
//                $this->telegram->sendMessage($message);

                SimpleLogService::info('req', "🔔 중복 QAID 발견", [
                    'qaid' => $existingQaids,
                ]);
            }

            // 각 행 처리 (빈 행 건너뛰기)
            foreach ($collection as $index => $row) {
                $currentRowNumber = $chunkOffset + $index;

                try {
                    $item = $this->prepareRow($row->toArray(), $currentRowNumber);

                    // qaid가 이미 존재하는지 확인하여 duplicated 필드 설정
                    $item['duplicated'] = 'N';
                    if (isset($item['qaid']) && in_array($item['qaid'], $existingQaids)) {
                        $item['duplicated'] = 'Y';
                    }

                    $this->processRow($currentRowNumber, $item);
                } catch (Exception $e) {
                    // 빈 행인 경우 로그만 남기고 건너뛰기
                    SimpleLogService::info('req', "빈 행 건너뛰기: {$currentRowNumber}번 행", [
                        'row_data' => $row->toArray(),
                        'error_message' => $e->getMessage()
                    ]);
                }

                // 현재 행 번호를 Redis에 저장
                $redis->set($this->redisChunkOffsetKey, $currentRowNumber + 1);
            }
        });

        // 청크 처리 후 메모리 정리
        $this->cleanupAfterChunk();
    }

    protected function processRow(int $rowNumber, array $item): void
    {
        try {
            $cate4 = Cate4::firstOrCreate(['name' => $item['cate4']]);
            $cate5 = Cate5::firstOrCreate([
                'cate4_id' => $cate4->id,
                'name' => $item['cate5']
            ]);

            // ProductBarcode 처리: barcode + wms_sku_id 조합으로 unique 처리
            // null 값은 '-'로 처리
            $wmsSkuId = $item['wms_sku_id'] ?? '-';
            $externalSkuId = $item['external_wms_sku_id'] ?? '-';

            $productBarcode = ProductBarcode::firstOrCreate(
                [
                    'barcode' => $item['barcode'],
                    'wms_sku_id' => $wmsSkuId,
                ],
                [
                    'external_wms_sku_id' => $externalSkuId,
                ]
            );

            // ProductLink 조회: external_wms_sku_id + vendor_item_id로 product_link_id 찾기
            $productLink = ProductLink::findByExternalAndVendor(
                $item['external_sku_id'],
                $item['vendor_item_id']
            );
            $lot = ProductLot::firstOrCreate(['name' => $item['lot_full_name']]);
            $vendor = ProductVendor::firstOrCreate(['name' => $item['vendor_name']]);

            // B 카테고리 처리 (b_cate)
            $bCate = null;
            if (!empty($item['b_cate'])) {
                $bCate = ReturnReasonB::firstOrCreate(['name' => $item['b_cate']]);
            }

            // M 카테고리 처리 (m_cate)
            $mCate = null;
            if (!empty($item['m_cate'])) {
                $mCate = returnReasonM::firstOrCreate(['name' => $item['m_cate']]);
            }

            // 입고 목록 상품 등록::QAID는 모두 달라야 하므로 모두 신규상품으로 입력 되어야 정상이다.
            $product = Product::firstOrNew([
                'req_id' => $this->req->id,
                'qaid' => $item['qaid'],
                'barcode' => $item['barcode'],
                'product_lot_id' => $lot->id,
            ]);

            if (isset($product->id)) {
                $logTitle = "{$rowNumber}번 행 [쿠팡PL 상품 재고 업데이트 - {$item['qaid']} ({$product->id})변경: {$product->name}";
            } else {
                $logTitle = "{$rowNumber}번 행 [쿠팡PL 신규상품 - {$item['qaid']}] 등록: {$item['description']}";
            }

            // 중복상품일 경우 처리 ($product->status)
            $status = Product::STATUS_REGISTERED;
            $statusId = null;

            // WorkStatus 데이터를 가져오기 - 검수대기와 중복상품 상태를 모두 가져옴
            $statusIds = WorkStatus::whereIn('link_code', [
                WorkStatus::LINK_INSPECT,     # 검수대기
                WorkStatus::LINK_DUPLICATE    # 중복상품
            ])->pluck('id', 'link_code')->toArray();

            if ($item['duplicated'] === 'Y') {
                $status = Product::STATUS_HELD; # 출고 보류
                $statusId = $statusIds[WorkStatus::LINK_DUPLICATE] ?? null;
            } else {
                $statusId = $statusIds[WorkStatus::LINK_INSPECT] ?? null;
            }

            // statusId가 null인 경우 기본값 설정
            if ($statusId === null) {
                // 기본 검수대기 상태를 찾아서 설정
                $defaultStatus = WorkStatus::where('link_code', WorkStatus::LINK_INSPECT)->first();
                $statusId = $defaultStatus ? $defaultStatus->id : 1; // 기본값으로 1 설정
            }

            $product->fill([
                'product_barcode_id' => $productBarcode?->id,
                'name' => $item['description'],
                'cate4_id' => $cate4->id,
                'cate5_id' => $cate5->id,
                'quantity' => isset($product->id) ? $product->quantity + $item['quantity'] : $item['quantity'],
                'amount' => $item['amount'],
                'user_id' => $this->user->id,
                'status' => $status,
                'rg' => $item['rg'],
                'duplicated' => $item['duplicated'],
                'product_vendor_id' => $vendor->id,
                'product_link_id' => $productLink?->id,
                'checked_status' => Product::CHECKED_STATUS_UNCHECKED,
                'return_reason_b_id' => $bCate?->id,
                'return_reason_m_id' => $mCate?->id,
            ])->save();

            // 반품 사유 처리 (detail_reason)
            if (!empty($item['detail_reason'])) {
                ReturnReason::updateOrCreate(
                    ['product_id' => $product->id],
                    ['reason' => $item['detail_reason']]
                );
            }

            // 상태 변환/통계::입고목록 등록 및 검수대기
            $now = now();
            ProductLog::insert([
                'product_id' => $product->id,
                'model_type' => 'App\Models\Product',
                'model_id' => $product->id,
                'work_status_id' => $statusId,
                'user_id' => $this->user->id,
                'memo' => "입고번호: {$this->req->id}<br>입고일: {$this->req->req_at}<br>$logTitle",
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            SimpleLogService::info('req', "{$rowNumber}번 행 처리 완료", [
                'qaid' => $item['qaid'],
                'description' => $item['description'],
                'duplicated' => $item['duplicated']
            ]);
        } catch (Exception $e) {
            // 실패하는 행만 기록 (상세한 오류 정보 포함)
            SimpleLogService::error('req', "{$rowNumber}번 행 저장 실패", [
                'row_number' => $rowNumber,
                'qaid' => $item['qaid'] ?? 'N/A',
                'description' => $item['description'] ?? 'N/A',
                'row_data' => $item,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ], $e);
        }
    }

    /**
     * @throws Exception
     */
    protected function prepareRow(array $row, ?int $rowNumber = null): array
    {
        // 디버깅: 헤더 매핑 상태 확인
        if (empty($this->columnIndexes)) {
            $rowInfo = $rowNumber ? "{$rowNumber}번 행" : "알 수 없는 행";
            SimpleLogService::error('req', "헤더 매핑이 설정되지 않았습니다 - {$rowInfo}", [
                'row_number' => $rowNumber,
                'row_data' => $row,
                'column_indexes' => $this->columnIndexes
            ]);
            throw new Exception("헤더 매핑이 설정되지 않았습니다. setHeaders() 메서드를 먼저 호출해주세요.");
        }

        $item = [];

        try {
            // 필수 필드들 처리 (excel-headers.php의 required 필드와 일치)
            // WithCalculatedFormulas를 사용하므로 수식이 자동으로 계산된 값으로 변환됨
            $item['qaid'] = $this->checkValue('QAID', $row[$this->columnIndexes['qaid']] ?? null, $rowNumber);
            $item['cate4'] = $this->checkValue('CATE4', $row[$this->columnIndexes['cate4']] ?? null, $rowNumber);
            $item['cate5'] = $this->checkValue('CATE5', $row[$this->columnIndexes['cate5']] ?? null, $rowNumber);
            $item['lot_full_name'] = $this->removeCarriageReturn($row[$this->columnIndexes['lot_full_name']] ?? null);
            $item['wms_sku_id'] = $this->removeCarriageReturn($row[$this->columnIndexes['wms_sku_id']] ?? null);
            $item['external_sku_id'] = $this->removeCarriageReturn($row[$this->columnIndexes['external_sku_id']] ?? null);
            $item['barcode'] = $this->removeCarriageReturn($row[$this->columnIndexes['barcode']] ?? null);
            $item['description'] = $this->checkValue('DESCRIPTION',  $row[$this->columnIndexes['description']] ?? null, $rowNumber);
            $item['vendor_name'] = $this->removeCarriageReturn($row[$this->columnIndexes['vendor_name']] ?? null);

            // 수량과 금액은 숫자 필드이므로 WithCalculatedFormulas로 계산된 값 사용
            $quantity = $row[$this->columnIndexes['quantity']] ?? 1;
            $amount = $row[$this->columnIndexes['amount']] ?? 0;

            // 숫자 값으로 변환 (수식이 계산된 값이므로 안전하게 처리)
            $item['quantity'] = is_numeric($quantity) ? (int)$quantity : 1;
            $item['amount'] = is_numeric($amount) ? (float)$amount : 0;

            // 필수 필드가 비어 있는 경우 예외 발생 (빈 행 처리)
            $requiredFields = ['qaid', 'cate4', 'cate5', 'lot_full_name', 'wms_sku_id', 'external_sku_id', 'barcode', 'description', 'vendor_name', 'quantity', 'amount',];
            $missingFields = [];

            foreach ($requiredFields as $field) {
                if (empty($item[$field])) {
                    $missingFields[] = $field;
                }
            }

            if (!empty($missingFields)) {
                $rowInfo = $rowNumber ? "{$rowNumber}번 행" : "알 수 없는 행";
                SimpleLogService::warning('req', "필수 필드 누락으로 빈 행 처리 - {$rowInfo}", [
                    'row_number' => $rowNumber,
                    'missing_fields' => $missingFields,
                    'item_data' => $item,
                    'row_data' => $row
                ]);

                throw new Exception("빈 행 감지됨 - 누락된 필드: " . implode(', ', $missingFields));
            }

            // 추가 안전장치: 모든 필드가 null이거나 빈 문자열인 경우 빈 행으로 처리
            $allEmpty = true;
            foreach ($item as $key => $value) {
                if (!empty($value)) {
                    $allEmpty = false;
                    break;
                }
            }

            if ($allEmpty) {
                $rowInfo = $rowNumber ? "{$rowNumber}번 행" : "알 수 없는 행";
                SimpleLogService::warning('req', "모든 필드가 비어있는 빈 행 감지 - {$rowInfo}", [
                    'row_number' => $rowNumber,
                    'row_data' => $row
                ]);
                throw new Exception("완전히 빈 행 감지됨");
            }

            // 선택적 필드들 처리 (excel-headers.php의 optional 필드)
            $item['vendor_item_id'] = null;
            $item['product_id'] = null;
            $item['item_id'] = null;
            $item['rg'] = 'N';
            $item['center'] = null;
            $item['b_cate'] = null;
            $item['m_cate'] = null;
            $item['detail_reason'] = null;

            // vendor_item_id가 있는지 확인 (숫자 필드)
            if (isset($this->columnIndexes['vendor_item_id'])) {
                $vendorItemId = $row[$this->columnIndexes['vendor_item_id']] ?? null;
                $item['vendor_item_id'] = is_numeric($vendorItemId) ? (string)$vendorItemId : null;
            }

            // product_id가 있는지 확인 (숫자 필드)
            if (isset($this->columnIndexes['product_id'])) {
                $productId = $row[$this->columnIndexes['product_id']] ?? null;
                $item['product_id'] = is_numeric($productId) ? (string)$productId : null;
            }

            // item_id가 있는지 확인 (숫자 필드)
            if (isset($this->columnIndexes['item_id'])) {
                $itemId = $row[$this->columnIndexes['item_id']] ?? null;
                $item['item_id'] = is_numeric($itemId) ? (string)$itemId : null;
            }

            // rg(구분) 필드가 있는지 확인
            if (isset($this->columnIndexes['rg'])) {
                $rgValue = $this->removeCarriageReturn($row[$this->columnIndexes['rg']] ?? null);
                $item['rg'] = ($rgValue === 'RG' || $rgValue === 'rg') ? 'Y' : 'N';
            }

            // center(센터) 필드가 있는지 확인
            if (isset($this->columnIndexes['center'])) {
                $item['center'] = $this->removeCarriageReturn($row[$this->columnIndexes['center']] ?? null);
            }

            // b_cate 필드가 있는지 확인
            if (isset($this->columnIndexes['b_cate'])) {
                $item['b_cate'] = $this->removeCarriageReturn($row[$this->columnIndexes['b_cate']] ?? null);
            }

            // m_cate 필드가 있는지 확인
            if (isset($this->columnIndexes['m_cate'])) {
                $item['m_cate'] = $this->removeCarriageReturn($row[$this->columnIndexes['m_cate']] ?? null);
            }

            // detail_reason 필드가 있는지 확인
            if (isset($this->columnIndexes['detail_reason'])) {
                $item['detail_reason'] = $this->removeCarriageReturn($row[$this->columnIndexes['detail_reason']] ?? null);
            }

            return $item;
        } catch (Exception $e) {
            $rowInfo = $rowNumber ? "{$rowNumber}번 행" : "알 수 없는 행";
            SimpleLogService::error('req', "행 데이터 준비 중 오류 발생 - {$rowInfo}", [
                'row_number' => $rowNumber,
                'row_data' => $row,
                'error_message' => $e->getMessage()
            ], $e);
            throw $e;
        }
    }

    /**
     * @throws Exception
     */
    private function checkValue($field, $value, ?int $rowNumber = null): string
    {
        // 먼저 캐리지 리턴 제거
        $value = $this->removeCarriageReturn($value);

        // 빈 값 검사
        $this->isEmpty($field, $value, $rowNumber);

        return $value;
    }

    /**
     * 캐리지 리턴 처리
     */
    private function removeCarriageReturn(string | null $string = null): string | null
    {
        if (!$string) {
            return null;
        }
        // UTF-8 인코딩 정리
        $string = mb_convert_encoding($string, 'UTF-8', 'UTF-8');

        // 잘못된 UTF-8 시퀀스 제거
        $string = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $string);

        // 제어 문자 제거
        $string = preg_replace('/[\x{200B}-\x{200D}\x{FEFF}]/u', '', $string);

        // 캐리지 리턴 및 줄바꿈 제거 (trim 사용하지 않음)
        $string = preg_replace('/\r\n|\r|\n/', ' ', $string);

        // 앞뒤 공백만 제거 (trim 대신 ltrim, rtrim 사용)
        $string = trim($string);

        return $string;
    }

    /**
     * @throws Exception
     */
    private function isEmpty($field, $value, ?int $rowNumber = null): void
    {
        // 더 안전한 빈 값 검사: null, 빈 문자열, 공백만 있는 문자열만 빈 값으로 간주
        if ($value === null || $value === '' || (is_string($value) && trim($value) === '')) {
            $rowInfo = $rowNumber ? "{$rowNumber}번 행" : "알 수 없는 행";
            $message = "엑셀 파일의 [{$rowInfo}] [$field] 값이 비어 있습니다. 필터를 이용해 비어 있는 부분을 찾아 값을 채우거나 행을 제거해 주세요.";
//            $this->telegram->sendMessage($message);

            SimpleLogService::error('req', $message, [
                'row_number' => $rowNumber,
                'field' => $field,
                'value' => $value,
            ]);

            throw new Exception($message);
        }
    }
}
