<?php

namespace App\Mails;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AttachmentEmail extends Mailable
{
    use Queueable, SerializesModels;

    // 첨부파일 경로
    protected string $filePath;

    // 메일 내용
    protected array $mailData;

    /**
     * Create a new message instance.
     */
    public function __construct(string $filepath, $mailData)
    {
        $this->filePath = $filepath;
        $this->mailData = $mailData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->mailData['title'],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.attachmentEmail',
            with: $this->mailData
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, Attachment>
     */
    public function attachments(): array
    {
        if (file_exists($this->filePath)) {
            $name = '코너스톤_' . date('Y-m-d') . '_입고요청.xlsx';
            return [
                Attachment::fromPath($this->filePath)
                    ->as($name)
                    ->withMime('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            ];
        }

        return [];
    }
}
