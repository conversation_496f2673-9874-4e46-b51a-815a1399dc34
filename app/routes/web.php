<?php

use App\Http\Controllers\Api\RepairController;
use App\Http\Controllers\Api\ResourceController;
use App\Http\Controllers\Outsourcing\OutsourcingController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\WarehousePalletController;
use App\Http\Controllers\WarehousePalletItemController;
use App\Http\Controllers\WMS\Admin\ProductLockController;
use App\Http\Controllers\WMS\AuthController;
use App\Http\Controllers\WMS\CarryoutController;
use App\Http\Controllers\WMS\CarryoutProductController;
use App\Http\Controllers\WMS\ExcelDownloadController;
use App\Http\Controllers\WMS\FaqController;
use App\Http\Controllers\WMS\InspectionController;
use App\Http\Controllers\WMS\LocationController;
use App\Http\Controllers\WMS\NoticeController;
use App\Http\Controllers\WMS\PalletController;
use App\Http\Controllers\WMS\PalletLoadedController;
use App\Http\Controllers\WMS\PalletProductController;
use App\Http\Controllers\WMS\Products\ProductController;
use App\Http\Controllers\WMS\Products\UnlinkedController;
use App\Http\Controllers\WMS\QaidController;
use App\Http\Controllers\WMS\ReqController;
use App\Http\Controllers\WMS\UserWorkHistoryController;
use App\Http\Controllers\WMS\VidController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return 'Welcome!';
});

// Route::get('password', function () {
//     return Hash::make('jcy810326@');
// });

Route::name('test.')->prefix('test')->group(function () {
    Route::get('/brands', [TestController::class, 'brands'])->name('brands');
    Route::get('/product-name', [TestController::class, 'productName'])->name('product-name');
});

/**
 * 클라이언트에서 데이터 전체를 요청하는 경우 사용
 */
Route::name('resource.')->prefix('resources')->group(function () {
    Route::get('/precesses', [ResourceController::class, 'getProcessTypes'])->name('process');
    Route::get('/statuses', [ResourceController::class, 'getStatuses'])->name('status');
});

/**
 * cnsproWMS에서 사용되는 라우트
 */
Route::middleware('logger')->name('wms.')->prefix('wms')->group(function () {
    Route::post('login', [AuthController::class, 'login'])->name('login');
    Route::post('logout', [AuthController::class, 'logout'])->name('logout');

    Route::middleware('auth:sanctum')->group(function () {
        Route::name('user.')->prefix('user')->group(function () {
            Route::get('/', [AuthController::class, 'me'])->name('me');
            Route::put('/profile', [AuthController::class, 'changeMyInfo'])->name('change.profile');
            Route::put('/password', [AuthController::class, 'changeMyPassword'])->name('change.password');

            // 나의 작업 이력
            Route::prefix('work-history')->name('work-history.')->group(function () {
                // 오늘 작업 내역 조회
                Route::get('/today', [UserWorkHistoryController::class, 'getTodayWorkHistory'])
                    ->name('today');

                // 기간별 작업 내역 조회
                Route::get('/by-date-range', [UserWorkHistoryController::class, 'getWorkHistoryByDateRange'])
                    ->name('by-date-range');

                // 작업 통계 조회
                Route::get('/statistics', [UserWorkHistoryController::class, 'getWorkStatistics'])
                    ->name('statistics');
            });
        });

        // 점검 요청 라우터
        Route::name('req.')->prefix('reqs')->group(function () {
            // admin.access 미들웨어가 필요한 라우트들을 그룹화
            Route::middleware('admin.access')->group(function () {
                Route::post('/', [ReqController::class, 'store'])->name('store');
                Route::post('/{id}', [ReqController::class, 'update'])
                    ->where('id', '[0-9]+')
                    ->name('update');
                Route::delete('/{id}', [ReqController::class, 'destroy'])
                    ->where('id', '[0-9]+')
                    ->name('destroy');

                // Vid.xlsx 업로드 관련 라우트
                Route::prefix('vid')->group(function () {
                    Route::post('/upload', [VidController::class, 'upload'])->name('vid.upload');
                    Route::get('/statistics', [VidController::class, 'statistics'])->name('vid.statistics');
                });
            });

            // 일반 사용자도 접근 가능한 라우트
            Route::get('/', [ReqController::class, 'reqList'])->name('list');
            Route::get('/unchecked', [ReqController::class, 'uncheckedList'])->name('unchecked');
            Route::get('/update-count/{id}', [ReqController::class, 'updateReqCount'])
                ->where('id', '[0-9]+')
                ->name('get.count');
            Route::get('/{id}', [ReqController::class, 'show'])
                ->where('id', '[0-9]+')
                ->name('show');
        });

        // 입고 상품 라우터
        Route::name('product.')->prefix('products')->group(function () {
            // (직원), (외주업체) 검색 상품 리스트
            Route::get('/search', [ProductController::class, 'searchProducts'])->name('search.list');

            // 상품 목록 - 작업목록, RG 상품 목록
            Route::get('/tasks', [ProductController::class, 'list'])->name('task.list');

            // 상품 목록 - 중복 상품
            Route::get('/duplicates', [ProductController::class, 'duplicateList'])->name('duplicate.list');

            // 상품 목록 - 미등록 상품
            Route::name('unlinked.')->prefix('unlinked')->group(function () {
                Route::get('/', [UnlinkedController::class, 'list'])->name('list');
                Route::post('/', [UnlinkedController::class, 'store'])->name('store');
                Route::put('/', [UnlinkedController::class, 'update'])->name('update');
            });

            // 상품 삭제
            Route::post('/destroy', [ProductController::class, 'destroyProducts'])->name('destroy');

            // 상품 상세정보(id)
            Route::get('/id/{id}', [ProductController::class, 'showProductById'])->name('show.id');

            // 상품 상세정보(QAID)
            Route::get('/qaid/{qaid}', [ProductController::class, 'showProductByQaid'])->name('show.qaid');
        });

        // 입고 검수
        Route::name('inspection.')->prefix('inspections')->group(function () {
            // 상품 리스트
            Route::get('/', [InspectionController::class, 'productList'])->name('list');
            // 검수 완료(개별)
            Route::patch('/pass', [InspectionController::class, 'inspectionPass'])->name('pass');
            // 검수 완료(일괄)
            Route::put('/complete/{id}', [InspectionController::class, 'checkUndelivered'])
                ->where('id', '[0-9]+')
                ->name('complete');
        });

        // 팔레트(창고: warehouse)
        Route::name('warehouse.')->prefix('warehouse')->group(function () {
            // 입고 상품 적재
            Route::get('/loading', [WarehousePalletController::class, 'loading'])->name('loading');

            // 팔레트 리스트
            Route::get('/pallets', [WarehousePalletController::class, 'list'])->name('pallet.list');

            // 상품 조회
            Route::get('/check-product/{qaid}', [WarehousePalletItemController::class, 'checkProduct'])->name('check.product');

            // 팔레트 생성
            Route::get('/pallets/generate', [WarehousePalletController::class, 'generatePalletNumber'])->name('pallet.generate');
            // 팔레트 저장
            Route::post('/pallets/save', [WarehousePalletController::class, 'store'])->name('pallet.store');

            // 팔레트 상품 리스트
            Route::get('/pallet/items', [WarehousePalletItemController::class, 'list'])->name('pallet.item.list');
            // 팔레트에 상품 저장
            Route::post('/pallet/items/save', [WarehousePalletItemController::class, 'storeItem'])->name('pallet.item.store');
        });

        // 수리/점검
        Route::name('repair.')->prefix('repairs')->group(function () {
            // 수리/점검 내용 기록하기
            Route::post('/store', [RepairController::class, 'store'])->name('store');
            // 구성품 카테고리 및 구성품 리스트
            Route::get('/parts-categories', [RepairController::class, 'getPartsCategories'])->name('parts.categories');
            Route::get('/parts/{category_id?}', [RepairController::class, 'getParts'])->name('parts');
            // QAID로 수리/점검 상품 조회
            Route::get('/check-product/{qaid}', [RepairController::class, 'checkProduct'])->name('check.product');

            Route::get('/code/symptoms/{req_type}', [RepairController::class, 'getSymptom'])->name('symptom.code');
            Route::get('/code/processes/symptom/{id}', [RepairController::class, 'getProcess'])->name('process.code');
            Route::get('/code/grade/process/{id}', [RepairController::class, 'getGrade'])->name('process.code');

            // 구성품 리스트
        });

        // Location
        Route::name('location.')->prefix('locations')->group(function () {
            Route::get('/generate/code/{place}', [LocationController::class, 'generateCode'])->name('generate.code');
        });

        // Pallet
        Route::name('pallet.')->prefix('pallets')->group(function () {
            Route::get('/list', [PalletController::class, 'list'])->name('list');
            Route::post('/exports', [PalletController::class, 'exportPallets'])->name('export');
            Route::put('/rollback-exports', [PalletController::class, 'rollbackExportPallets'])->name('rollback');
            Route::put('/save-export-date', [PalletController::class, 'saveExportDate'])->name('export.date');

            // 팔레트 상품
            Route::name('product.')->prefix('products')->group(function () {
                Route::get('/', [PalletProductController::class, 'list'])->name('product.list');
                Route::patch('/', [PalletProductController::class, 'deliveryInspection'])->name('delivery.inspection'); # 출고검수
                Route::put('/close', [PalletProductController::class, 'closePallet'])->name('close.pallet'); # 팔레트 마감
                Route::put('/open', [PalletProductController::class, 'openPallet'])->name('open.pallet'); # 팔레트 마감 취소(재오픈)
                Route::patch('/exclude-from-pallet', [PalletProductController::class, 'excludeFromPallet'])->name('exclude.pallet'); # 점검 취소(다시 검수완료로 보냄)
            });

            // 팔레트에 상품 적재
            Route::name('loaded.')->prefix('loaded')->group(function () {
                Route::get('/', [PalletLoadedController::class, 'getLoadedList'])->name('loaded');
                Route::get('/set/{place}/{code}', [PalletLoadedController::class, 'setLocationCode'])->name('set');
                Route::get('/check-product/{qaid}', [PalletLoadedController::class, 'checkProduct'])->name('check.product');
                Route::get('/check-invoice/{id}/{repair_type}', [PalletLoadedController::class, 'checkInvoice'])->name('check.invoice');
                Route::get('/other-expenses/{type}/{code}', [PalletLoadedController::class, 'otherExpenses'])->name('other.expenses');
                Route::post('/save-on-pallet', [PalletLoadedController::class, 'store'])->name('store');
            });
        });

        // Carryout
        Route::name('carryout.')->prefix('carryout')->group(function () {
            Route::get('/', [CarryoutController::class, 'list'])->name('list');
            Route::post('/', [CarryoutController::class, 'store'])->name('store');
            Route::put('/export', [CarryoutController::class, 'export'])->name('export');
            Route::put('/import', [CarryoutController::class, 'import'])->name('import');
            Route::delete('/destroy/{id}', [CarryoutController::class, 'destroy'])->name('destroy');

            Route::name('product.')->prefix('products')->group(function () {
                Route::get('/', [CarryoutProductController::class, 'list'])->name('product.list');
                Route::patch('/export', [CarryoutProductController::class, 'export'])->name('product.export');
                Route::patch('/import', [CarryoutProductController::class, 'import'])->name('product.import');
                Route::delete('/destroy', [CarryoutProductController::class, 'destroy'])->name('product.destroy');
            });
        });

        // 게시판
        Route::name('board.')->prefix('board')->group(function () {
            Route::get('/', [NoticeController::class, 'index'])->name('index');

            Route::middleware('admin.access')->group(function () {
                Route::post('/', [NoticeController::class, 'store'])->name('store');
                Route::put('/{id}', [NoticeController::class, 'update'])->name('update');
                Route::delete('/{id}', [NoticeController::class, 'delete'])->name('delete');
            });

            Route::get('/{id}', [NoticeController::class, 'show'])->name('show');
        });

        // faq
        Route::name('faq.')->prefix('faqs')->group(function () {
            Route::get('/', [FaqController::class, 'index'])->name('index');

            Route::middleware('admin.access')->group(function () {
                Route::post('/', [FaqController::class, 'store'])->name('store');
                Route::put('/{id}', [FaqController::class, 'update'])->name('update');
                Route::delete('/{id}', [FaqController::class, 'delete'])->name('delete');
            });

            Route::get('/{id}', [FaqController::class, 'show'])->name('show');
        });

        // QAID 재발행 관련
        Route::name('qaid.')->prefix('qaids')->group(function () {
            Route::get('/', [QaidController::class, 'list'])->name('list');
            // 존재하는지 확인하고 없으면 저장, 있으면(중복이면) 경고
            Route::get('/{qaid}/{id}', [QaidController::class, 'existsQaid'])
                ->where(['qaid' => 'Q[0-9]+', 'id' => '[0-9]+'])
                ->name('exists');
            Route::put('/{qaid}/{id}', [QaidController::class, 'rePrintQaid'])
                ->where(['qaid' => 'Q[0-9]+', 'id' => '[0-9]+'])
                ->name('reprint');
            Route::get('/history/{id}', [QaidController::class, 'getHistory'])->name('history');
        });

        // 파일 다운로드
        Route::name('download.')->prefix('download')->group(function () {
            // 엑셀로 다운로드
            Route::name('excel.')->prefix('excel')->group(function () {
                // 통합 엑셀 다운로드 엔드포인트
                Route::post('/', [ExcelDownloadController::class, 'downloadAsExcel'])->name('download');

                // 사용 가능한 Export 타입 목록 조회
                Route::get('/types', [ExcelDownloadController::class, 'getAvailableExports'])->name('types');
            });
        });

        // 관리자 전용
        Route::name('admin.product-lock.')->prefix('admin/product-lock')->middleware('admin.access')->group(function () {
            Route::post('/lock', [ProductLockController::class, 'lock'])->name('lock');
            Route::post('/unlock', [ProductLockController::class, 'unlock'])->name('unlock');
            Route::get('/locked-products', [ProductLockController::class, 'lockedProducts'])->name('locked-products');
        });

        // 아직 사용하지 않는 라우터
        // @todo: 외주업체(작업 전)
        Route::name('outsourcing.')->prefix('outsourcing')->group(function () {
            Route::post('/login', [OutsourcingController::class, 'login'])->name('login');
            Route::get('/repair/{id}', [OutsourcingController::class, 'getList'])->name('list');
            Route::get('/product/{qaid}', [OutsourcingController::class, 'checkProduct'])->name('check.product');
        });
    });
});
