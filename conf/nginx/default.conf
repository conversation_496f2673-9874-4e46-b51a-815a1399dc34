server {
    listen 80;
    #server_name csp-api.freshlove.net;
    server_name cnsprowms-api.test;
    
    root /var/www/html/public;
    index index.php index.html;

    charset UTF-8;

    access_log /var/log/nginx/access.log main;
    error_log  /var/log/nginx/error.log warn;

    proxy_buffers            32 4m;
    proxy_busy_buffers_size  25m;
    proxy_buffer_size        512k;
    proxy_ignore_headers "Cache-Control" "Expires";
    proxy_max_temp_file_size 0;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    # proxy_connect_timeout 300;
    # proxy_send_timeout 300;
    proxy_read_timeout 300;
    proxy_intercept_errors off;

    client_max_body_size     50m;
    client_body_buffer_size  4m;
    client_body_temp_path    /tmp;

    if (!-f $request_filename) {
        rewrite ^(.*)$ /index.php break;
    }
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass cnsprowms-api:9000;
        fastcgi_index index.php;
        
        try_files $uri = 404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;

        # fastcgi_connect_timeout 300; # default 60
        # fastcgi_send_timeout 300; # default 60
        fastcgi_read_timeout 300;
        fastcgi_buffers 64 16k; # default 8 4k
        # fastcgi_buffer_size 32k; # upstream 오류가 발생할 때 사용
        
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    # error_page 403 /403.html;
    # error_page 404 /404.html;
    # error_page 405 /405.html;

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    location ~ /\. {
        deny  all;
    }

    location ~* /(?:uploads|files|data|tmp)/.*\.php$ {
        deny all;
    }

    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }

    location = /robots.txt {
        allow all;
        log_not_found off;
        access_log off;
    }

    location ~* ^.+\.(ogg|ogv|svg|svgz|eot|otf|woff|mp4|ttf|rss|atom|jpg|jpeg|gif|png|ico|zip|tgz|gz|rar|bz2|doc|xls|xlsx|exe|ppt|tar|mid|midi|wav|bmp|rtf)$ {
        access_log off;
        log_not_found off;
        expires max;
    }
}